// 在浏览器控制台运行此脚本来mock测试数据

// 打开数据库并添加测试数据
async function mockFailedUploads() {
  try {
    // 打开数据库
    const request = indexedDB.open("witness_client_db", 2);

    return new Promise((resolve, reject) => {
      request.onerror = () => reject(request.error);
      request.onsuccess = () => {
        const db = request.result;

        // 创建测试数据
        const mockData = [
          {
            roomId: "room_001",
            fileName: "2024-01-15_14-30-25-123_1u1_57.mp4",
            failureReason: "网络连接超时",
            timestamp: new Date().toISOString(),
            retryCount: 0,
          },
          {
            roomId: "room_002",
            fileName: "2024-01-15_15-45-10-456_2u2_89.mp4",
            failureReason: "服务器内部错误",
            timestamp: new Date(Date.now() - 3600000).toISOString(), // 1小时前
            retryCount: 1,
          },
          {
            roomId: "room_003",
            fileName: "2024-01-15_16-20-33-789_3u3_12.mp4",
            failureReason: "文件格式不支持",
            timestamp: new Date(Date.now() - 7200000).toISOString(), // 2小时前
            retryCount: 2,
          },
          {
            roomId: "room_001",
            fileName: "2024-01-15_14-35-18-999_1u1_98.mp4",
            failureReason: "磁盘空间不足",
            timestamp: new Date(Date.now() - 1800000).toISOString(), // 30分钟前
            retryCount: 0,
          },
        ];

        // 添加数据到数据库
        const transaction = db.transaction(["failed_uploads"], "readwrite");
        const objectStore = transaction.objectStore("failed_uploads");

        let addedCount = 0;
        mockData.forEach((data) => {
          const addRequest = objectStore.add(data);
          addRequest.onsuccess = () => {
            addedCount++;
            console.log(`✅ 添加测试数据成功: ${data.fileName}`);
          };
          addRequest.onerror = () => {
            console.error(
              `❌ 添加测试数据失败: ${data.fileName}`,
              addRequest.error
            );
          };
        });

        transaction.oncomplete = () => {
          console.log(`🎉 成功添加 ${addedCount} 条测试数据到IndexedDB`);
          db.close();
          resolve(addedCount);
        };

        transaction.onerror = () => {
          console.error("❌ 事务执行失败:", transaction.error);
          db.close();
          reject(transaction.error);
        };
      };

      // 处理数据库升级（如果需要）
      request.onupgradeneeded = (event) => {
        const db = event.target.result;

        // 创建failed_uploads表
        if (!db.objectStoreNames.contains("failed_uploads")) {
          const objectStore = db.createObjectStore("failed_uploads", {
            keyPath: "id",
            autoIncrement: true,
          });

          // 创建索引
          objectStore.createIndex("by_roomId", "roomId", { unique: false });
          objectStore.createIndex("by_timestamp", "timestamp", {
            unique: false,
          });

          console.log("📊 创建failed_uploads表和索引");
        }
      };
    });
  } catch (error) {
    console.error("❌ Mock数据失败:", error);
    throw error;
  }
}

// 查看所有数据
async function viewAllFailedUploads() {
  try {
    const request = indexedDB.open("witness_client_db", 1);

    return new Promise((resolve, reject) => {
      request.onerror = () => reject(request.error);
      request.onsuccess = () => {
        const db = request.result;
        const transaction = db.transaction(["failed_uploads"], "readonly");
        const objectStore = transaction.objectStore("failed_uploads");
        const getAllRequest = objectStore.getAll();

        getAllRequest.onsuccess = () => {
          const data = getAllRequest.result;
          console.log("📋 当前所有失败上传记录:", data);
          db.close();
          resolve(data);
        };

        getAllRequest.onerror = () => {
          console.error("❌ 查询数据失败:", getAllRequest.error);
          db.close();
          reject(getAllRequest.error);
        };
      };
    });
  } catch (error) {
    console.error("❌ 查看数据失败:", error);
    throw error;
  }
}

// 添加待上传测试数据
async function mockPendingUploads() {
  try {
    // 打开数据库
    const request = indexedDB.open("witness_client_db", 2);

    return new Promise((resolve, reject) => {
      request.onerror = () => reject(request.error);
      request.onsuccess = () => {
        const db = request.result;

        // 创建测试数据
        const mockData = [
          {
            roomId: "room_004",
            fileName: "2024-01-15_17-10-45-111_4u4_25.mp4",
            filePath: "D:\\anychat\\video\\2024-01-15\\17-10-45-111_4u4_25.mp4",
            fileSize: 1024000, // 1MB
            timestamp: new Date().toISOString(),
            uploadStatus: "pending",
          },
          {
            roomId: "room_005",
            fileName: "2024-01-15_17-25-30-222_5u5_67.mp4",
            filePath: "D:\\anychat\\video\\2024-01-15\\17-25-30-222_5u5_67.mp4",
            fileSize: 2048000, // 2MB
            timestamp: new Date(Date.now() - 1800000).toISOString(), // 30分钟前
            uploadStatus: "pending",
          },
          {
            roomId: "room_006",
            fileName: "2024-01-15_17-40-15-333_6u6_89.mp4",
            filePath: "D:\\anychat\\video\\2024-01-15\\17-40-15-333_6u6_89.mp4",
            fileSize: 3072000, // 3MB
            timestamp: new Date(Date.now() - 3600000).toISOString(), // 1小时前
            uploadStatus: "pending",
          },
          {
            roomId: "room_004",
            fileName: "2024-01-15_17-15-20-444_4u4_34.mp4",
            filePath: "D:\\anychat\\video\\2024-01-15\\17-15-20-444_4u4_34.mp4",
            fileSize: 512000, // 512KB
            timestamp: new Date(Date.now() - 900000).toISOString(), // 15分钟前
            uploadStatus: "pending",
          },
          {
            roomId: "room_007",
            fileName: "2024-01-15_17-50-05-555_7u7_12.mp4",
            filePath: "D:\\anychat\\video\\2024-01-15\\17-50-05-555_7u7_12.mp4",
            fileSize: 4096000, // 4MB
            timestamp: new Date(Date.now() - 7200000).toISOString(), // 2小时前
            uploadStatus: "pending",
          },
        ];

        // 添加数据到数据库
        const transaction = db.transaction(["pending_uploads"], "readwrite");
        const objectStore = transaction.objectStore("pending_uploads");

        let addedCount = 0;
        mockData.forEach((data) => {
          const addRequest = objectStore.add(data);
          addRequest.onsuccess = () => {
            addedCount++;
            console.log(`✅ 添加待上传测试数据成功: ${data.fileName}`);
          };
          addRequest.onerror = () => {
            console.error(
              `❌ 添加待上传测试数据失败: ${data.fileName}`,
              addRequest.error
            );
          };
        });

        transaction.oncomplete = () => {
          console.log(`🎉 成功添加 ${addedCount} 条待上传测试数据到IndexedDB`);
          db.close();
          resolve(addedCount);
        };

        transaction.onerror = () => {
          console.error("❌ 事务执行失败:", transaction.error);
          db.close();
          reject(transaction.error);
        };
      };

      // 处理数据库升级（如果需要）
      request.onupgradeneeded = (event) => {
        const db = event.target.result;

        // 创建pending_uploads表
        if (!db.objectStoreNames.contains("pending_uploads")) {
          const objectStore = db.createObjectStore("pending_uploads", {
            keyPath: "id",
            autoIncrement: true,
          });

          // 创建索引
          objectStore.createIndex("by_roomId", "roomId", { unique: false });
          objectStore.createIndex("by_status", "uploadStatus", {
            unique: false,
          });
          objectStore.createIndex("by_timestamp", "timestamp", {
            unique: false,
          });

          console.log("📊 创建pending_uploads表和索引");
        }
      };
    });
  } catch (error) {
    console.error("❌ Mock待上传数据失败:", error);
    throw error;
  }
}

// 查看待上传数据
async function viewAllPendingUploads() {
  try {
    const request = indexedDB.open("witness_client_db", 2);

    return new Promise((resolve, reject) => {
      request.onerror = () => reject(request.error);
      request.onsuccess = () => {
        const db = request.result;
        const transaction = db.transaction(["pending_uploads"], "readonly");
        const objectStore = transaction.objectStore("pending_uploads");
        const getAllRequest = objectStore.getAll();

        getAllRequest.onsuccess = () => {
          const data = getAllRequest.result;
          console.log("📋 当前所有待上传记录:", data);
          db.close();
          resolve(data);
        };

        getAllRequest.onerror = () => {
          console.error("❌ 查询待上传数据失败:", getAllRequest.error);
          db.close();
          reject(getAllRequest.error);
        };
      };
    });
  } catch (error) {
    console.error("❌ 查看待上传数据失败:", error);
    throw error;
  }
}

// 清空待上传数据
async function clearAllPendingUploads() {
  try {
    const request = indexedDB.open("witness_client_db", 2);

    return new Promise((resolve, reject) => {
      request.onerror = () => reject(request.error);
      request.onsuccess = () => {
        const db = request.result;
        const transaction = db.transaction(["pending_uploads"], "readwrite");
        const objectStore = transaction.objectStore("pending_uploads");
        const clearRequest = objectStore.clear();

        clearRequest.onsuccess = () => {
          console.log("🗑️ 已清空所有待上传记录");
          db.close();
          resolve();
        };

        clearRequest.onerror = () => {
          console.error("❌ 清空待上传数据失败:", clearRequest.error);
          db.close();
          reject(clearRequest.error);
        };
      };
    });
  } catch (error) {
    console.error("❌ 清空待上传数据失败:", error);
    throw error;
  }
}

// 清空所有数据
async function clearAllFailedUploads() {
  try {
    const request = indexedDB.open("witness_client_db", 2);

    return new Promise((resolve, reject) => {
      request.onerror = () => reject(request.error);
      request.onsuccess = () => {
        const db = request.result;
        const transaction = db.transaction(["failed_uploads"], "readwrite");
        const objectStore = transaction.objectStore("failed_uploads");
        const clearRequest = objectStore.clear();

        clearRequest.onsuccess = () => {
          console.log("🗑️ 已清空所有失败上传记录");
          db.close();
          resolve();
        };

        clearRequest.onerror = () => {
          console.error("❌ 清空数据失败:", clearRequest.error);
          db.close();
          reject(clearRequest.error);
        };
      };
    });
  } catch (error) {
    console.error("❌ 清空数据失败:", error);
    throw error;
  }
}

// 使用示例：
console.log("🚀 IndexedDB 测试数据工具已加载");
console.log("可用命令：");
console.log("- mockFailedUploads(): 添加失败上传测试数据");
console.log("- viewAllFailedUploads(): 查看所有失败上传数据");
console.log("- clearAllFailedUploads(): 清空所有失败上传数据");
console.log("- mockPendingUploads(): 添加待上传测试数据");
console.log("- viewAllPendingUploads(): 查看所有待上传数据");
console.log("- clearAllPendingUploads(): 清空所有待上传数据");

// 直接运行添加测试数据
// mockFailedUploads().then(() => {
//   return mockPendingUploads();
// }).then(() => {
//   return viewAllFailedUploads();
// }).then(() => {
//   return viewAllPendingUploads();
// }).catch(console.error);
