import { join } from "https://deno.land/std/path/mod.ts";

// 根据操作系统获取正确的路径分隔符
// const isWindows = Deno.build.os === "windows";

// 配置信息
const config = {
  local: {
    distPath: join(".", "dist"),
  },
  remote: {
    directory: "/home/<USER>/shangzheng-wintess-server/client",
    host: "*************",
    username: "root",
  },
};

// 添加命令行参数解析
const args = Deno.args;
const shouldInstallDeps = args.includes("--i");
const shouldBuild = args.includes("--b");

async function execCommand(commandStr: string): Promise<string> {
  console.log(`执行命令: ${commandStr}`);

  // 如果是SSH命令并包含多个操作，特殊处理
  if (commandStr.includes("ssh") && commandStr.includes(" && ")) {
    // 对SSH命令进行修改，使用分号代替&&，这在某些SSH连接中更稳定
    const parts = commandStr.split(" ssh ");
    if (parts.length > 1) {
      const sshPart = parts[0] + " ssh ";
      const commandPart = parts[1].replace(/ && /g, "; ");
      commandStr = sshPart + commandPart;
      console.log(`修改后的SSH命令: ${commandStr}`);
    }
  }

  const [cmd, ...args] = commandStr.split(" ");
  const command = new Deno.Command(cmd, {
    args,
    // 添加 cwd 参数，指定工作目录
    cwd: new URL("..", import.meta.url).pathname, // 获取脚本所在目录的上级目录
    stderr: "piped",
    stdout: "piped",
  });

  try {
    // 使用 output() 方法代替 spawn() + 手动管理流
    // 这可以简化代码并减少出错的可能性
    const { code, stdout, stderr } = await command.output();

    // 解码输出
    const decoder = new TextDecoder();
    const stdoutText = decoder.decode(stdout);
    const stderrText = decoder.decode(stderr);

    // 输出结果
    if (stdoutText) console.log(stdoutText);
    if (stderrText) console.error(stderrText);

    if (code !== 0) {
      throw new Error(`命令执行失败 (退出码 ${code}): ${commandStr}`);
    }

    return "命令执行完成";
  } catch (error: unknown) {
    console.error(`执行命令出错: ${(error as Error).message}`);
    throw new Error(`命令执行失败: ${commandStr}`);
  }
}

async function buildProject() {
  if (shouldInstallDeps) {
    console.log("开始安装依赖...");
    await execCommand("pnpm install --no-frozen-lockfile");
  } else {
    console.log("跳过依赖安装...");
  }

  if (shouldBuild) {
    console.log("开始构建项目...");
    await execCommand("pnpm run build");
  } else {
    console.log("跳过构建...");
  }
}

async function deployToRemote() {
  const { directory, host, username } = config.remote;
  const ARCHIVE_NAME = "dist.tar.gz";

  // 打印当前工作目录，用于调试
  console.log("当前工作目录:", Deno.cwd());

  try {
    // 1. 压缩dist目录
    console.log("开始压缩dist目录...");
    await execCommand(`tar -czf ${ARCHIVE_NAME} -C ${config.local.distPath} .`);

    // 2. 清理远程目录并创建新目录
    console.log("清理远程目录...");
    const cleanCommand = `sshpass -p Apexsoft!@#3214 ssh ${username}@${host} rm -rf ${directory} ; mkdir -p ${directory}`;
    await execCommand(cleanCommand);

    // 3. 上传压缩文件
    console.log("上传压缩文件...");
    const scpCommand = `sshpass -p Apexsoft!@#3214 scp ${ARCHIVE_NAME} ${username}@${host}:${directory}`;
    await execCommand(scpCommand);

    // 4. 在远程解压文件
    console.log("远程解压文件...");
    // 使用分号连接远程命令，这在某些SSH环境中更稳定
    const unzipCommand = `sshpass -p Apexsoft!@#3214 ssh ${username}@${host} cd ${directory}; tar -xzf ${ARCHIVE_NAME}; rm ${ARCHIVE_NAME}`;
    await execCommand(unzipCommand);

    // 5. 清理本地压缩文件
    console.log("清理本地压缩文件...");
    await execCommand(`rm -f ${ARCHIVE_NAME}`);

    console.log("部署完成！");
  } catch (error) {
    console.error("部署失败:", error);
    // 打印更详细的错误信息
    if (error instanceof Error) {
      console.error("错误详情:", error.stack);
    }
    // 清理本地压缩文件（即使发生错误也尝试清理）
    await execCommand(`rm -f ${ARCHIVE_NAME}`);
  }
}

async function main() {
  try {
    // 1. 构建项目
    await buildProject();

    // 2. 部署到远程
    await deployToRemote();

    console.log("所有任务完成！");
  } catch (error) {
    console.error("执行失败:", error);
    Deno.exit(1);
  }
}

// 运行脚本
if (import.meta.main) {
  main();
}
