import { defineMock } from "./base";

export default defineMock([
  {
    url: "menus/routes",
    method: ["GET"],
    body: {
      code: 1,
      data: [
        {
          path: "/remote-witness",
          component: "RemoteWitnessLayout",
          name: "/remote-witness",
          meta: {
            title: "远程见证",
            icon: "document",
            hidden: true,
            alwaysShow: false,
            params: null,
          },
          children: [
            {
              path: "user",
              component: "remote-witness/user",
              name: "remoteWitness-user",
              meta: {
                title: "座席端",
                icon: "document",
                hidden: true,
                alwaysShow: false,
                params: null,
              },
            },
          ],
        },
        {
          path: "/remote-witness/customer",
          component: "remote-witness/customer",
          name: "remoteWitness-customer",
          meta: {
            title: "客户端",
            icon: "document",
            hidden: true,
            alwaysShow: false,
            params: null,
          },
        },
      ],
      msg: "一切ok",
    },
  },
]);
