export const SECRET = "AIDEMO";
export enum Status {
  Idle = "idle", // 空闲等待
  Connecting = "connecting", // 正在连接视频
  Connected = "Connected", // 链接成功
  Recording = "recording", // 录制中
  RecordEnd = "recordEnd", // 录制结束
}

export const HSNR = [
  {
    nr: '<speak>尊敬的投资者：\n       我是characters 中信证券股份有限公司 北京安外大街证券营业部员工 受理<say-as interpret-as="characters">sl001</say-as>，工号 <say-as interpret-as="characters">sl001</say-as>。<say-as interpret-as="address">深南大道1000号103-3</say-as>在您/贵机构申请开通深圳报价回购业务（天天利财）权限前，按照监管及公司要求，本公司需向您/贵机构进行风险告知。本公司将对风险告知的全过程进行录音录像，请您理解并配合。\n       首先需要核实您/贵机构的身份信息：您/贵机构的姓名/名称是<say-as interpret-as="digits">客户名称</say-as>，证件类型是身份证，证件号码是<say-as interpret-as="id">110103198101015348</say-as><break time="1000ms"/>。请您确认是否准确无误？</speak>',
    pagetime: "10",
    ypbffs: "4",
    khhd: "是|确认|准确|无误|准确无误|对|没错",
    zxyq: "",
  },
  {
    mp3: {
      filepath: "MjAyNC8xMS80LzE3MzA2OTgzNDQ3MzEyODQ=",
      time: 270,
      autoplay: 1,
      url: "/slxt/image/downloadRes?token=1734339779125393970433SwvD15i2820PxAv2Vkw17Sps6GZo21Yju33Id8vrQy120ZgOW34Cm28fR9z34CRa12cOc271114gUf3c27SQ30b30rYz34DnirGp21C15PJ2232BCKcauU20Pl10",
      md5: "",
    },
    nr: "请问您是xxx先生/女士本人吗?",
    pagetime: "10",
    ypbffs: "2",
    khhd: "无需",
    zxyq: "",
  },
  {
    nr: "请问您是本人自愿申请开户申请吗?",
    pagetime: "10",
    ypbffs: "4",
    khhd: "是|确认|确认知悉|知悉|清楚|知道",
    zxyq: "0",
  },
  {
    nr: "感谢您的配合,本次见证结束,祝您投资顺利!",
    pagetime: "10",
    ypbffs: "4",
    khhd: "无需",
    zxyq: "",
  },
];
