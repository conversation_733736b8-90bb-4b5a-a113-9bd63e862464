<!--<template>-->
<!--  <div class="tts-player w-full h-full"></div>-->
<!--</template>-->

<!--<script setup lang="ts">-->
<!--import { ref, onMounted, onBeforeUnmount } from "vue";-->
<!--import { useTTSAudioPlayer } from "@/components/remoteWitness/useTTSAudioPlayer";-->

<!--// 暴露方法给父组件-->
<!--defineExpose({-->
<!--  playAudio,-->
<!--  initWebsocket,-->
<!--  stopAudio,-->
<!--});-->
<!--</script>-->

<!--<style scoped>-->
<!--.tts-player {-->
<!--  display: flex;-->
<!--  flex-direction: column;-->
<!--  align-items: center;-->
<!--}-->

<!--.tts-input {-->
<!--  width: 80%;-->
<!--  height: 100px;-->
<!--  margin-bottom: 1rem;-->
<!--  padding: 0.5rem;-->
<!--}-->

<!--.tts-button {-->
<!--  padding: 0.5rem 1rem;-->
<!--  background-color: #007bff;-->
<!--  color: white;-->
<!--  border: none;-->
<!--  cursor: pointer;-->
<!--}-->
<!--</style>-->
