<script setup lang="ts">
import { useRemoteWitnessStore } from "@/store";
import { HSNRItem } from "@/types/type";
import { stripSSMLTags } from "@/utils/utils";
import "@tower1229/videojs-plugin-marker";
import "@tower1229/videojs-plugin-marker/dist/style.css";
import "@videojs/http-streaming";
import type { TabsPaneContext } from "element-plus";
import videojs from "video.js";
import type Player from "video.js/dist/types/player";
import "video.js/dist/video-js.css";
import { nextTick, onUnmounted, ref, watch } from "vue";
import DialogItem from "./DialogItem.vue";
interface Marker {
  offset: number;
  data: {
    content: string;
  };
}
// import "videojs-contrib-hls";
// 定义 props 类型
interface Props {
  visible?: boolean;
  videoUrl: string;
  hsnr?: HSNRItem[];
}

const activeName = ref("first");
const store = useRemoteWitnessStore();
const handleClick = (tab: TabsPaneContext, event: Event) => {
  console.log(tab, event);
};

// 使用 withDefaults 设置默认值
const props = withDefaults(defineProps<Props>(), {
  title: "视频预览",
  visible: false,
});

// 定义 emit 事件
const emit = defineEmits<{
  (e: "update:visible", value: boolean): void;
}>();

// 创建 ref
const videoPlayerRef = ref<HTMLVideoElement | null>(null);
const videoPlayer = ref<Player | null>(null);

// 处理关闭事件
const handleClose = () => {
  emit("update:visible", false);
};

// 更新可见性
const updateVisible = (value: boolean) => {
  emit("update:visible", value);
};

// 初始化视频播放器
const initVideoPlayer = () => {
  if (videoPlayerRef.value) {
    try {
      videoPlayer.value = videojs(videoPlayerRef.value, {
        controls: true,
        autoPlay: true,

        sources: [
          {
            src: props.videoUrl,
            type: "video/mp4",
          },
        ],
        html5: {
          hls: {
            enableLowInitialPlaylist: true,
            smoothQualityChange: true,
            overrideNative: true,
          },
        },
      });
      // 添加错误处理
      videoPlayer.value.on("error", function () {
        console.error("视频播放错误:", videoPlayer.value?.error());
      });
      videoPlayer.value.on("loadedmetadata", () => {
        nextTick(() => {
          window.dispatchEvent(new Event("resize"));
        });
      });
      //  设置打点信息
      console.log(generateMarkers(), "generateMarkers(),");
      videoPlayer.value.markerPlugin({
        markers: generateMarkers(),
      });
    } catch (error) {
      console.error("播放器初始化错误:", error);
    }
  }
};
const generateMarkers = (): Marker[] => {
  const markers: Marker[] = [];

  // 从 hsnr 中生成标记
  props.hsnr?.forEach((item, index) => {
    // 话术标记
    markers.push({
      offset: item.start_time,
      data: {
        content: "话术",
      },
    });
    // 客户回答标记
    if (item.asr_time) {
      markers.push({
        offset: item.asr_time,
        data: {
          content: "客户回答",
        },
      });
    }
  });

  // 从 detectionHistory 中生成标记
  store.detectionHistory?.forEach((item) => {
    markers.push({
      offset: item.elapsedSeconds,
      data: {
        content: "智能检测",
      },
    });
  });

  return markers;
};
// 销毁视频播放器
const destroyVideoPlayer = () => {
  if (videoPlayer.value) {
    videoPlayer.value.dispose();
    videoPlayer.value = null;
  }
};

// 监听 visible 变化
watch(
  () => props.visible,
  (newVal) => {
    if (newVal) {
      nextTick(() => {
        initVideoPlayer();
      });
    } else {
      destroyVideoPlayer();
    }
  },
  { immediate: true }
);

// 组件卸载时销毁播放器
onUnmounted(() => {
  destroyVideoPlayer();
});
const showTimeTooltip = ref(-1);

// 格式化时间

// 跳转到指定时间点
const jumpToTime = (timestamp: number | undefined) => {
  if (videoPlayer.value) {
    videoPlayer.value.currentTime(timestamp);
  }
};
</script>

<template>
  <div class="w-full h-full">
    <div class="flex flex-row border-t-1 border-[#DDDDDD] pt-11">
      <div class="video-preview-container flex-2 max-h-[60vh]">
        <video
          ref="videoPlayerRef"
          class="video-js vjs-default-skin h-full"
          controls
          preload="auto"
          width="100%"
          height="100%"
        >
          <source :src="videoUrl" type="video/mp4" />
        </video>
      </div>

      <div class="flex-1 flex-grow-1">
        <el-tabs v-model="activeName" class="ml-17" @tab-click="handleClick">
          <el-tab-pane label="话术/回答详情" name="first">
            <div class="dialog-list">
              <div
                v-for="(item, index) in props.hsnr"
                :key="index"
                class="dialog-item"
              >
                <dialog-item
                  :timestamp="item.start_time"
                  role="话术"
                  :content="stripSSMLTags(item.nr)"
                  :index="index"
                  text-class="text-hex-333333"
                  @jump="jumpToTime"
                />
                <dialog-item
                  v-if="item.khhd !== '无需'"
                  :timestamp="item.asr_time"
                  role="客户"
                  :content="item.asr_content"
                  :index="index"
                  text-class="text-hex-52C41A"
                  @jump="jumpToTime"
                />
              </div>
            </div>
          </el-tab-pane>
          <el-tab-pane label="智能检测结果" name="second">
            <div>
              <div
                v-for="(item, index) in store.detectionHistory"
                :key="index"
                class="dialog-item"
              >
                <dialog-item
                  :timestamp="item.elapsedSeconds"
                  role="智能检测"
                  role-class="text-[#333333] text-14 w-70"
                  :content="item.detail"
                  :index="index"
                  text-class="text-hex-FF1919"
                  @jump="jumpToTime"
                />
              </div>
            </div>
          </el-tab-pane>
        </el-tabs>
      </div>
    </div>
  </div>
</template>

<style scoped>
.video-preview-container {
  flex: 1;
}
.video-preview-container {
  display: flex;
  justify-content: center;
  align-items: center;

  width: 100%;
  max-height: 60vh;
  @apply aspect-video;
}
.custom-class {
  font-weight: bold;
  color: red;
}

.video-js {
  width: 100%;
  height: 100%;
  object-fit: contain;
}
:deep(.el-tab-pane) {
  height: 50vh;
  overflow-y: auto;
}
</style>
