<script lang="ts" setup>
import { useI18n } from "vue-i18n";
import { getParam<PERSON>ey } from "@/utils/utils";
import useRemoteWitnessStore from "@/store/modules/remoteWitness";

const { t } = useI18n();
// init input params
</script>

<template>
  <div class="inputs-container">
    <h1 style="font-size: 14px; font-weight: 500">{{ t("param") }}</h1>
    <el-row :gutter="10">
      <el-col :span="12">
        <el-input v-model="store.sdkAppId" placeholder="sdkAppId" type="number">
          <template #prepend><div class="key">SDKAppID</div></template>
        </el-input>
      </el-col>
      <el-col :span="12">
        <el-input
          v-model="store.sdkSecretKey"
          placeholder="sdkSecretKey"
          type="string"
        >
          <template #prepend><div class="key">sdkSecretKey</div></template>
        </el-input>
      </el-col>
    </el-row>
    <el-row :gutter="10">
      <el-col :span="12">
        <el-input v-model="store.userId" placeholder="userID">
          <template #prepend><div class="key">UserID</div></template>
        </el-input>
      </el-col>
      <el-col :span="12">
        <el-input v-model="store.roomId" placeholder="roomID" type="number">
          <template #prepend><div class="key">RoomID</div></template>
        </el-input>
      </el-col>
    </el-row>
    <div class="alert">
      <el-alert type="error" :closable="false">
        <span class="alert">
          {{ t("alert") }}
          <a target="_blank" :href="t('url')">{{ t("click") }}</a>
        </span>
      </el-alert>
    </div>
  </div>
</template>

<style lang="stylus" scoped>
.el-row
  margin-bottom 10px
.key
  width 80px
  color #212529
.alert
  font-size 14px !important
  padding 10px 0
</style>
