<script lang="ts" setup>
import { WitnessAI } from "@/api/witness-ai";
import useRemoteWitnessStore from "@/store/modules/remoteWitness";
import { useQuery } from "@tanstack/vue-query";
import { isNumber } from "lodash-es";

const remoteWitnessStore = useRemoteWitnessStore();
// 获取质量检查数据
const { data: qualityCheckData } = useQuery({
  queryKey: ["queryAllQualityCheck"],
  queryFn: () => WitnessAI.queryAllQualityCheck(),
  select: (res) => res.records,
});

/**客户回答检测的配置 */
const KHHDJC = computed(() =>
  qualityCheckData.value?.find((item) => item.code === "KHHDJC")
);
/**人脸质量检测的配置 */
const RLZLJC = computed(() =>
  qualityCheckData.value?.find((item) => item.code === "RLZLJC")
);
/**在框人数检测的配置 */
const ZKRSJC = computed(() =>
  qualityCheckData.value?.find((item) => item.code === "ZKRSJC")
);
/**人脸比对检测的配置 */
const RLBD = computed(() =>
  qualityCheckData.value?.find((item) => item.code === "RLBD")
);

/**通过分 */
const totalPassScore = computed(() => {
  if (KHHDJC.value && RLZLJC.value && ZKRSJC.value && RLBD.value) {
    return (
      Number(KHHDJC.value.passScore) +
      Number(RLZLJC.value.passScore) +
      Number(ZKRSJC.value.passScore) +
      Number(RLBD.value.passScore)
    );
  }
  return 0;
});

//根据检测记录计算分数
const detectionHistoryRes = computed(() => {
  if (KHHDJC.value && RLZLJC.value && ZKRSJC.value && RLBD.value) {
    let answerScore = KHHDJC.value.totalScore;
    let faceQualityScore = RLZLJC.value.totalScore;
    let faceNumberScore = ZKRSJC.value.totalScore;
    let faceCompareScore = RLBD.value.totalScore;

    remoteWitnessStore.detectionHistory.forEach((item, idx) => {
      if (KHHDJC.value && RLZLJC.value && ZKRSJC.value && RLBD.value) {
        if (item.faceCount > Number(ZKRSJC.value.value)) {
          //在框人数检测
          faceNumberScore -= ZKRSJC.value.onceDeductedScore;
        }
        if (
          item.detail.includes("遮挡") ||
          item.detail.includes("人脸质量不合格，请重新拍摄") //TODO: 结合后端代码确认这个是扣质量分还是扣人脸比对分
        ) {
          //人脸质量检测
          faceQualityScore -= RLZLJC.value.onceDeductedScore;
        }
        if (
          (item.compareScore != 0 &&
            item.compareScore < RLBD.value.onceDeductedScore) ||
          item.detail.includes("人脸对比结果")
        ) {
          //人脸比对检测
          faceCompareScore -= RLBD.value.onceDeductedScore;
        }
        if (item.detail.includes("客户回答内容") && idx === 0) {
          //客户回答检测
          answerScore -= RLBD.value.onceDeductedScore;
        }
      }
    });
    let totalScore =
      answerScore + faceQualityScore + faceNumberScore + faceCompareScore;
    return {
      totalScore: totalScore,
      answerScore: answerScore,
      faceQualityScore: faceQualityScore,
      faceCompareScore: faceCompareScore,
      faceNumberScore: faceNumberScore,
    };
  } else {
    return {
      totalScore: "--",
      answerScore: "--",
      faceQualityScore: "--",
      faceCompareScore: "--",
      faceNumberScore: "--",
    };
  }
});

/**
 * 计算提交时的视频检测结果
 */
watchEffect(() => {
  if (isNumber(detectionHistoryRes.value?.totalScore)) {
    const isVideoDetectionPassed =
      detectionHistoryRes.value.totalScore > totalPassScore.value;
    console.log(
      "detectionHistoryRes.value",
      detectionHistoryRes.value,
      isVideoDetectionPassed
    );
    remoteWitnessStore.submitDetectionResult({
      videoDetection: isVideoDetectionPassed,
    });
  }
});
</script>

<template>
  <div class="m-11 text-sm">
    <div class="flex justify-between mb-18">
      <div class="text-[#333]">本次智能检测结果</div>
      <div>
        <span class="text-primary">{{ detectionHistoryRes.totalScore }}分</span>
        <span class="text-[#333]">
          （{{
            isNumber(detectionHistoryRes.totalScore)
              ? detectionHistoryRes.totalScore > totalPassScore
                ? "通过"
                : "未通过"
              : "--"
          }}）
        </span>
      </div>
    </div>
    <div class="flex flex-col gap-8">
      <div class="flex justify-between">
        <div class="text-[#666]">客户回答内容智能检测</div>
        <div class="text-[#333] flex items-center gap-8">
          <ElTag
            v-if="
              KHHDJC?.passScore &&
              isNumber(detectionHistoryRes.answerScore) &&
              detectionHistoryRes.answerScore < KHHDJC.passScore
            "
            type="warning"
            size="small"
          >
            较低
          </ElTag>
          <span>{{ detectionHistoryRes.answerScore }}分</span>
          <!-- <svg-icon icon-class="right-arrow" size="14" color="#888" /> -->
        </div>
      </div>
      <div class="flex justify-between">
        <div class="text-[#666]">客户人脸质量智能检测</div>
        <div class="text-[#333] flex items-center gap-8">
          <ElTag
            v-if="
              RLZLJC?.passScore &&
              isNumber(detectionHistoryRes.faceQualityScore) &&
              detectionHistoryRes.faceQualityScore < RLZLJC?.passScore
            "
            type="warning"
            size="small"
          >
            较低
          </ElTag>
          <span>{{ detectionHistoryRes.faceQualityScore }}分</span>
          <!-- <svg-icon icon-class="right-arrow" size="14" color="#888" /> -->
        </div>
      </div>
      <div class="flex justify-between">
        <div class="text-[#666]">客户人脸比对智能检测</div>
        <div class="text-[#333] flex items-center gap-8">
          <ElTag
            v-if="
              RLBD?.passScore &&
              isNumber(detectionHistoryRes.faceCompareScore) &&
              detectionHistoryRes.faceCompareScore < RLBD?.passScore
            "
            type="warning"
            size="small"
          >
            较低
          </ElTag>
          <span>{{ detectionHistoryRes.faceCompareScore }}分</span>
          <!-- <svg-icon icon-class="right-arrow" size="14" color="#888" /> -->
        </div>
      </div>
      <div class="flex justify-between">
        <div class="text-[#666]">客户在框检测智能检测</div>
        <div class="text-[#333] flex items-center gap-8">
          <ElTag
            v-if="
              ZKRSJC?.passScore &&
              isNumber(detectionHistoryRes.faceNumberScore) &&
              detectionHistoryRes.faceNumberScore < ZKRSJC?.passScore
            "
            type="warning"
            size="small"
          >
            较低
          </ElTag>
          <span>{{ detectionHistoryRes.faceNumberScore }}分</span>
          <!-- <svg-icon icon-class="right-arrow" size="14" color="#888" /> -->
        </div>
      </div>
    </div>
  </div>
  <div class="border-b-0.5px border-dashed border-b-[#d6d6d6] mt-20 mb-8"></div>
</template>
