<script lang="ts" setup>
import { useI18n } from "vue-i18n";
import TRTC from "trtc-sdk-v5";
import { ElMessage } from "element-plus/es";

import { DeviceItem } from "@/types/type";
import useRemoteWitnessStore from "@/store/modules/remoteWitness";

const store = useRemoteWitnessStore();
const { t } = useI18n();
const emit = defineEmits(["switchDevice"]);

const updateDevice = async () => {
  console.log("updateDevice");
  const cameraItems: DeviceItem[] = await TRTC.getCameraList();
  console.log(cameraItems);
  cameraItems.forEach((item) => {
    item.value = item.deviceId;
  });
  const microphoneItems: DeviceItem[] = await TRTC.getMicrophoneList();
  microphoneItems.forEach((item) => {
    item.value = item.deviceId;
  });

  store.$patch({
    cameraList: cameraItems,
    microphoneList: microphoneItems,
  });

  if (!store.videoDeviceId) {
    store.videoDeviceId = cameraItems[0].deviceId;
  }

  if (!store.audioDeviceId) {
    store.audioDeviceId = microphoneItems[0].deviceId;
  }
};

navigator.mediaDevices
  .getUserMedia({ audio: true, video: true })
  .then((stream) => {
    stream.getTracks().forEach((track) => {
      track.stop();
    });
    updateDevice();
  })
  .catch(() => {
    ElMessage({ message: t("permit"), type: "error" });
  });

navigator.mediaDevices.ondevicechange = updateDevice;

const handleDeviceChange = () => {
  emit("switchDevice", {
    videoId: store.videoDeviceId,
    audioId: store.audioDeviceId,
  });
};
</script>

<template>
  <div class="select-container">
    <h1 style="font-size: 14px; font-weight: 500">{{ t("device") }}</h1>
    <el-row :gutter="10">
      <el-col :span="12" class="device-container">
        <div class="label">Camera</div>
        <el-select
          v-model="store.videoDeviceId"
          class="select"
          placeholder="Camera"
          @change="handleDeviceChange"
        >
          <el-option
            v-for="item in store.cameraList"
            :key="item.deviceId"
            :label="item.label"
            :value="item.deviceId"
          />
        </el-select>
      </el-col>
      <el-col :span="12" class="device-container">
        <div class="label">Microphone</div>
        <el-select
          v-model="store.audioDeviceId"
          class="select"
          placeholder="Microphone"
          @change="handleDeviceChange"
        >
          <el-option
            v-for="item in store.microphoneList"
            :key="item.deviceId"
            :label="item.label"
            :value="item.deviceId"
          />
        </el-select>
      </el-col>
    </el-row>
    <p style="font-size: 14px">{{ t("deviceHint") }}</p>
  </div>
</template>

<style lang="stylus" scoped>
.select-container
  padding-bottom 5px
  .device-container
    display flex
    width 100%

  .label
    padding 0 20px
    width 120px
    height 32px
    line-height 32px
    font-size 14px
    border-top 1px solid #DCDFE6
    border-left 1px solid #DCDFE6
    border-bottom 1px solid #DCDFE6
    border-radius 4px 0 0 4px
    color #212529
    background-color #F5F7FA

  .select
    width 100%
</style>
