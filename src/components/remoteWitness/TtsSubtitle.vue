<script setup lang="ts">
import { ref, onMounted, onUnmounted, nextTick, watch, computed } from "vue";

export interface SubtitleItem {
  text: string;
  beginTime: number; // 开始时间
  endTime: number; // 结束时间
  beginIndex: number;
  endIndex: number;
}

// 修改 Props 接口
interface TtsSubtitleProps {
  subtitles: SubtitleItem[];
}

const props = defineProps<TtsSubtitleProps>();

const watchSubtitles = computed(() => props.subtitles);

const subtitleChars = computed(() => props.subtitles.map((val) => val.text));

// 更新时间的定时器
let timeUpdateInterval: NodeJS.Timeout | null = null;
// 缓存当前激活字幕下标
const currentActiveIndex = ref<number | null>(null);

// 将文本拆分成字符数组

// 开始时间
const startTime = ref(Date.now());
const currentTime = ref(Date.now());

// 是否全部播放完成
const isAllPlayed = ref(false);
// 滚动相关引用和状态
const subtitleContainer = ref<HTMLDivElement | null>(null);
const subtitleWrapper = ref<HTMLDivElement | null>(null);
const scrollOffset = ref(0);

// 自动滚动函数
const autoScroll = async () => {
  // 找到当前激活的字符
  const activeSubtitle = watchSubtitles.value.find((subtitle) => {
    if (!subtitle.beginTime) return false;
    const elapsedTime = currentTime.value - startTime.value;
    return elapsedTime >= subtitle.beginTime && elapsedTime <= subtitle.endTime;
  });

  if (activeSubtitle) {
    await nextTick();
    const currentChar =
      subtitleWrapper.value!.querySelector(".tts-char-current");
    if (currentChar) {
      const containerRect = subtitleContainer.value!.getBoundingClientRect();
      const charRect = currentChar.getBoundingClientRect();
      const wrapperRect = subtitleWrapper.value!.getBoundingClientRect();

      // 计算当前字符右边缘是否快要超出容器
      const charRightEdge = charRect.right - wrapperRect.left;
      const containerRightEdge = containerRect.right - wrapperRect.left;
      const distanceFromEdge = containerRightEdge - charRightEdge;

      // 如果字符右边缘即将超出，则开始滚动
      const charsToOffset = 4; // 往左偏移4个字符
      const offsetWidth = charsToOffset * (charRect.width + 6); // 每个字符的宽度（包括margin）

      if (distanceFromEdge < offsetWidth) {
        const scrollDistance =
          containerRect.width -
          (charRect.left - wrapperRect.left) -
          charRect.width -
          offsetWidth;

        // 平滑过渡到新的偏移量
        scrollOffset.value = scrollDistance;
      }
    }
  }
};

// 阻止默认滚动行为
const preventScroll = (e: Event) => {
  e.preventDefault();
};

onMounted(() => {
  // 阻止滚动事件
  if (subtitleContainer.value) {
    subtitleContainer.value.addEventListener("wheel", preventScroll, {
      passive: false,
    });
    subtitleContainer.value.addEventListener("touchmove", preventScroll, {
      passive: false,
    });
  }
});

watch(currentTime, () => {
  autoScroll();
});

const isPlaying = ref(false);

// 新增播放控制函数
// 添加动画帧 ID
let animationFrameId: number | null = null;

// 添加销毁标志
let isDestroyed = false;

// 修改播放控制函数
const startPlay = () => {
  if (isPlaying.value) return;

  isDestroyed = false;
  startTime.value = Date.now();
  isPlaying.value = true;

  // 使用 requestAnimationFrame 替代 setInterval
  const updateFrame = () => {
    if (isDestroyed) return;

    currentTime.value = Date.now();
    if (isPlaying.value) {
      animationFrameId = requestAnimationFrame(updateFrame);
    }
  };
  updateFrame();
};

const pausePlay = () => {
  if (!isPlaying.value) return;

  isDestroyed = true;
  isPlaying.value = false;
  if (animationFrameId) {
    cancelAnimationFrame(animationFrameId);
    animationFrameId = null;
  }
};

// // 修改清理函数，确保清理动画帧
// const clearSub = () => {
//   pausePlay();
//   scrollOffset.value = 0;
//   isAllPlayed.value = false;
// };
// 暴露方法

const clearSub = () => {
  pausePlay();
  scrollOffset.value = 0;
  isAllPlayed.value = false;
  currentActiveIndex.value = null;
};
// 修改组件卸载时的清理
onUnmounted(() => {
  isDestroyed = true;
  if (animationFrameId) {
    cancelAnimationFrame(animationFrameId);
    animationFrameId = null;
  }
  if (timeUpdateInterval) {
    clearInterval(timeUpdateInterval);
  }
  // 移除事件监听
  if (subtitleContainer.value) {
    subtitleContainer.value.removeEventListener("wheel", preventScroll);
    subtitleContainer.value.removeEventListener("touchmove", preventScroll);
  }
});
defineExpose({
  startPlay,
  pausePlay,
  clearSub,
});
// 获取字符的类名
const getCharClass = (index: number) => {
  if (isAllPlayed.value) {
    return "tts-char-active";
  }
  // 如果缓存激活字幕下标与 index 相同，则直接返回激活样式
  if (currentActiveIndex.value && currentActiveIndex.value > index) {
    return "tts-char-active";
  }
  const activeSubtitle = watchSubtitles.value.find((subtitle) => {
    if (!subtitle.beginTime) return false;
    const elapsedTime = currentTime.value - startTime.value;
    return elapsedTime >= subtitle.beginTime && elapsedTime <= subtitle.endTime;
  });

  if (activeSubtitle) {
    if (index < activeSubtitle.beginIndex) {
      return "tts-char-active";
    } else if (index === activeSubtitle.beginIndex) {
      // 更新缓存
      currentActiveIndex.value = index;
      return "tts-char-current";
    } else if (index > activeSubtitle.beginIndex) {
      return "tts-char-inactive";
    }
  }

  return "tts-char-inactive";
};

// 获取动态样式
const getCharStyle = (index: number) => {
  if (isAllPlayed.value) {
    return {};
  }

  const activeSubtitle = watchSubtitles.value.find((subtitle) => {
    if (!subtitle.beginTime) return false;
    const elapsedTime = currentTime.value - startTime.value;
    return elapsedTime >= subtitle.beginTime && elapsedTime <= subtitle.endTime;
  });

  if (activeSubtitle && index === activeSubtitle.beginIndex) {
    const elapsedTime = currentTime.value - startTime.value;
    const duration = activeSubtitle.endTime - activeSubtitle.beginTime;
    const relativeTime = elapsedTime - activeSubtitle.beginTime;

    // 动态进度（0-1）
    const progress = Math.min(1, Math.max(0, relativeTime / duration));

    return {
      "--progress": progress,
    };
  }

  return {};
};
</script>

<template>
  <div ref="subtitleContainer" class="tts-subtitle-container">
    <div
      ref="subtitleWrapper"
      class="tts-subtitle-wrapper marquee-content whitespace-nowrap"
      :style="{ transform: `translateX(${scrollOffset}px)` }"
    >
      <span
        v-for="(char, index) in subtitleChars"
        :key="index"
        ref="charRefs"
        :class="getCharClass(index)"
        class="tts-char"
        :style="getCharStyle(index)"
        :data-text="char"
        :data-begin="index"
      >
        {{ char }}
      </span>
    </div>
  </div>
</template>

<style scoped>
.tts-subtitle-container {
  display: flex;
  justify-content: flex-start;
  align-items: center;
  font-size: 36px;
  margin-top: 20px;
  user-select: none;
  overflow: hidden;
  width: 100%;
  position: relative;
}

.tts-subtitle-wrapper {
  display: flex;
  align-items: center;
  transition: transform 0.3s ease;
  will-change: transform;
}

.tts-char {
  position: relative;
  margin: 0 3px;
  display: inline-block;
  font-weight: normal;
  color: #a8abb2; /* 默认未激活颜色 */
  transition:
    transform 0.1s ease,
    color 0.1s ease;
}

/* 未激活字符 */
.tts-char-inactive {
  transform: scale(1);
}

/* 已高亮字符 */
.tts-char-active {
  color: #409eff;
  font-weight: bold;
  transform: scale(1.05);
}

/* 当前激活字符 */
.tts-char-current {
  position: relative;
  color: #a8abb2; /* 文字本身透明，由 ::before 渐变显示 */
  font-weight: bold;
  transform: scale(1.1);
}

/* 动态渐变效果 */
.tts-char-current::before {
  content: attr(data-text);
  position: absolute;
  left: 0;
  top: 0;
  background: linear-gradient(to right, #a8abb2 0%, #409eff 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  width: calc(var(--progress) * 100%);
  overflow: hidden;
  white-space: nowrap;
  transition: width 0.1s ease;
}
</style>
