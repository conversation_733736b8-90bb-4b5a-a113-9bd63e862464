// useTTSAudioPlayer.ts
import { SubtitleItem } from "@/components/remoteWitness/TtsSubtitle.vue";
import useRemoteWitnessStore, {
  useRemoteWitnessStoreHook,
} from "@/store/modules/remoteWitness";
import { emitter, EventTypes } from "@/utils/eventBus";
import WebSocketSingleton from "@/utils/WebSocketSingleton";
import { onBeforeUnmount, ref, watch } from "vue";

interface AudioMessage {
  status: "START" | "END" | "TEXT" | "audio" | "ERROR";
  data: string;
}

export const createAudioState = () => ({
  text: ref<string>(""),
  isReady: ref(false),
  subtitles: ref<SubtitleItem[]>([]),
  mediaSource: ref<MediaSource>(new MediaSource()),
  audioQueue: [] as Uint8Array[],
  isAppending: false,
  isCompleted: false,
  audioContextInitialized: false,
  ttsId: "",
  segments: [] as string[],
  totalSegments: 0, // 新增：总分段数
  completedSegments: 0, // 新增：已完成的分段数
});

export class AudioHandler {
  private audioElement: HTMLAudioElement | null = null;
  private sourceBuffer: SourceBuffer | null = null;
  private audioContext: AudioContext | null = null;
  private mediaElementSource: MediaElementAudioSourceNode | null = null;
  private trtcInstance: any = null;
  private AudioMixerInit: boolean = false;
  private readonly AudioMixerId = "hsbf";
  private canPlay = false;
  private ws: WebSocketSingleton;
  private store: any;
  private state: ReturnType<typeof createAudioState>;
  private audioSegments: Map<number, Uint8Array> = new Map();
  private currentIndex = 0;
  private currentSegmentData: Map<number, Uint8Array[]> = new Map(); // 存储每段的音频数据块
  private segmentStatus: Map<number, boolean> = new Map(); // 标记每段是否接收完成
  private isFirstSegmentStarted = false; // 标记第一段是否开始处理

  constructor(
    state: ReturnType<typeof createAudioState>,
    ws: WebSocketSingleton
  ) {
    this.state = state;
    this.store = useRemoteWitnessStoreHook();
    this.ws = ws;
  }

  async initialize(audioElement: HTMLAudioElement) {
    this.cleanup();
    this.audioElement = audioElement;
    this.audioElement.src = URL.createObjectURL(this.state.mediaSource.value);
    this.state.mediaSource.value.addEventListener(
      "sourceopen",
      this.onSourceOpen.bind(this)
    );
  }

  private onSourceOpen() {
    if (
      this.state.mediaSource.value &&
      this.state.mediaSource.value.sourceBuffers.length === 0
    ) {
      try {
        this.sourceBuffer =
          this.state.mediaSource.value.addSourceBuffer("audio/mpeg");
        this.sourceBuffer.addEventListener(
          "updateend",
          this.onBufferUpdateEnd.bind(this)
        );
      } catch (error) {
        console.error("Error initializing SourceBuffer:", error);
      }
    }
  }

  async initAudioMixer() {
    if (
      !this.state.isReady ||
      !this.audioElement ||
      this.state.audioContextInitialized
    )
      return;

    try {
      if (this.audioContext) await this.audioContext.close();
      this.audioContext = new AudioContext();
      this.mediaElementSource = this.audioContext.createMediaElementSource(
        this.audioElement
      );
      const destination = this.audioContext.createMediaStreamDestination();

      this.mediaElementSource.connect(destination);
      this.mediaElementSource.connect(this.audioContext.destination);

      const audioTrack = destination.stream.getAudioTracks()[0];
      if (!audioTrack) throw new Error("No audio track available");

      await this.trtcInstance?.startPlugin("AudioMixer", {
        id: this.AudioMixerId + Math.random(),
        track: audioTrack,
      });

      this.state.audioContextInitialized = true;
    } catch (error) {
      console.error("Error initializing audio mixer:", error);
      this.state.audioContextInitialized = false;
      if (this.audioContext) {
        await this.audioContext.close();
        this.audioContext = null;
      }
      this.mediaElementSource = null;
    }
  }

  cleanup() {
    if (this.audioContext) {
      this.audioContext.close().catch(console.error);
      this.audioContext = null;
    }
    if (this.mediaElementSource) {
      this.mediaElementSource.disconnect();
      this.mediaElementSource = null;
    }
    if (this.audioElement) {
      this.audioElement.pause();
      this.audioElement.currentTime = 0;
      URL.revokeObjectURL(this.audioElement.src);
      this.audioElement = null;
    }
    this.state.audioQueue = [];
    this.state.mediaSource.value = new MediaSource();
    this.state.audioContextInitialized = false;
    this.state.isReady.value = false;
    this.state.isCompleted = false;
    this.audioSegments.clear();
    this.currentIndex = 0;
    this.state.totalSegments = 0; // 重置总段数
    this.state.completedSegments = 0; // 重置已完成段数
    this.currentSegmentData.clear();
    this.segmentStatus.clear();
    this.isFirstSegmentStarted = false;
  }

  stop() {
    if (this.audioElement) {
      this.audioElement.pause();
      this.audioElement.currentTime = 0;
      this.canPlay = false;
    }
  }

  async handleAudioMessage(message: Blob | any) {
    try {
      if (message instanceof Blob) {
        if (!this.audioElement) return;
        const arrayBuffer = await message.arrayBuffer();
        const dataView = new DataView(arrayBuffer);

        const ttsIdLength = dataView.getInt32(0);
        if (ttsIdLength < 0 || ttsIdLength > 100) {
          throw new Error("Invalid ttsId length");
        }

        const ttsIdBytes = new Uint8Array(arrayBuffer, 4, ttsIdLength);
        const receivedTtsId = new TextDecoder("utf-8").decode(ttsIdBytes);
        const audioData = new Uint8Array(arrayBuffer, 4 + ttsIdLength);
        if (audioData.length <= 0) {
          throw new Error("Invalid audio data");
        }
        const [baseTtsId, indexStr] = receivedTtsId.split("_");
        const index = parseInt(indexStr, 10);

        if (baseTtsId !== this.state.ttsId) return;
        // 初始化该段的数据数组
        if (!this.currentSegmentData.has(index)) {
          this.currentSegmentData.set(index, []);
        }

        // 将数据块添加到对应的段
        const segmentChunks = this.currentSegmentData.get(index);
        if (segmentChunks) {
          segmentChunks.push(audioData);
        }

        // 对第一段的特殊处理
        if (index === 0) {
          this.processFirstSegmentChunk(index);
        }
      } else {
        const body = message;
        const receivedTtsId = body.data.requestId;
        const [baseTtsId, indexStr] = receivedTtsId?.split("_") || [];
        if (baseTtsId !== this.state.ttsId) return;
        const index = parseInt(indexStr, 10);

        switch (body.status) {
          case "START":
            // 假设 START 消息中包含总段数（需后端配合）
            if (body.data.totalSegments !== undefined) {
              this.state.totalSegments = body.data.totalSegments;
            }
            console.log(body.data);
            break;
          case "SEGMENTS":
            // 假设 START 消息中包含总段数（需后端配合）
            if (body.data.total !== undefined) {
              this.state.totalSegments = body.data.total;
              this.state.segments = body.data.segments;
            }
            console.log(body.data);
            break;
          case "END":
            this.state.completedSegments++;
            this.segmentStatus.set(index, true);

            // 如果是第一段接收完成，合并并清理
            if (index === 0) {
              this.processFirstSegmentComplete(index);
            } else {
              // 其他段等待完成后处理
              this.processCompletedSegment(index);
            }

            if (this.state.completedSegments === this.state.totalSegments) {
              this.state.isCompleted = true;
            }
            break;
          case "TEXT":
            const { result } = body.data;
            if (!result?.subtitles) return;

            const { segments } = this.state;
            if (!segments[index]) return;

            // 计算文本范围
            const textRange = {
              beginIndex: segments
                .slice(0, index)
                .reduce((acc, seg) => acc + seg.length, 0),
              endIndex: 0,
            };
            textRange.endIndex = textRange.beginIndex + segments[index].length;
            console.log(textRange, "textRange");
            // 更新字幕并发送事件
            this.state.subtitles.value = result.subtitles;
            emitter.emit(EventTypes.TTS_SUBTITLES_UPDATE, {
              subtitles: result.subtitles,
              tts: baseTtsId,
              ...textRange,
            });
            break;
        }
      }
    } catch (error) {
      console.error("Error handling audio message:", error);
    }
  }

  private processFirstSegmentChunk(index: number) {
    // 确保这是第一段数据
    if (index === 0) {
      const chunks = this.currentSegmentData.get(index);
      if (chunks && chunks.length > 0) {
        // 每收到一个数据块就立即添加到播放队列
        const latestChunk = chunks[chunks.length - 1];
        this.addToQueue(latestChunk);
        this.isFirstSegmentStarted = true;
      }
    }
  }

  private processFirstSegmentComplete(index: number) {
    // 第一段完成时的处理
    const chunks = this.currentSegmentData.get(index);
    if (chunks && this.segmentStatus.get(index)) {
      // 清理第一段的数据
      this.currentSegmentData.delete(index);
      this.segmentStatus.delete(index);
    }
  }

  private processCompletedSegment(index: number) {
    const segmentData = this.currentSegmentData.get(index);
    if (segmentData && this.segmentStatus.get(index)) {
      // 合并该段的所有数据块
      const totalLength = segmentData.reduce(
        (acc, chunk) => acc + chunk.length,
        0
      );
      const combinedData = new Uint8Array(totalLength);
      let offset = 0;

      segmentData.forEach((chunk) => {
        combinedData.set(chunk, offset);
        offset += chunk.length;
      });

      // 将合并后的数据添加到播放队列
      this.addToQueue(combinedData);

      // 清理已处理的段数据
      this.currentSegmentData.delete(index);
      this.segmentStatus.delete(index);
    }
  }

  private addToQueue(data: Uint8Array) {
    this.state.audioQueue.push(data);
    if (!this.state.isAppending) {
      this.state.isAppending = true;
      this.processQueue();
    }
  }

  private processQueue() {
    if (
      this.state.audioQueue.length > 0 &&
      this.sourceBuffer &&
      !this.sourceBuffer.updating
    ) {
      const nextData = this.state.audioQueue.shift();
      if (nextData) {
        this.appendAudioData(nextData);
      }
    }
  }

  private appendAudioData(data: Uint8Array) {
    if (this.sourceBuffer && !this.sourceBuffer.updating) {
      this.sourceBuffer.appendBuffer(data);
    }
  }

  private onBufferUpdateEnd() {
    if (
      this.state.audioQueue.length > 0 &&
      this.sourceBuffer &&
      !this.sourceBuffer.updating
    ) {
      const nextData = this.state.audioQueue.shift();
      if (nextData) {
        this.appendAudioData(nextData);
      }
    } else if (this.sourceBuffer && !this.sourceBuffer.updating) {
      this.state.isAppending = false;
      this.state.mediaSource.value?.endOfStream();
    }

    if (this.audioElement && this.canPlay) {
      this.audioElement.play().catch(console.error);
      this.state.isReady.value = true;
    }
  }

  async play(
    textInput: string,
    trtc: any,
    audioElement: HTMLAudioElement,
    speed?: number
  ) {
    if (!textInput.trim()) throw new Error("请输入文本！");

    this.canPlay = true;
    if (this.state.text.value !== textInput) {
      this.cleanup();
      this.state.text.value = textInput;
      this.state.ttsId = Math.random().toString(36).substr(2, 8);
      await this.initialize(audioElement);
    } else if (this.audioElement) {
      this.audioElement.currentTime = 0;
      this.audioElement.play().catch(console.error);
      return this.state.ttsId;
    }

    this.trtcInstance = trtc;
    this.state.isCompleted = false;
    this.state.isReady.value = false;

    // 计算总段数（假设每600字符一段，后端也可以返回）
    const maxLength = 600;
    this.state.totalSegments = Math.ceil(textInput.length / maxLength);

    this.ws?.publish({
      destination: "/tts",
      body: JSON.stringify({
        text: textInput,
        ttsId: this.state.ttsId,
        speed: speed,
      }),
    });
    return this.state.ttsId;
  }
}

export function useTTSAudioPlayer(handAudioEnd?: () => void) {
  const state = createAudioState();
  let ws: WebSocketSingleton = WebSocketSingleton.getNewWebsocket();
  let audioHandler: AudioHandler | null = null;
  const store = useRemoteWitnessStore();

  const onAudioEnded = (): boolean => {
    return state.isCompleted;
  };

  const initWebsocket = async (): Promise<void> => {
    await ws.connect(store.socketToken);
    audioHandler = new AudioHandler(state, ws);
    try {
      ws?.subscribe(
        "/audio",
        audioHandler.handleAudioMessage.bind(audioHandler)
      );
      ws?.subscribe(
        "/tts_event",
        audioHandler.handleAudioMessage.bind(audioHandler)
      );
    } catch (error) {
      console.error("Error initializing WebSocket:", error);
    }
  };

  watch(
    () => store.socketToken,
    (val) => {
      if (val) {
        initWebsocket();
      }
    },
    { immediate: true }
  );

  let audioElement: HTMLAudioElement | null = null;

  const playAudio = async (
    textInput: string,
    trtc: any,
    speed?: number
  ): Promise<string> => {
    if (!audioHandler) throw new Error("AudioHandler not initialized");

    if (state.text.value !== textInput) {
      audioElement = document.createElement("audio");
      audioElement.addEventListener("ended", handleAudioEnded);
      audioElement.addEventListener("canplay", initAudioMixer);
      return await audioHandler.play(textInput, trtc, audioElement, speed);
    } else {
      if (!audioElement) throw new Error("AudioElement not initialized");
      return await audioHandler.play(textInput, trtc, audioElement, speed);
    }
  };

  const stopAudio = (): void => {
    audioHandler?.stop();
  };

  const handleAudioEnded = (): void => {
    if (onAudioEnded()) handAudioEnd?.();
  };

  const initAudioMixer = async (): Promise<void> => {
    await audioHandler?.initAudioMixer();
  };

  onBeforeUnmount(() => {
    if (audioElement) {
      audioElement.removeEventListener("ended", handleAudioEnded);
      audioElement.removeEventListener("canplay", initAudioMixer);
    }
    ws?.disconnect();
    ws = null;
    audioHandler?.cleanup();
  });

  return {
    state,
    playAudio,
    stopAudio,
    onAudioEnded,
    audioHandler,
    initWebsocket,
  };
}
