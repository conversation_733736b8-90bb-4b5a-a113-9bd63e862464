<script lang="ts" setup>
import { Status } from "@/config/appConfig";
import { useRemoteWitnessStore } from "@/store";
const store = useRemoteWitnessStore();

defineProps<{
  status: Status;
}>();
</script>

<template>
  <div
    v-if="[Status.Recording, Status.RecordEnd].includes(status)"
    ref="historyList"
    class="mt-20 grow flex flex-col overflow-y-auto"
  >
    <div
      class="fw-500 text-16 color-hex-333333 font-leading-22 text-left ml-14"
    >
      实时质检
    </div>
    <div class="h-1px border-1px border-solid border-hex-DDDDDD m-10"></div>
    <div class="bg-hex-F2F2F2 rounded-4 m-x-10 mb-12 grow overflow-hidden">
      <DetectionResult v-if="status === Status.RecordEnd" />
      <div
        class="overflow-y-auto h-full"
        :class="{
          'h-[calc(100%-152px)]': status === Status.RecordEnd,
        }"
      >
        <div
          v-for="item in store.detectionHistory"
          :key="item.timestamp"
          ref="lastItem"
          class="flex ml-11 pt-8 font-leading-22 flex-row"
        >
          <div class="fw-400 text-16 color-hex-FF8700 text-left">
            {{ item.timestamp }}
          </div>
          <div
            class="fw-400 text-16 color-hex-333333 font-leading-22 text-left ml-8"
          >
            {{ `${item.detail}` }}
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
