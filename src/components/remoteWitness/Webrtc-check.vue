<script setup lang="ts">
import useRemoteWitnessStore from "@/store/modules/remoteWitness";
import { DeviceItem } from "@/types/type";
import { ElMessage } from "element-plus/es";
import TRTC from "trtc-sdk-v5";
import { useI18n } from "vue-i18n";

const mp3Url = "./sound/TestSpeaker.mp3";
const audioElement = new Audio(mp3Url); // 替换为你的音频文件路径

const trtc = TRTC.create();
const BAR_COUNT = 34; // 柱子总数
const MAX_VOLUME = 100; // 最大音量值
const UNIT_VOLUME = MAX_VOLUME / BAR_COUNT; // 每个柱子对应的音量单位
const jyVolumNum = 80;
const volumeNum = ref(0);
const store = useRemoteWitnessStore();
const { t } = useI18n();
const emit = defineEmits(["switchDevice"]);

// 新增：设备检测完成标志
const deviceLoaded = ref(false);

const updateDevice = async () => {
  console.log("updateDevice");
  const cameraItems: DeviceItem[] = await TRTC.getCameraList();
  cameraItems.forEach((item) => {
    item.value = item.deviceId;
  });
  const microphoneItems: DeviceItem[] = await TRTC.getMicrophoneList();
  const speaker: DeviceItem[] = await TRTC.getSpeakerList();
  microphoneItems.forEach((item) => {
    item.value = item.deviceId;
  });
  speaker.forEach((item) => {
    item.value = item.deviceId;
  });

  store.$patch({
    cameraList: cameraItems,
    microphoneList: microphoneItems,
    speakerList: speaker,
  });

  // 先尝试从本地存储获取设备ID
  store.getDeviceFromStorage();

  // 如果本地存储中没有或设备不可用，则使用默认设备
  if (
    !store.videoDeviceId ||
    !cameraItems.some((item) => item.deviceId === store.videoDeviceId)
  ) {
    store.videoDeviceId = cameraItems[0]?.deviceId;
  }

  if (
    !store.audioDeviceId ||
    !microphoneItems.some((item) => item.deviceId === store.audioDeviceId)
  ) {
    store.audioDeviceId = microphoneItems[0]?.deviceId;
  }

  if (
    !store.speakerId ||
    !speaker.some((item) => item.deviceId === store.speakerId)
  ) {
    store.speakerId = speaker[0]?.deviceId;
  }

  // 保存当前设备选择到本地存储
  store.saveDeviceToStorage();
  // 新增：设备检测完成
  deviceLoaded.value = true;
};

onMounted(async () => {
  await trtc.startLocalVideo({
    view: "video", // 在 DOM 中的 elementId 为 localVideo 的标签上预览视频。
    publish: false, // 不发布摄像头
    option: {
      cameraId: store.videoDeviceId,
    },
  });
  trtc.enableAudioVolumeEvaluation(500);
  trtc.on(TRTC.EVENT.AUDIO_VOLUME, (event) => {
    event.result.forEach(({ userId, volume }) => {
      const isMe = userId === ""; // 当 userId 为空串时，代表本地麦克风音量。
      if (isMe) {
        console.log(volume);
        volumeNum.value = volume;
      } else {
        console.log(`user: ${userId} volume: ${volume}`);
      }
    });
  });
  // 测试麦克风无需发布音频
  await trtc.startLocalAudio({
    publish: false,
    option: { microphoneId: store.audioDeviceId },
  });
});
const handlePlayAudio = async () => {
  if (audioElement.setSinkId) {
    await audioElement.setSinkId(store.speakerId); // 指定输出设备
    console.log(`音频已切换到设备 ID: ${store.speakerId}`);
  } else {
    console.warn("当前浏览器不支持 setSinkId 方法");
  }
  audioElement.currentTime = 0; // 重置播放位置
  audioElement.play().catch((err) => {
    console.error("音频播放失败：", err);
  });
  store.saveDeviceToStorage();
};
onBeforeUnmount(async () => {
  await trtc.stopLocalVideo();
  trtc.enableAudioVolumeEvaluation(-1);
  await trtc.stopLocalAudio();
});
navigator.mediaDevices
  .getUserMedia({ audio: true, video: true })
  .then((stream) => {
    stream.getTracks().forEach((track) => {
      track.stop();
    });
    updateDevice();
  })
  .catch(() => {
    ElMessage({ message: t("permit"), type: "error" });
    // 新增：检测失败也算检测完成
    deviceLoaded.value = true;
  });

navigator.mediaDevices.ondevicechange = updateDevice;

const handleDeviceChange = async () => {
  emit("switchDevice", {
    videoId: store.videoDeviceId,
    audioId: store.audioDeviceId,
  });
  await trtc.updateLocalVideo({
    option: {
      cameraId: store.videoDeviceId,
    },
  });
  await trtc.updateLocalAudio({
    option: {
      microphoneId: store.audioDeviceId,
    },
  });

  // 保存用户的设备选择到本地存储
  store.saveDeviceToStorage();
};
</script>

<template>
  <div class="w-378 h-649">
    <div
      class="h-25 fw-500 text-18 color-hex-333333 font-leading-25 text-center"
    >
      设备检测
    </div>
    <div
      class="mt-10 w-378 h-48 fw-400 text-14 color-hex-333333 font-leading-24 text-left"
    >
      请确认您的摄像头、麦克风、扬声器设备是否正常，如果存在
      多个摄像头、麦克风、杨声器，可在下方选择可用设备。
    </div>
    <div class="flex-x-center">
      <div
        id="video"
        class="camera w-100rem h-60rem bg-hex-000000 mt-30 mb-30"
      ></div>
    </div>
    <el-row :gutter="10" class="left-item">
      <el-col :span="5">
        <div class="left-title">选择摄像头</div>
      </el-col>
      <el-col :span="19">
        <el-select
          v-model="store.videoDeviceId"
          class="select"
          placeholder="Camera"
          @change="handleDeviceChange"
        >
          <el-option
            v-for="item in store.cameraList"
            :key="item.deviceId"
            :label="item.label"
            :value="item.deviceId"
          />
        </el-select>
        <div
          v-if="deviceLoaded && store.cameraList.length === 0"
          class="text-orange"
        >
          未检测到摄像头
        </div>
      </el-col>
    </el-row>
    <el-row :gutter="10" class="left-item">
      <el-col :span="5">
        <div class="left-title">选择麦克风</div>
      </el-col>
      <el-col :span="19">
        <el-select
          v-model="store.audioDeviceId"
          class="select"
          placeholder="Microphone"
          @change="handleDeviceChange"
        >
          <el-option
            v-for="item in store.microphoneList"
            :key="item.deviceId"
            :label="item.label"
            :value="item.deviceId"
          />
        </el-select>
        <div
          v-if="deviceLoaded && store.microphoneList.length === 0"
          class="text-orange"
        >
          未检测到麦克风
        </div>
      </el-col>
    </el-row>
    <el-row :gutter="10" class="left-item mt-47">
      <el-col :span="5">
        <div class="left-title">麦克风音量</div>
      </el-col>
      <el-col :span="19">
        <div
          class="mt--20 mb-9 ml-12 h-17 fw-400 text-12 color-hex-3686FD font-leading-17 text-left"
        >
          对着麦克风说"哈喽"试试，能看见音量跳动吗?
        </div>
        <div class="mic-bar-container">
          <div class="flex-y-center pl-6">
            <svg-icon icon-class="pic_yy" size="16px" />
          </div>
          <div
            v-for="(_, index) in BAR_COUNT"
            :key="index"
            class="mic-bar"
            :class="{ active: volumeNum / UNIT_VOLUME > index }"
          ></div>
          <div class="absolute" :style="{ left: `${80}%` }">
            <div class="suggestion-line bottom-0 top--10"></div>
            <div class="triangle absolute left--10"></div>
            <div
              class="absolute text-14 color-hex-666666 w-4 text-nowrap top-30 left--25 whitespace-nowrap"
            >
              建议音量
            </div>
            <div></div>
          </div>
        </div>
      </el-col>
    </el-row>

    <el-row :gutter="10" class="left-item">
      <el-col :span="5">
        <div class="left-title">选择扬声器</div>
      </el-col>
      <el-col :span="19">
        <el-select
          v-model="store.speakerId"
          class="select"
          placeholder="选择扬声器"
          @change="handleDeviceChange"
        >
          <el-option
            v-for="item in store.speakerList"
            :key="item.deviceId"
            :label="item.label"
            :value="item.deviceId"
          />
        </el-select>
        <div
          v-if="deviceLoaded && store.speakerList.length === 0"
          class="text-orange"
        >
          未检测到扬声器
        </div>
      </el-col>
    </el-row>
    <el-row :gutter="10" class="left-item">
      <el-col :span="5">
        <div class="left-title">扬声器试音</div>
      </el-col>
      <el-col :span="19">
        <el-button type="primary" plain class="w-full" @click="handlePlayAudio">
          请调高设备音量，点击播放音频试试~
        </el-button>
      </el-col>
    </el-row>
    <el-row justify="center">
      <el-button type="text" @click="updateDevice">刷新设备列表</el-button>
    </el-row>
  </div>
</template>

<style scoped lang="scss">
.left-title {
  @apply fw-400 text-14 color-hex-333333 font-leading-20 flex-y-center mt-5;
}
.left-item {
  @apply mb-30;
}

.mic-bar-container {
  display: flex;
  //justify-content: center;

  .mic-bar {
    width: 1rem;
    height: 4rem;
    border: 1px solid #cccccc;
    border-radius: 0.75rem;
    background-color: #e6e6e6;
    transition:
      height 0.1s ease-in-out,
      background-color 0.1s ease-in-out;
  }
  .mic-bar:not(:first-child) {
    margin-left: 1rem;
  }
  .mic-bar.active {
    background: #3686fd;
  }
  .suggestion-line {
    position: absolute;
    width: 0.25rem;
    height: 7rem;
    background-color: #3686fd;
  }

  .triangle {
    width: 0;
    height: 0;
    border-left: 1.5rem solid transparent; /* 左边透明 */
    border-right: 1.5rem solid transparent; /* 右边透明 */
    border-bottom: 1.5rem solid #3686fd; /* 三角形颜色 */
    margin: 20px auto; /* 上下间距，水平居中 */
    @apply ml-4;
  }
}
</style>
