<script lang="ts" setup>
import { ElMessageBox } from "element-plus";

// 定义话术项的接口
interface SpeechItem {
  id: string;
  nr: string;
  pagetime: number;
  khhd: string;
  waitTime: number;
  needAnswer: boolean;
  playMode: string;
  isSpecial: boolean;
}

// 组件属性
const props = defineProps<{
  visible: boolean;
  allSpeeches: SpeechItem[]; // 所有话术（包含普通和特殊）
}>();

// 组件事件
const emit = defineEmits<{
  "update:visible": [value: boolean];
  confirm: [selectedSpeeches: SpeechItem[]];
  cancel: [];
}>();

// 选中的特殊话术ID集合
const selectedSpecialIds = ref<Set<string>>(new Set());

// 计算属性：特殊话术列表
const specialSpeeches = computed(() => {
  return props.allSpeeches.filter((speech) => speech.isSpecial);
});

// 计算属性：普通话术列表
const normalSpeeches = computed(() => {
  return props.allSpeeches.filter((speech) => !speech.isSpecial);
});

// 处理特殊话术选择变化
const handleSpecialSpeechChange = (speechId: string, checked?: any) => {
  if (typeof checked === "boolean") {
    // 来自 checkbox 的变化
    if (checked) {
      selectedSpecialIds.value.add(speechId);
    } else {
      selectedSpecialIds.value.delete(speechId);
    }
  } else {
    // 来自整个框的点击，切换状态
    if (selectedSpecialIds.value.has(speechId)) {
      selectedSpecialIds.value.delete(speechId);
    } else {
      selectedSpecialIds.value.add(speechId);
    }
  }
};

// 确认选择
const handleConfirm = () => {
  // 组合普通话术和选中的特殊话术
  const selectedSpecialSpeeches = specialSpeeches.value.filter((speech) =>
    selectedSpecialIds.value.has(speech.id)
  );

  // 按原始顺序合并：普通话术在前，特殊话术插入到适当位置
  const finalSpeeches = [...normalSpeeches.value, ...selectedSpecialSpeeches];

  emit("confirm", finalSpeeches);
  emit("update:visible", false);
  selectedSpecialIds.value.clear();
};

// 取消选择
const handleCancel = () => {
  emit("cancel");
  emit("update:visible", false);
  selectedSpecialIds.value.clear();
};

// 关闭对话框
const handleClose = () => {
  if (selectedSpecialIds.value.size > 0) {
    ElMessageBox.confirm("您有未保存的选择，确认关闭吗？", "提示", {
      confirmButtonText: "确定",
      cancelButtonText: "取消",
      type: "warning",
    }).then(() => {
      handleCancel();
    });
  } else {
    handleCancel();
  }
};
</script>

<template>
  <el-dialog
    :model-value="visible"
    title="选择特殊话术"
    width="60%"
    :before-close="handleClose"
  >
    <div class="max-h-60vh overflow-y-auto">
      <div class="mb-16">
        <p class="text-16 text-[#333] mb-8">
          以下是可选的特殊话术，请选择需要在本次见证中使用的话术：
        </p>
        <p class="text-14 text-[#666]">
          选中的特殊话术将与标准话术一起组成最终的见证话术流程。
        </p>
      </div>

      <div class="max-h-400 overflow-y-auto">
        <div
          v-for="speech in specialSpeeches"
          :key="speech.id"
          class="border border-gray-200 rounded-8 p-12 mb-12 cursor-pointer transition-all duration-200 hover:border-[#409eff] hover:bg-gray-50 hover:shadow-[0_2px_8px_rgba(64,158,255,0.1)]"
          :class="{
            'border-[#409eff] bg-blue-50': selectedSpecialIds.has(speech.id),
          }"
          @click="handleSpecialSpeechChange(speech.id)"
        >
          <div class="flex items-start gap-12">
            <el-checkbox
              :model-value="selectedSpecialIds.has(speech.id)"
              class="mt-4 pointer-events-none"
            />
            <div class="flex-1 min-w-0">
              <div class="speech-content">
                <p class="text-14 text-[#333] mb-8 leading-1.5">
                  {{ speech.nr }}
                </p>
                <div class="flex gap-16 text-12 text-[#999]">
                  <span>
                    需要回答: {{ speech.khhd !== "无需" ? "是" : "否" }}
                  </span>
                  <span
                    v-if="speech.khhd && speech.khhd !== '无需'"
                    class="before:content-['·'] before:mr-8 before:text-[#ddd]"
                  >
                    客户回答: {{ speech.khhd }}
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div v-if="specialSpeeches.length === 0" class="text-center py-32">
          <p class="text-14 text-[#999]">暂无特殊话术</p>
        </div>
      </div>

      <div class="mt-16 px-12 py-12 bg-blue-50 rounded-8">
        <p class="text-12 text-[#666] flex items-center">
          <span class="i-ep-info-filled mr-8 text-[#409eff]"></span>
          提示：已选择 {{ selectedSpecialIds.size }} 项特殊话术。
          这些话术将与标准话术组合后传递给视频见证系统。
        </p>
      </div>
    </div>

    <template #footer>
      <div class="text-right">
        <el-button @click="handleCancel">取消</el-button>
        <el-button type="primary" @click="handleConfirm">
          确认选择 ({{ selectedSpecialIds.size }})
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>
