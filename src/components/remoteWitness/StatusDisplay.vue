<script setup lang="ts">
import DeviceDetection from "@/components/remoteWitness/DeviceDetection.vue";
import { Status } from "@/config/appConfig";
import "element-plus/es/components/dialog/style/index.mjs";
import "element-plus/es/components/message/style/index.mjs";

defineProps<{
  status: Status;
  formattedTime?: string;
}>();

const emit = defineEmits<{
  "show-settings": [];
}>();

// 设置弹窗状态
const settingsDialogVisible = ref(false);

const handleShowSettings = () => {
  settingsDialogVisible.value = true;
  emit("show-settings");
};

// 处理设备检测完成事件
const handleDeviceReady = (deviceInfo: any) => {
  console.log("设备检测完成:", deviceInfo);
  settingsDialogVisible.value = false;
};

// 处理设备异常事件
const handleDeviceError = (error: any) => {
  console.warn("设备异常:", error);
};
</script>

<template>
  <div class="status w-full flex-row flex-x-between p-10 border-shadow">
    <div class="flex-y-center">
      <div
        class="w-8 h-8 rounded-5"
        :class="
          Status.Recording === status
            ? 'bg-hex-FF4D4F'
            : Status.Idle === status
              ? 'bg-hex-52C41A'
              : 'bg-hex-3686FD'
        "
      ></div>
      <div
        class="pl-6 fw-400 text-14 color-hex-333333 font-leading-20 text-left"
      >
        {{
          Status.RecordEnd === status
            ? "录制结束"
            : Status.Recording === status
              ? "录制中"
              : Status.Idle === status
                ? "空闲等待中"
                : Status.Connected === status
                  ? "连接成功"
                  : "正在连接视频"
        }}
      </div>
    </div>
    <div
      v-if="Status.Recording == status || Status.RecordEnd === status"
      class="color-red text-14 pr-100"
    >
      {{ formattedTime }}
    </div>

    <div>
      <el-icon @click="handleShowSettings"><Setting /></el-icon>
    </div>
  </div>
  <div class="w-full bg-white p-10">
    <div class="bg-hex-999999 h-316 flex-center">
      <div v-if="status === Status.Idle" class="leftText">空闲等待中</div>
      <div v-if="status === Status.Connecting">
        <div class="leftText">客户已连接视频，等待接入中......</div>
      </div>
      <div
        v-if="
          [Status.Connected, Status.Recording, Status.RecordEnd].includes(
            status
          )
        "
        class="wh-full relative"
      >
        <div id="remote" class="wh-full"></div>
        <div id="local" class="absolute w-131 h-99 bottom-0 right-0"></div>
      </div>
    </div>
  </div>

  <!-- 设置弹窗 -->
  <el-dialog
    v-model="settingsDialogVisible"
    title="设备检测设置"
    width="1200px"
    :close-on-click-modal="false"
  >
    <div class="h-60vh">
      <DeviceDetection
        @device-ready="handleDeviceReady"
        @device-error="handleDeviceError"
      />
    </div>
  </el-dialog>
</template>

<style scoped lang="scss">
.border-shadow {
  border-bottom: 1px solid #ddd; /* 下边框颜色 */
  box-shadow: 0 1px 2px rgb(0 0 0 / 10%); /* 阴影效果 */
}

.leftText {
  @apply fw-400 text-14px color-hex-FFFFFF font-leading-20 text-left;
}
</style>
