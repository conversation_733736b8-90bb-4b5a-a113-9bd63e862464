<script setup lang="ts">
import { Status } from "@/config/appConfig";

defineProps<{
  status: Status;
  record: boolean;
  videoPreviewLoading?: boolean;
}>();

const emit = defineEmits<{
  "start-record": [];
  "stop-record": [];
  "video-preview": [];
}>();

const handleStartRecord = () => {
  emit("start-record");
};

const handleStopRecord = () => {
  emit("stop-record");
};

const searchMedia = () => {
  emit("video-preview");
};
</script>

<template>
  <div v-if="status != Status.Idle" class="flex-x-center">
    <el-button
      v-if="!record"
      type="primary"
      class="mr-30"
      :disabled="![Status.Connected, Status.RecordEnd].includes(status)"
      @click="handleStartRecord"
    >
      {{ status === Status.RecordEnd ? "重新录制" : "开始录制" }}
    </el-button>
    <el-button
      v-if="record"
      type="primary"
      class="mr-30"
      @click="handleStopRecord"
    >
      结束录制
    </el-button>
    <el-button
      type="primary"
      :loading="videoPreviewLoading"
      @click="searchMedia"
    >
      视频预览
    </el-button>
  </div>
</template>
