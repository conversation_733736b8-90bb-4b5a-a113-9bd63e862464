<script setup lang="ts">
import { DEVICE_INFO_KEY } from "@/enums/CacheEnum";
import { ElMessageBox } from "element-plus";
import "element-plus/es/components/message-box/style/index.mjs";

interface DeviceInfo {
  camera: { index: number; name: string };
  microphone: { index: number; name: string };
  speaker: { index: number; name: string };
  ttsData: any;
}

interface DeviceError {
  hasError: boolean;
  message: string;
}

const emit = defineEmits<{
  "device-ready": [deviceInfo: DeviceInfo]; // 设备检测完成且正常
  "device-error": [error: DeviceError]; // 设备异常
}>();

let messageHandler: ((event: MessageEvent) => void) | null = null;

onMounted(() => {
  // 创建消息处理函数
  messageHandler = (event: MessageEvent) => {
    const cmd = event.data.cmd;
    const msg = event.data.msg;

    switch (cmd) {
      case "CMDAnyChatAgentReady": {
        setTimeout(() => {
          const agentAppFrame = document.querySelector(
            "#agentApp"
          ) as HTMLIFrameElement;

          agentAppFrame?.contentWindow?.postMessage({
            cmd: "CMDAnyChatAgentInit",
            msg: {
              presetData: {
                config: {
                  appId: "95E61F1D-854C-8E41-8249-74BFE2E76D16",
                  serverIp: "dev.bairuitech.cn",
                  serverPort: "14603",
                },
                others: {
                  pluginDownload:
                    "/witness-agent/AnyChatChromeProxySetup_V9.5.0.R8.231019.exe",
                },
              },
            },
          });
        }, 100);
        break;
      }

      case "CMDAnyChatDeviceVoice": {
        console.log("设备检测结果:", msg);

        // 保存设备信息到 sessionStorage
        const deviceInfo: DeviceInfo = {
          camera: msg.camera,
          microphone: msg.microphone,
          speaker: msg.speaker,
          ttsData: msg.ttsData,
        };
        sessionStorage.setItem(DEVICE_INFO_KEY, JSON.stringify(deviceInfo));

        // 检查设备状态
        const hasCameraIssue = msg.camera && msg.camera.index === -1;
        const hasMicrophoneIssue =
          msg.microphone && msg.microphone.index === -1;
        const hasSpeakerIssue = msg.speaker && msg.speaker.index === -1;

        // 如果有设备异常
        if (hasCameraIssue || hasMicrophoneIssue || hasSpeakerIssue) {
          let errorMessage = "设备异常：\n";

          if (hasCameraIssue) {
            errorMessage += "• 摄像头未检测到或不可用\n";
          }
          if (hasMicrophoneIssue) {
            errorMessage += "• 麦克风未检测到或不可用\n";
          }
          if (hasSpeakerIssue) {
            errorMessage += "• 扬声器未检测到或不可用\n";
          }

          const deviceError: DeviceError = {
            hasError: true,
            message: errorMessage,
          };

          emit("device-error", deviceError);

          ElMessageBox.alert(errorMessage, "设备异常", {
            confirmButtonText: "确定",
            type: "error",
            center: true,
          });
          return; // 阻止后续处理
        }

        // 设备正常，触发 device-ready 事件
        emit("device-ready", deviceInfo);

        break;
      }
    }
  };

  // 添加消息监听
  window.addEventListener("message", messageHandler);
});

onUnmounted(() => {
  // 清理消息监听
  if (messageHandler) {
    window.removeEventListener("message", messageHandler);
  }
});
</script>

<template>
  <div class="wh-full">
    <iframe
      id="agentApp"
      class="w-full h-full"
      src="/witness-agent/anychatAgent/index.html#/?device=1"
    ></iframe>
  </div>
</template>
