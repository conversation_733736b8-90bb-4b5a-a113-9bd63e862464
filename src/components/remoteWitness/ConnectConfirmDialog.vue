<script setup lang="ts">
import picSPImg from "@/assets/<EMAIL>";
import { useDict } from "@/composables/useDict";
import { Status } from "@/config/appConfig";
import { usePwaitStore } from "@/store";
import useRemoteWitnessStore from "@/store/modules/remoteWitness";
import { getDictName } from "@/utils/dict";
import { convertToAnyChatTtsInfo } from "@/utils/speechFormatConverter";
import { computed, ref, watch } from "vue";
import SpecialSpeechDialog from "./SpecialSpeechDialog.vue";

const centerDialogVisible = defineModel("centerDialogVisible", {
  type: Boolean,
  default: false,
});

const pwaitStore = usePwaitStore();
const remoteWitnessStore = useRemoteWitnessStore();

// 使用 useDict hooks 获取字典数据
const { dictMap } = useDict("FQQD");

// 特殊话术弹窗状态
const specialSpeechDialogVisible = ref(false);

// 直接从store计算获取业务摘要
const clientUserBusinessSummary = computed(() => {
  const summaryArray = remoteWitnessStore.clientUserBusinessSummary || [];
  if (summaryArray.length === 0)
    return {
      khmc: "",
      ywmc: "",
      fqqd: "",
      yybmc: "",
    };

  // 其他字段取第一个元素，ywmc字段用顿号分隔拼接多个业务名称
  const firstSummary = summaryArray[0];
  const ywmcList = summaryArray.map((item) => item.ywmc).filter(Boolean);

  return {
    ...firstSummary,
    ywmc: ywmcList.join("、"), // 用顿号分隔多个业务名称
  };
});

function confirmAnswer() {
  if (!pwaitStore.bizRequestId) {
    console.error(
      "ConnectConfirmDialog: bizRequestId is not available in pwaitStore"
    );
    return;
  }
  remoteWitnessStore.fetchBusinessSummary(String(pwaitStore.bizRequestId)); // 确保传入string类型

  // 获取话术，传递业务类型和业务代码
  const bizType = pwaitStore.bizType || "1"; // 从 pwait store 获取业务类型，默认为 "1"
  const bizCode = pwaitStore.bizCode || ""; // 从 pwait store 获取业务代码

  console.log("confirmAnswer 调用获取话术:", { bizType, bizCode });

  // 调用获取话术接口
  remoteWitnessStore.getHsnr(bizType, bizCode);
  // 获取揭示信息
  remoteWitnessStore.getRevealInfo(bizCode, pwaitStore.initiateChannel);

  // 直接显示弢窗，数据已经在store中准备好了
  centerDialogVisible.value = true;
}

watch(
  () => pwaitStore.pwaitState,
  (newState) => {
    if (newState === "confirmConnect") {
      confirmAnswer();
    } else if (newState === "wait") {
      centerDialogVisible.value = false;
    }
  },
  { immediate: true }
);

const enterRoom = () => {
  // 先接受呼叫
  pwaitStore.acceptCall();

  // 检查是否有特殊话术需要选择
  const hasSpecialSpeeches = remoteWitnessStore.hsnr.some(
    (speech: any) => speech.isSpecial
  );

  if (hasSpecialSpeeches) {
    // 有特殊话术，显示选择弹窗
    specialSpeechDialogVisible.value = true;
  } else {
    // 没有特殊话术，直接处理话术并设置连接状态
    proceedToSetSpeeches(remoteWitnessStore.hsnr);
  }
};

// 处理特殊话术选择确认
const handleSpecialSpeechConfirm = (selectedSpeeches: any[]) => {
  proceedToSetSpeeches(selectedSpeeches);
};

// 处理特殊话术选择取消
const handleSpecialSpeechCancel = () => {
  // 取消选择，使用普通话术
  const normalSpeeches = remoteWitnessStore.hsnr.filter(
    (speech: any) => !speech.isSpecial
  );
  proceedToSetSpeeches(normalSpeeches);
};

// 设置话术并进入连接状态的逻辑
const proceedToSetSpeeches = (finalSpeeches: any[]) => {
  try {
    // 将话术转换为anychat格式
    const ttsInfo = convertToAnyChatTtsInfo(finalSpeeches);

    // 直接设置store中的话术数据
    remoteWitnessStore.finalSpeeches = [...finalSpeeches];
    remoteWitnessStore.ttsInfo = [...ttsInfo];

    console.log("最终话术列表:", finalSpeeches);
    console.log("转换后的ttsInfo:", ttsInfo);

    // 设置连接状态
    remoteWitnessStore.$patch({ callStatus: Status.Connecting });
    centerDialogVisible.value = false;
    specialSpeechDialogVisible.value = false;
  } catch (error) {
    console.error("处理话术时出错:", error);
    // 发生错误时仍然继续流程，但使用原始话术
    remoteWitnessStore.$patch({ callStatus: Status.Connecting });
    centerDialogVisible.value = false;
    specialSpeechDialogVisible.value = false;
  }
};

const rejectEnterRoom = () => {
  pwaitStore.rejectCall();
  centerDialogVisible.value = false;
};
</script>

<template>
  <div>
    <el-dialog
      v-model="centerDialogVisible"
      width="500"
      align-center
      :show-close="false"
      :close-on-click-modal="false"
      :destroy-on-close="true"
    >
      <template #header>
        <div
          class="w-full flex-row flex-y-center border-b-1 border-hex-DDDDDD pb-14"
        >
          <TagWithIndicator title="见证申请" />
        </div>
      </template>
      <div class="flex-y-center">
        <div class="">
          <img :src="picSPImg" class="w-59 h-55 mx-41" />
        </div>
        <div>
          <div
            class="fw-400 text-16 color-hex-20212B font-leading-22 text-left mb-16 flex-x-center"
          >
            {{ clientUserBusinessSummary.khmc }} 正在申请见证：{{
              clientUserBusinessSummary.ywmc
            }}
          </div>
          <el-row :gutter="50">
            <!-- <el-col :span="12">
            <div class="flex-y-center">
              <div class="">客户等级：</div>
              <div></div>
            </div>
          </el-col> -->
            <el-col :span="24">
              <div class="flex-y-center">
                <div>发起终端：</div>
                <div>
                  {{
                    getDictName(
                      dictMap,
                      "FQQD",
                      clientUserBusinessSummary.fqqd || ""
                    )
                  }}
                </div>
              </div>
            </el-col>
          </el-row>
          <el-row :gutter="20" class="mt-6">
            <el-col :span="24">
              <div class="flex-y-center">
                <div>营业部：</div>
                <div>{{ clientUserBusinessSummary.yybmc }}</div>
              </div>
            </el-col>
          </el-row>
        </div>
      </div>

      <template #footer>
        <div class="dialog-footer flex-x-center m-y-15">
          <el-button class="mr-30" @click="rejectEnterRoom">拒绝</el-button>
          <el-button type="primary" @click="enterRoom">接受</el-button>
        </div>
      </template>
    </el-dialog>
    <!-- 原有的确认连接弹窗 -->
    <!-- <CommonDialog
      v-model="centerDialogVisible"
      title="确认连接"
      width="500px"
      center
      :show-close="false"
      :close-on-click-modal="false"
    >
      <div class="flex-center p-30">
        <div class="mr-20">
          <img :src="picSPImg" alt="视频图标" class="w-60 h-60" />
        </div>
        <div class="flex-1">
          <div class="color-hex-333 text-16 fw-500 mb-8">确认视频见证</div>
          <div class="color-hex-666 text-14 lh-20">
            客户
            <span class="color-hex-333 fw-500">
              {{ clientUserBusinessSummary.khmc }}
            </span>
            申请办理
            <span class="color-hex-333 fw-500">
              {{ clientUserBusinessSummary.ywmc }}
            </span>
            业务，发起地区为
            <span class="color-hex-333 fw-500">
              {{ getDictName(dictMap, "FQQD", clientUserBusinessSummary.fqqd) }}
            </span>
            ，归属营业部为
            <span class="color-hex-333 fw-500">
              {{ clientUserBusinessSummary.yybmc }}
            </span>
            ，是否接受此次见证申请？
          </div>
        </div>
      </div>
      <template #footer>
        <div class="flex-center gap-20">
          <el-button @click="rejectEnterRoom">拒绝</el-button>
          <el-button type="primary" @click="enterRoom">接受</el-button>
        </div>
      </template>
    </CommonDialog> -->

    <!-- 特殊话术选择弹窗 -->
    <SpecialSpeechDialog
      v-model:visible="specialSpeechDialogVisible"
      :all-speeches="remoteWitnessStore.hsnr as any"
      @confirm="handleSpecialSpeechConfirm"
      @cancel="handleSpecialSpeechCancel"
    />
  </div>
</template>
