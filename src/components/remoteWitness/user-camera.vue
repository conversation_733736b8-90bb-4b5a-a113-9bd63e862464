<script setup lang="ts">
import remoteWitnessApi from "@/api/remote-witness";
import User<PERSON><PERSON> from "@/api/user";
import { Status } from "@/config/appConfig";
import { usePwaitStore } from "@/store";
import useRemoteWitnessStore from "@/store/modules/remoteWitness";
import { ElMessage } from "element-plus";
import TRTC from "trtc-sdk-v5";
import { nextTick, ref } from "vue";
import { useI18n } from "vue-i18n";

import ClientApi from "@/api/client";

import Buttons from "@/components/remoteWitness/Buttons.vue";
import StatusDisplay from "@/components/remoteWitness/StatusDisplay.vue";
import { useFaceDetection } from "@/composables/useFaceDetection";
import { WebSocketAction } from "@/enums/WebSocketActionEnum";
import { HSNRItem } from "@/types/type";
import WebSocketSingleton from "@/utils/WebSocketSingleton";

const pwaitStore = usePwaitStore();

const playIndex = ref(0);
// const handAudioEnd = () => {
//   const ws = WebSocketSingleton.getInstance();
//
//   if (
//     playIndex.value !== null &&
//     props.hsnr[playIndex.value]?.khhd &&
//     !props.hsnr[playIndex.value]?.khhd?.includes("无需")
//   ) {
//     ws.publish({
//       destination: "/message",
//       body: JSON.stringify({
//         recipient: store.connectId,
//         content: JSON.stringify({
//           action: WebSocketAction.ASR,
//           content: props.hsnr[playIndex.value]?.khhd,
//         }),
//       }),
//     });
//   } else {
//     // 播放下一段话术
//     if (playIndex.value < props.hsnr.length - 1) {
//       playIndex.value++;
//       playAudio(props.hsnr[playIndex.value].nr, trtc);
//     }
//   }
// };
const props = defineProps<{
  hsnr: HSNRItem[];
}>();
const { t } = useI18n();
const elapsedSeconds = ref<number>(0); // 用于存储经过的秒数
let timer: ReturnType<typeof setInterval> | null = null; // 计时器的类型
const {
  detectFaces,
  startCapture,
  detectionHistory,
  isProcessing,
  sendReferenceImage,
  qualityAlert,
  clearupFaceDetection,
} = useFaceDetection();

const startTimer = (): void => {
  if (timer) return;
  timer = store.startTimer();
};

// 使用 defineModel 接收父组件传入的 v-model 状态，并自动实现双向绑定
const status = defineModel<Status>({
  required: true, // 确保外部传入值
});

const showSettingDialog = ref(false);
const centerDialogVisible = ref(false);
const store = useRemoteWitnessStore();
const route = useRoute();

const ywqqid: string = route.query.ywqqid as string;
let token: string = store.socketToken;
const userSig: string = route.query.userSig as string;
const SDKAppId: string = route.query.SDKAppId as string;
const host = window.location.hostname; // 获取主机名，例如 'localhost'
const port = 8090;
const socket = ref<any>(null); // 建立和 pad 端的连接
const emit = defineEmits(["update:trtc", "stopRecord"]);
const trtc = TRTC.create();
console.log("子组件 emit trtc", trtc); // 检查此处是否打印
const mediaUrl = ref("");
const showVideoPreview = ref(false);
const coverUrl = ref("");
// status
const roomStatus = ref("exited"); // exited, exiting, entering, entered
const camStatus = ref("stopped"); // stopped, starting, started, stopping
const micStatus = ref("stopped");
const shareStatus = ref("stopped"); // stopping, stopped, sharing, shared

const audioMuted = ref(false);
const videoMuted = ref(false);
const record = ref(false);
const historyList = ref();
const lastItem = ref();
let taskId = "";
let cleanupFaceDetect: (() => void) | undefined;

function socketRecordStatusSync(action: WebSocketAction) {
  const ws = WebSocketSingleton.getInstance();
  ws.publish({
    destination: "/message",

    body: JSON.stringify({
      recipient: store.connectId,
      content: JSON.stringify({
        action: action,
      }),
    }),
  });
}
const handleStartRecord = async () => {
  // if (import.meta.env.MODE === "development") {
  if (import.meta.env.MODE === "production") {
    // 生产环境才调用录制接口
    const result = await UserAPI.startRecord(store.roomId, store.connectId);
    record.value = true;
    status.value = Status.Recording;
    startTimer();
    store.$patch({
      detectionHistory: [],
    }); //重新录制清空检测记录
    store.submitDetectionResult({
      videoDetection: null,
    }); //重新录制清空视频检测结果

    taskId = result.taskId;
    console.log(result);
  } else {
    startTimer();
    record.value = true;
    status.value = Status.Recording;
    store.$patch({
      detectionHistory: [],
    }); //重新录制清空检测记录
    store.submitDetectionResult({
      videoDetection: null,
    }); //重新录制清空视频检测结果
  }
  socketRecordStatusSync(WebSocketAction.START_RECORD); //开始录制计时，通知 pad 端开始录制了
  cleanupFaceDetect = await startDetectFace();
};
const handleStopRecord = async () => {
  //结束录制暴露事件，可以在父组件中监听，可以用来结束话术播放
  emit("stopRecord");
  socketRecordStatusSync(WebSocketAction.STOP_RECORD);
  if (import.meta.env.MODE === "production") {
    const result = await UserAPI.stopRecord(taskId);
    console.log(result);
  }
  record.value = false;

  if (timer) {
    clearInterval(timer);
    timer = null;
  }
  status.value = Status.RecordEnd;

  // 结束录制，不清理资源，解决结束录制后坐席看不到客户画面的问题
  // cleanupFaceDetect && cleanupFaceDetect(); // 这个清理会把视频也关掉
  clearupFaceDetection(); // 清理人脸检测资源

  //落地数据
  await ClientApi.modifyWitness({
    id: Number(store.roomId || 115),
    markerData: JSON.stringify({
      detectionHistory: detectionHistory.value,
      hsnr: props.hsnr,
    }),
  });
  ElMessageBox.alert("已完成见证视频录制，请审核!", "录制提示", {
    type: "warning",
    confirmButtonText: "已知晓",
  });
  // await handleExit();
};

const videoPreviewLoading = ref(false);
async function searchMedia() {
  videoPreviewLoading.value = true; // 开始加载，设置 loading 状态为 true

  // 定义轮询函数
  const pollMedia = async () => {
    const res = await UserAPI.searchMedia(store.roomId);
    console.log("🚀 ~ searchMedia ~ res:", res);

    // 更新当前值
    mediaUrl.value = res.mediaUrl || "";
    coverUrl.value = res.coverUrl || "";

    // 检查是否 mediaUrl 和 coverUrl 都有值
    if (res.mediaUrl && res.coverUrl) {
      // 如果都有值，结束轮询，更新 loading 状态
      videoPreviewLoading.value = false;
      // 只有在获取成功后才显示视频预览弹窗
      showVideoPreview.value = true;
      return true;
    }
    return false;
  };

  // 首次尝试获取
  let isComplete = await pollMedia();

  // 如果首次获取未成功，开始轮询
  if (!isComplete) {
    // 显示轮询开始的提示信息
    ElMessage({
      message: "视频合成中，请稍候...",
      type: "info",
      duration: 3000,
    });
    // 最多轮询 30 次，每次间隔 2 秒，总共最多等待 60 秒
    let attempts = 0;
    const maxAttempts = 30;
    const interval = 2000; // 2 秒

    const polling = setInterval(async () => {
      attempts++;

      try {
        isComplete = await pollMedia();

        // 如果获取成功或达到最大尝试次数，清除轮询
        if (isComplete || attempts >= maxAttempts) {
          clearInterval(polling);

          // 如果达到最大尝试次数但仍未获取到，也结束 loading 状态
          if (!isComplete && attempts >= maxAttempts) {
            videoPreviewLoading.value = false;
            console.warn("视频预览链接获取超时");
            ElMessage({
              message: "视频预览链接获取超时，请稍后再试",
              type: "warning",
              duration: 3000,
            });
          }
        }
      } catch (error) {
        console.error("轮询视频预览链接出错:", error);
        clearInterval(polling);
        videoPreviewLoading.value = false;
        ElMessage({
          message: "获取视频预览出错，请稍后再试",
          type: "error",
          duration: 3000,
        });
      }
    }, interval);
  }
}

watch(
  status,
  (newval) => {
    if (newval === Status.Idle) {
      handleExit();
    }
  },
  { immediate: true }
);
watch(
  detectionHistory,
  (val) => {
    nextTick(() => {
      scrollToBottom();
    });
  },
  { deep: true, immediate: true }
);
function scrollToBottom() {
  const lastItems = historyList.value?.querySelectorAll(".time-container");
  if (lastItems && lastItems.length > 0) {
    lastItems[lastItems.length - 1].scrollIntoView({ behavior: "smooth" });
  }
}
async function startDetectFace() {
  // 1. 获取视频轨道并创建视频元素
  const video = document.createElement("video");
  let sourceVideoTrack = trtc.getVideoTrack({ userId: store.connectId });
  // let sourceVideoTrack = trtc.getVideoTrack();
  if (!sourceVideoTrack) {
    throw new Error("获取视频轨道失败");
  }

  // 2. 设置视频流
  const mediaStream = new MediaStream();
  mediaStream.addTrack(sourceVideoTrack);
  video.srcObject = mediaStream;
  await video.play();
  if (store.gongAnFilepath || store.idCardFilepath) {
    //有公安图就优先使用，否则使用身份证人像图
    await sendReferenceImage(store.gongAnFilepath || store.idCardFilepath);
  }
  await detectFaces(video, true);
  await startCapture(video);

  // 返回清理函数
  return () => {
    // 停止视频播放
    video.pause();
    // 清理视频源
    video.srcObject = null;
    // 移除视频元素
    video.remove();
    // 停止媒体流中的所有轨道
    mediaStream.getTracks().forEach((track) => track.stop());
    clearupFaceDetection();
  };
}

async function handleStopLocalAudio() {
  if (micStatus.value !== "started") {
    return;
  }
  micStatus.value = "stopping";
  try {
    await trtc.stopLocalAudio();
    micStatus.value = "stopped";
  } catch (error: any) {
    micStatus.value = "started";
  }
}

async function handleStopLocalVideo() {
  if (camStatus.value !== "started") {
    return;
  }
  camStatus.value = "stopping";
  try {
    await trtc.stopLocalVideo();
    camStatus.value = "stopped";
  } catch (error: any) {
    camStatus.value = "started";
  }
}

async function handleStopShare() {
  if (shareStatus.value !== "shared") {
    ElMessage({
      message: "stopScreenShare() - please start firstly",
      type: "warning",
    });
    return;
  }
  shareStatus.value = "stopping";
  try {
    await trtc.stopScreenShare();
    shareStatus.value = "stopped";
  } catch (error: any) {
    shareStatus.value = "shared";
  }
}
async function handleExit() {
  if (roomStatus.value !== "entered") {
    return;
  }
  roomStatus.value = "exiting";
  record.value = false;
  store.getHsnr(); // 退出后重新获取话术
  try {
    uninstallEventHandlers();
    await trtc.exitRoom();
    await trtc.stopLocalVideo();
    await trtc.stopLocalAudio();
    roomStatus.value = "exited";
    store.remoteUsersViews = [];
  } catch (error: any) {
    roomStatus.value = "entered";
  }

  if (micStatus.value === "started") handleStopLocalAudio();
  if (camStatus.value === "started") handleStopLocalVideo();
  if (shareStatus.value === "shared") handleStopShare();
  await UserAPI.dissmissRoom(store.roomId);
}

watch(
  () => pwaitStore.pwaitState,
  (newV) => {
    console.log("pwaitState", newV);
    if (newV === "confirmConnect") {
      confirmAnswer();
    } else if (newV === "wait") {
      centerDialogVisible.value = false;
      clientUserInfo.value = {};
    }
  }
);

// watch(
//   () => pwaitStore.applyReceivedMsg,
//   (newV) => {
//     if (newV === AgentEvents.EndCall) {
//       centerDialogVisible.value = false;
//       clientUserInfo.value = {};
//     }
//   }
// );

const clientUserInfo = ref<Record<string, any>>({});
const clientUserBusinessSummary = ref<Record<string, any>>({});
async function confirmAnswer() {
  const data = await remoteWitnessApi.getBusinessData(pwaitStore.bizRequestId);
  const data2 = await remoteWitnessApi.getBusinessSummary(
    pwaitStore.bizRequestId
  );
  clientUserBusinessSummary.value = data2;
  clientUserInfo.value = data.BusinessDataSupplement || data.BusinessData; // 优先使用BusinessDataSupplement的数据

  centerDialogVisible.value = true;
}

async function setupWebSocket(token: string) {
  const ws = WebSocketSingleton.getInstance();

  try {
    // 建立连接
    const client = await ws.connect(token, true);

    console.log("WebSocket connection established.");

    // 订阅消息
    ws.subscribe("/message", (message) => {
      const result = JSON.parse(message);
      if (result.action === WebSocketAction.ENTER_ROOM) {
        store.$patch({ connectId: result.userId });
        // centerDialogVisible.value = true;
      }
    });
  } catch (error) {
    console.error("WebSocket connection error:", error);
  }
}
function rejectEnterRoom() {
  centerDialogVisible.value = false;
  pwaitStore.rejectCall();
}
async function enterRoom() {
  centerDialogVisible.value = false;
  pwaitStore.acceptCall();

  // const ws = WebSocketSingleton.getInstance();
  // // const roomId = parseInt(getParamKey("roomId"), 10).toString();
  const roomId = pwaitStore.witnessRequestId + "";
  // const userId = pwaitStore.clientId + "";

  // await ws.publish({
  //   destination: "/message",

  //   body: JSON.stringify({
  //     recipient: store.connectId,
  //     content: JSON.stringify({
  //       action: WebSocketAction.ENTER_ROOM,
  //       roomId: roomId,
  //     }),
  //   }),
  // });
  store.$patch({ roomId });
  await handleEnter();
}
let onPlayNextCallback: ((index: number) => void) | null = null;

function onPlayNext(callback: (index: number) => void) {
  onPlayNextCallback = callback;
}

async function handleEnter() {
  if (!store.getInitParamsStates()) {
    ElMessage({ message: t("paramsNeed"), type: "error" });
    return;
  }
  roomStatus.value = "entering";
  status.value = Status.Connecting;
  try {
    await trtc.enterRoom({
      roomId: parseInt(store.roomId, 10),
      sdkAppId: parseInt(store.sdkAppId, 10),
      userId: store.userId,
      userSig: await store.getUserSig(),
    });
    roomStatus.value = "entered";
    status.value = Status.Connected;
    installEventHandlers();

    await handleStartLocalAudio();
    await handleStartLocalVideo();
  } catch (error: any) {
    roomStatus.value = "stopped";
  }
}
onUnmounted(() => {
  const ws = WebSocketSingleton.getInstance();
  ws.disconnect();
  cleanupFaceDetect && cleanupFaceDetect();
  clearInterval(detectionInterval);
});
async function handleStartLocalAudio() {
  micStatus.value = "starting";
  try {
    await trtc.startLocalAudio({
      option: {
        microphoneId: store.audioDeviceId,
      },
    });
    audioMuted.value = false;
    micStatus.value = "started";
  } catch (error: any) {
    micStatus.value = "stopped";
  }
}
async function handleStartLocalVideo() {
  camStatus.value = "starting";
  try {
    await trtc.startLocalVideo({
      view: "local",
      option: {
        cameraId: store.videoDeviceId,
        profile: "1080p",
      },
    });
    await TRTC.setCurrentSpeaker(store.speakerId);
    videoMuted.value = false;
    camStatus.value = "started";
  } catch (error: any) {
    camStatus.value = "stopped";
  }
}
function installEventHandlers() {
  trtc.on(TRTC.EVENT.ERROR, handleError);
  trtc.on(TRTC.EVENT.KICKED_OUT, handleKickedOut);
  trtc.on(TRTC.EVENT.REMOTE_USER_ENTER, handleRemoteUserEnter);
  trtc.on(TRTC.EVENT.REMOTE_USER_EXIT, handleRemoteUserExit);
  trtc.on(TRTC.EVENT.REMOTE_VIDEO_AVAILABLE, handleRemoteVideoAvailable);
  trtc.on(TRTC.EVENT.REMOTE_VIDEO_UNAVAILABLE, handleRemoteVideoUnavailable);
  trtc.on(TRTC.EVENT.REMOTE_AUDIO_UNAVAILABLE, handleRemoteAudioUnavailable);
  trtc.on(TRTC.EVENT.REMOTE_AUDIO_AVAILABLE, handleRemoteAudioAvailable);
  trtc.on(TRTC.EVENT.SCREEN_SHARE_STOPPED, handleScreenShareStopped);
}

function uninstallEventHandlers() {
  trtc.off(TRTC.EVENT.ERROR, handleError);
  trtc.off(TRTC.EVENT.KICKED_OUT, handleKickedOut);
  trtc.off(TRTC.EVENT.REMOTE_USER_ENTER, handleRemoteUserEnter);
  trtc.off(TRTC.EVENT.REMOTE_USER_EXIT, handleRemoteUserExit);
  trtc.off(TRTC.EVENT.REMOTE_VIDEO_AVAILABLE, handleRemoteVideoAvailable);
  trtc.off(TRTC.EVENT.REMOTE_VIDEO_UNAVAILABLE, handleRemoteVideoUnavailable);
  trtc.off(TRTC.EVENT.REMOTE_AUDIO_UNAVAILABLE, handleRemoteAudioUnavailable);
  trtc.off(TRTC.EVENT.REMOTE_AUDIO_AVAILABLE, handleRemoteAudioAvailable);
  trtc.off(TRTC.EVENT.SCREEN_SHARE_STOPPED, handleScreenShareStopped);
}
let detectionInterval: number;
onMounted(async () => {
  if (!token) {
    await store.createInviteLink();
    token = store.socketToken;
  }
  emit("update:trtc", trtc);
  await setupWebSocket(token);
});

function handleError(error: any) {
  ElMessage({ message: `Local error: ${error.message}`, type: "error" });
  // addFailedLog(`Local error: ${error.message}`);
}

function handleKickedOut(event: any) {
  ElMessage({
    message: `User has been kicked out for ${event.reason}`,
    type: "warning",
  });
  // addFailedLog(`User has been kicked out for ${event.reason}`);
}

function handleRemoteUserEnter(event: any) {
  const { userId } = event;
  // addSuccessLog(`Remote User [${userId}] entered`);
}

function handleRemoteUserExit(event: any) {
  // addSuccessLog(`Remote User [${event.userId}] exit`);
  ElMessage.warning("客户已离开视频房间");
  status.value = Status.Idle;
  pwaitStore.unpass("客户已离开视频房间");
}

async function handleRemoteVideoAvailable(event: any) {
  const { userId, streamType } = event;
  try {
    // addSuccessLog(`[${userId}] [${streamType}] video available`);
    if (streamType === TRTC.TYPE.STREAM_TYPE_MAIN) {
      (store.remoteUsersViews as string[]).push(`${userId}_main`);
      await nextTick();
      await trtc.startRemoteVideo({
        userId,
        streamType,
        view: `remote`,
        option: {
          fillMode: "contain",
        },
      });
    } else {
      // trtc.startRemoteVideo({ userId, streamType, view: `${userId}_${streamType}` });
      store.remoteUsersViews.push(`${userId}_screen`);
      await nextTick();
      trtc.startRemoteVideo({
        userId,
        streamType,
        view: `sub_stream`,
        option: {
          fillMode: "contain",
        },
      });
    }

    store.addSuccessLog(`Play remote video success: [${userId}]`);
  } catch (error: any) {
    store.addFailedLog(
      `Play remote video failed: [${userId}], error: ${error.message}`
    );
  }
}

async function handleRemoteVideoUnavailable(event: any) {
  // addSuccessLog(`[${event.userId}] [${event.streamType}] video unavailable`);
  const { streamType } = event;
  trtc.stopRemoteVideo({ userId: event.userId, streamType });
  if (streamType === TRTC.TYPE.STREAM_TYPE_MAIN) {
    store.remoteUsersViews = store.remoteUsersViews.filter(
      (userId: string) => userId !== `${event.userId}_main`
    );
    clearupFaceDetection && clearupFaceDetection();
  } else {
    store.remoteUsersViews = store.remoteUsersViews.filter(
      (userId: string) => userId !== `${event.userId}_screen`
    );
  }
}

function handleRemoteAudioUnavailable(event: any) {
  // addSuccessLog(`[${event.userId}] audio unavailable`);
}

function handleRemoteAudioAvailable(event: any) {
  // addSuccessLog(`[${event.userId}] audio available`);
}

function handleScreenShareStopped() {
  shareStatus.value = "stopped";
  // addSuccessLog("Stop share screen success");
}
</script>

<template>
  <div class="wh-full flex flex-col">
    <!-- 连接确认弹窗 -->
    <ConnectConfirmDialog
      v-model:centerDialogVisible="centerDialogVisible"
      :client-user-info="clientUserInfo"
      :client-user-business-summary="clientUserBusinessSummary"
      @enter-room="enterRoom"
      @reject-enter-room="rejectEnterRoom"
    />

    <!-- 状态展示，视频容器 -->
    <StatusDisplay
      :status="status"
      :formatted-time="store.formattedTime"
      @show-settings="showSettingDialog = true"
    />

    <!-- 操作按钮 -->
    <Buttons
      :status="status"
      :record="record"
      :video-preview-loading="videoPreviewLoading"
      @start-record="handleStartRecord"
      @stop-record="handleStopRecord"
      @video-preview="searchMedia"
    />

    <!-- 实时质检 -->
    <RealtimeDetection :status="status" />

    <!-- 视频预览 -->
    <VideoPreviewDialog
      v-model:visible="showVideoPreview"
      :video-url="mediaUrl"
      title="视频预览"
      :hsnr="props.hsnr"
    />

    <!-- 设备检测 -->
    <CommonDialog
      v-model="showSettingDialog"
      title="设备选择"
      width="500"
      :hide-footer="true"
      @close="showSettingDialog = false"
    >
      <div class="overflow-y-auto flex-center">
        <webrtc-check />
      </div>
    </CommonDialog>
  </div>
</template>
