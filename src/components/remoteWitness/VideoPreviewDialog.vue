<script setup lang="ts">
import { HSNRItem } from "@/types/type";
import "@tower1229/videojs-plugin-marker";
import "@tower1229/videojs-plugin-marker/dist/style.css";
import "@videojs/http-streaming";
import "video.js/dist/video-js.css";
import VideoPreview from "./VideoPreview.vue";
// import "videojs-contrib-hls";
// 定义 props 类型
interface Props {
  visible: boolean;
  videoUrl: string;
  title?: string;
  hsnr?: HSNRItem[];
}
interface Marker {
  offset: number;
  data: {
    content: string;
  };
}
const dialogWidth = computed(() => {
  return isMobileApp.value || window.innerWidth <= 1024 ? "92%" : "70%";
});
const isMobileApp = computed(() => {
  const ua = window.navigator.userAgent.toLowerCase();
  return /iphone|ipad|android/.test(ua);
});
// 使用 withDefaults 设置默认值
const props = withDefaults(defineProps<Props>(), {
  title: "视频预览",
  visible: false,
});

// 定义 emit 事件
const emit = defineEmits<{
  (e: "update:visible", value: boolean): void;
}>();

// 处理关闭事件
const handleClose = () => {
  emit("update:visible", false);
};

// 更新可见性
const updateVisible = (value: boolean) => {
  emit("update:visible", value);
};
</script>

<template>
  <el-dialog
    :model-value="visible"
    :width="dialogWidth"
    :destroy-on-close="true"
    class="video-preview-dialog"
    @close="handleClose"
    @update:model-value="updateVisible"
  >
    <template #header>
      <tag-with-indicator :title="title" />
    </template>
    <VideoPreview
      class="video-preview-dialog"
      :video-url="props.videoUrl"
      :hsnr="props.hsnr"
      :visible="props.visible"
    />
  </el-dialog>
</template>

<style scoped>
.video-preview-dialog {
  :deep(.video-preview-container) {
    @apply w-486 h-483;
  }
}
</style>
