<script setup lang="ts">
import { ref, computed, watch, onUnmounted } from "vue";
import { COLORS, DEFAULTS } from "@/config/voice-assistant";

export interface VoiceAssistantProps {
  duration?: number; // 倒计时持续时间
  strokeWidth?: number; // 圆环宽度
  size?: number; // 组件大小
  iconSize?: number; // 内部图标大小
  updateInterval?: number; // 动画更新频率
  onClick?: () => {};
}

const props = withDefaults(defineProps<VoiceAssistantProps>(), {
  duration: DEFAULTS.DURATION,
  strokeWidth: DEFAULTS.STROKE_WIDTH,
  size: DEFAULTS.SIZE,
  iconSize: DEFAULTS.ICON_SIZE,
  updateInterval: DEFAULTS.UPDATE_INTERVAL,
});
const progress = ref(100);
const isActive = ref(false);

const circumference = computed(() => 2 * Math.PI * DEFAULTS.CIRCLE_RADIUS);
const offset = computed(
  () => circumference.value - (progress.value / 100) * circumference.value
);

const containerStyle = computed(() => ({
  width: `${props.size}px`,
  height: `${props.size}px`,
}));

const iconContainerStyle = computed(() => ({
  width: `${props.iconSize + 10}px`,
  height: `${props.iconSize + 10}px`,
}));

let timer: number | null = null;

const startCountdown = () => {
  isActive.value = true;
  progress.value = 100;
};

watch(isActive, (newValue) => {
  if (newValue && progress.value > 0) {
    timer = window.setInterval(() => {
      progress.value -= (props.updateInterval / props.duration) * 100;
      if (progress.value <= 0) {
        progress.value = 0;
        isActive.value = false;
        if (timer) clearInterval(timer);
      }
    }, props.updateInterval);
  }
});
defineExpose({ startCountdown });
onUnmounted(() => {
  if (timer) clearInterval(timer);
});
const viewBoxSize = (DEFAULTS.CIRCLE_RADIUS + 8) * 2;
const viewBoxPosition = viewBoxSize / 2;
const viewBox = `0 0 ${viewBoxSize} ${viewBoxSize}`;
</script>

<template>
  <div
    class="relative flex items-center justify-center cursor-pointer"
    :style="containerStyle"
    @click="$emit('click', $event)"
  >
    <div class="relative" :style="iconContainerStyle">
      <!-- Progress ring -->
      <svg :viewBox="viewBox" class="absolute inset-0 w-full h-full -rotate-90">
        <defs>
          <linearGradient id="circleGradient">
            <stop offset="0%" :stop-color="COLORS.GRADIENT.START" />
            <stop offset="100%" :stop-color="COLORS.GRADIENT.END" />
          </linearGradient>
        </defs>
        <defs>
          <linearGradient id="circleGradientopacity">
            <stop
              offset="0%"
              :stop-color="COLORS.GRADIENT.START"
              stop-opacity="0.1"
            />
            <stop
              offset="100%"
              :stop-color="COLORS.GRADIENT.END"
              stop-opacity="0.1"
            />
          </linearGradient>
        </defs>
        <!-- Background circle -->
        <circle
          :cx="viewBoxPosition"
          :cy="viewBoxPosition"
          :r="DEFAULTS.CIRCLE_RADIUS"
          fill="none"
          :stroke="COLORS.BACKGROUND_CIRCLE"
          :stroke-width="strokeWidth"
        />
        <!-- Progress circle -->
        <circle
          :cx="viewBoxPosition"
          :cy="viewBoxPosition"
          :r="DEFAULTS.CIRCLE_RADIUS"
          fill="none"
          stroke="url(#circleGradient)"
          :stroke-width="strokeWidth"
          :stroke-dasharray="circumference"
          :stroke-dashoffset="offset"
          stroke-linecap="round"
          class="transition-all duration-50 ease-linear"
        />
        <circle
          :cx="viewBoxPosition"
          :cy="viewBoxPosition"
          fill="none"
          :r="DEFAULTS.CIRCLE_RADIUS - 14"
          stroke="url(#circleGradientopacity)"
          :stroke-width="DEFAULTS.CIRCLE_RADIUS - 30"
        />
        <circle
          :cx="viewBoxPosition"
          :cy="viewBoxPosition"
          fill="none"
          :r="viewBoxPosition - 2"
          stroke="url(#circleGradientopacity)"
          stroke-width="4"
        />
      </svg>

      <!-- Microphone icon -->
      <div class="absolute inset-4 flex items-center justify-center">
        <svg
          :viewBox="`0 0 ${DEFAULTS.ICON_VIEW_BOX} ${DEFAULTS.ICON_VIEW_BOX}`"
          class="w-40 h-40"
          fill="none"
        >
          <defs>
            <linearGradient id="gradient" x1="0%" y1="0%" x2="100%" y2="100%">
              <stop offset="0%" stop-color="#F15CA9" />
              <stop offset="100%" stop-color="#16B0FF" />
            </linearGradient>
          </defs>

          <path
            d="M12 2a3 3 0 0 0-3 3v7a3 3 0 0 0 6 0V5a3 3 0 0 0-3-3z"
            fill="url(#gradient)"
          />
          <path
            d="M19 10v2a7 7 0 0 1-14 0v-2M12 19v3"
            stroke="url(#gradient)"
            stroke-width="6"
            stroke-linecap="round"
            stroke-linejoin="round"
          />
        </svg>
      </div>
    </div>
  </div>
</template>
