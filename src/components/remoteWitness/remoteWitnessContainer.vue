<script setup lang="ts">
import { defineProps } from "vue";

// 定义 props
defineProps<{
  bgColor?: string; // 可选，背景色类型为字符串
}>();
</script>

<template>
  <div class="page">
    <div class="content" :style="{ backgroundColor: bgColor }">
      <slot></slot>
    </div>
  </div>
</template>

<style scoped lang="scss">
.page {
  @apply wh-full;
  // width: 100vw;
  height: calc(100vh - 18.75rem);
  background-color: #f5f5f5;
  padding: 2.5rem;
  overflow: auto;
}
.content {
  @apply wh-full rounded-8;
  background-color: #ffffff;
}
</style>
