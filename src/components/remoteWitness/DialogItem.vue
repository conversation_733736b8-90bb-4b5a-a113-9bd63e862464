<script setup lang="ts">
import { VideoPlay } from "@element-plus/icons-vue";
// import { formattedTime } from "../../store";
import { formattedTime } from "@/store";
defineProps<{
  timestamp: number | undefined;
  role: string;
  content: string | undefined;
  index: number;
  textClass?: string;
  roleClass?: string;
}>();

defineEmits<{
  (e: "jump", timestamp: number | undefined): void;
}>();
</script>

<template>
  <div class="flex items-start mb-14">
    <div
      class="shrink-0 w-80 h-24 relative cursor-pointer"
      @click="$emit('jump', timestamp)"
    >
      <div class="time-tag-hover rounded-12 flex-center">
        <el-icon class="text-white"><video-play /></el-icon>
      </div>
      <div class="time-tag bg-hex-F2F3F5 flex-center">
        {{ timestamp ? formattedTime(timestamp) : "--:--" }}
      </div>
    </div>
    <div class="dialog-content mt-2">
      <div class="shrink-0" :class="[roleClass ? roleClass : 'dialog-role']">
        {{ role }}：
      </div>
      <div
        class="dialog-text break-words whitespace-normal flex-1 min-w-0"
        :class="textClass"
      >
        {{ content }}
      </div>
    </div>
  </div>
</template>
<style scoped>
.customer-text {
}
.time-tag,
time-tag-hover {
  @apply cursor-pointer transition-all duration-300 w-80 h-24  rounded-12;
}

.time-tag-hover {
  @apply bg-[#3787FD] transition-all duration-300   rounded-12 absolute top-0 left-0 right-0 bottom-0 opacity-0;
}
.time-tag-hover:hover {
  @apply opacity-100;
}

.time-tag:hover {
}

.time-tag-text {
  @apply transition-all duration-300;
}
.dialog-content {
  @apply flex flex-row ml-5 w-full min-w-0;
}

.dialog-role {
  @apply text-[#333333] text-14 w-45;
}
</style>
