<script setup lang="ts">
import { useTTSAudioPlayer } from "@/components/remoteWitness/useTTSAudioPlayer";
import { WebSocketAction } from "@/enums/WebSocketActionEnum";
import useRemoteWitnessStore from "@/store/modules/remoteWitness";
import { HSNRItem } from "@/types/type";
import { emitter, EventTypes, TTSSubtitlesUpdateData } from "@/utils/eventBus";
import { stripSSMLTags } from "@/utils/utils";
import WebSocketSingleton from "@/utils/WebSocketSingleton";
import { computed, ref, watch } from "vue";
type LayoutItem = {
  text: string;
  x: number;
  y: number;
  width: number;
  globalIndex: number;
};
type PageLayout = {
  items: LayoutItem[];
  startIndex: number;
  endIndex: number;
};
interface Subtitle {
  text: string;
  beginTime: number;
  endTime: number;
  beginIndex: number;
  endIndex: number;
}
type TimelineItem = {
  text: string;
  beginTime: number | null;
  endTime: number | null;
  beginIndex: number;
  endIndex: number;
  phoneme: string;
};
type Timeline = TimelineItem[];
const playState = reactive({
  currentIndexMap: {} as Record<string, number>, // 使用映射记录每个范围的处理进度
  currentPlayingIndex: 0,
  isInitialized: false,
});
interface Props {
  hsnr: string;
  trtc: any;
  speed?: number;
  hsnrItem: HSNRItem;
  handAudioEnd: (hsnrItem: HSNRItem) => void;
  canvasWidth?: number; // 添加宽度 prop
  canvasHeight?: number; // 添加高度 prop
  canvasStart?: boolean; //是否启动canvas渲染
}
const marqueeContainer = ref<HTMLDivElement | null>(null);

const emit = defineEmits<{
  (e: "scroller"): void;
}>();
// 存储计算好的布局信息
const pageLayouts = ref<PageLayout[]>([]);

const store = useRemoteWitnessStore();
const props = withDefaults(defineProps<Props>(), {
  canvasWidth: 336,
  canvasHeight: 99,
  canvasStart: true,
});
const topPadding = 5; // 添加相同的上边距
const canvasWidth = computed(() => props.canvasWidth);
const canvasHeight = computed(() => props.canvasHeight);
// 初始化 timeline 为空数组
const timeline = ref<Timeline>([]);

// 创建初始化函数
const initializeTimeline = (text: string) => {
  timeline.value = stripSSMLTags(text)
    .split("")
    .map((text, index) => ({
      text,
      beginTime: null,
      endTime: null,
      beginIndex: index,
      endIndex: index + 1,
      phoneme: text.replace(/[^\u4e00-\u9fa5]/g, ""),
    }));
};
const currentTime = ref(0);
let animationFrameId: number | null = null;
let startTime: number | null = null;
const isPlaying = ref(false);
let hasSync = false;
const handAudioEnd = () => {
  props.handAudioEnd && props.handAudioEnd(props.hsnrItem);
};
const { playAudio, stopAudio } = useTTSAudioPlayer(handAudioEnd);
let ttsid = "";
const divLineWidth = ref(0);
const canvas = ref<HTMLCanvasElement | null>(null);
let ctx: CanvasRenderingContext2D | null = null;
const lastHighlightedIndex = ref(-1);

const lineHeight = 22; // 行高
const defaultColor = "white";
const highlightColor = "#1890ff";

const startPlay = async () => {
  isPlaying.value = true;
  if (animationFrameId) return;
  currentPage.value = 0;
  lastHighlightedIndex.value = -1;
  nextTick(() => calculateLayouts());
  ttsid = await playAudio(props.hsnr, props.trtc, props.speed);
  //是否已经初始化过字幕
  if (timeline.value[0].beginTime) {
    startTime = Date.now();
    hasSync = false;
    ttsSubSync();

    updateTime();
  }
};

const currentPage = ref(0);

//tts字幕播放同步到pad端
async function ttsSubSync() {
  if (!store.useSocketNotification) {
    return;
  }
  const ws = WebSocketSingleton.getInstance();
  await ws.publish({
    destination: "/message",

    body: JSON.stringify({
      recipient: store.connectId,
      content: JSON.stringify({
        action: WebSocketAction.TTS_SUB,
        sub: timeline.value,
        hasSync,
      }),
    }),
  });
  hasSync = true;
}
const stopPlay = () => {
  isPlaying.value = false;
  currentPage.value = 0;
  lastHighlightedIndex.value = -1;
  drawCanvas();
  if (animationFrameId) {
    cancelAnimationFrame(animationFrameId);
    animationFrameId = null;
  }
  stopAudio();

  currentTime.value = 0;
};

defineExpose({
  startPlay,
  stopPlay,
  canvas,
});

const updateTime = () => {
  if (!startTime || !isPlaying.value) return;
  currentTime.value = Date.now() - startTime;
  drawCanvas();
  animationFrameId = requestAnimationFrame(updateTime);
};

// 在 TTS_SUBTITLES_UPDATE 事件处理中使用
// 提取字幕处理逻辑
const handleSubtitles = (
  subtitles: Subtitle[],

  startIndex = 0,
  timelineSegment: Timeline = timeline.value // 新增参数
) => {
  let currentIndex = startIndex;

  subtitles.forEach((sub) => {
    const subText = sub.text;
    let subCharIndex = 0;

    while (
      subCharIndex < subText.length &&
      currentIndex < timelineSegment.length
    ) {
      const currentSubChar = subText[subCharIndex];
      let matchIndex = findMatchingCharacter(
        timelineSegment,
        currentSubChar,
        currentIndex
      );

      if (matchIndex === -1) {
        // 如果没找到匹配的字符，使用当前位置的时间
        updateTimeRange(
          timelineSegment,
          currentIndex,
          currentIndex,
          sub.beginTime,
          sub.endTime
        );

        // 如果当前timeline字符为*，直接跳过
        if (timelineSegment[currentIndex].text === "*") {
          currentIndex++;
          continue;
        }
        const isMovable = /[a-zA-Z0-9\p{P}]/u.test(
          timelineSegment[currentIndex].text
        );

        // 如果是数字、字母或标点符号，则移动 currentIndex
        if (isMovable) {
          currentIndex++;
        }

        // currentIndex++;
        subCharIndex++;
        continue;
      }

      // 更新时间信息
      updateTimeRange(
        timelineSegment,
        currentIndex,
        matchIndex,
        sub.beginTime,
        sub.endTime
      );

      currentIndex = matchIndex + 1;

      subCharIndex++;
    }
  });

  return currentIndex;
};
const findMatchingCharacter = (
  timeline: Timeline,
  char: string,
  startIndex: number
): number => {
  if (!timeline) return -1;

  // 向前查找
  const forwardEndIndex = Math.min(startIndex + 5, timeline.length);
  for (let i = startIndex; i < forwardEndIndex; i++) {
    if (timeline[i].text === char) return i;
  }

  // 向后查找
  const backwardStartIndex = Math.max(0, startIndex - 3);
  for (let i = startIndex - 1; i >= backwardStartIndex; i--) {
    if (timeline[i]?.text === char) return i;
  }

  return -1;
};

const measureSpan = ref<HTMLSpanElement | null>(null);
// 在画布尺寸变化时重新计算布局
const getTextWidth = (text: string): number => {
  if (!measureSpan.value) return 0;

  // 复用现有的 span 样式
  measureSpan.value.textContent = text;
  return measureSpan.value.getBoundingClientRect().width;
};

const calculateLayouts = () => {
  if (!measureSpan.value) return;
  if (pageLayouts.value?.length) {
    return;
  }
  if (!props.canvasStart) return;
  pageLayouts.value = [];
  let currentPageItems: LayoutItem[] = [];
  let x = 0;
  let y = 0;
  let pageStartIndex = 0;
  const testWidth = getTextWidth("测试");
  // 如果测试文本宽度为0，说明元素还未正确渲染，退出计算
  if (testWidth === 0) {
    const observer = new MutationObserver((mutations) => {
      const newWidth = getTextWidth("测试");
      console.log("newWidth测试", newWidth);
      if (newWidth > 0) {
        observer.disconnect();
        calculateLayouts();
      }
    });

    observer.observe(measureSpan.value, {
      attributes: true,
      childList: true,
      subtree: true,
    });
    return;
  }
  const dpr = window.devicePixelRatio || 1;

  const maxwidth = canvasWidth.value;
  const maxheight = canvasHeight.value - topPadding;

  timeline.value.forEach((item, index) => {
    const charWidth = getTextWidth(item.text);

    // 检查是否需要换行
    if (x + charWidth > maxwidth) {
      x = 0;
      y += lineHeight;
    }

    // 检查是否需要新页面
    if (y + lineHeight > maxheight) {
      // 保存当前页面布局
      pageLayouts.value.push({
        items: currentPageItems,
        startIndex: pageStartIndex,
        endIndex: index - 1,
      });

      // 重置为新页面
      currentPageItems = [];
      x = 0;
      y = 0;
      pageStartIndex = index;
    }

    // 添加当前字符的布局信息
    currentPageItems.push({
      text: item.text,
      x,
      y,
      width: charWidth,
      globalIndex: index,
    });

    x += charWidth;
  });

  // 添加最后一页
  if (currentPageItems.length > 0) {
    pageLayouts.value.push({
      items: currentPageItems,
      startIndex: pageStartIndex,
      endIndex: timeline.value.length - 1,
    });
  }
  console.log(pageLayouts.value, currentPageLayout.value);
};
watch(
  [canvasWidth, canvasHeight],
  () => {
    const canvasEl = canvas.value;
    if (canvasEl && ctx) {
      const dpr = window.devicePixelRatio || 1;
      canvasEl.width = canvasWidth.value * dpr;
      canvasEl.height = canvasHeight.value * dpr;
      ctx.scale(dpr, dpr);
      calculateLayouts();
      drawCanvas();
    }
  },
  { immediate: true }
);
watch(
  () => props.hsnr,
  (newValue) => {
    if (newValue) {
      initializeTimeline(newValue);
      calculateLayouts();
    }
  },
  { immediate: true }
);
// 获取当前页面的布局
const currentPageLayout = computed(() => {
  return (
    pageLayouts.value[currentPage.value] || {
      items: [],
      startIndex: 0,
      endIndex: 0,
    }
  );
});
const updateTimeRange = (
  timeline: NonNullable<Timeline>,
  start: number,
  end: number,
  beginTime: number,
  endTime: number
) => {
  for (let i = start; i <= end; i++) {
    timeline[i].beginTime = beginTime;
    timeline[i].endTime = endTime;
  }
};
onMounted(() => {
  const canvasEl = canvas.value;
  if (!canvasEl) return;

  const dpr = window.devicePixelRatio || 1;

  // 设置更高分辨率的画布
  canvasEl.width = props.canvasWidth * dpr;
  canvasEl.height = props.canvasHeight * dpr;
  canvasEl.style.width = `${props.canvasWidth}px`;
  canvasEl.style.height = `${props.canvasHeight}px`;

  ctx = canvasEl.getContext("2d", { alpha: false });
  if (ctx) {
    ctx.scale(dpr, dpr);
  }
  nextTick(() => {
    if (measureSpan.value) {
      // 计算一次divLineWidth并缓存
      divLineWidth.value = calculateDivLineWidth();
      calculateLayouts();
      drawCanvas();
    }
  });

  emitter.on(
    EventTypes.TTS_SUBTITLES_UPDATE,
    ({ subtitles, tts, beginIndex, endIndex }: TTSSubtitlesUpdateData) => {
      if (!subtitles.length) return;
      if (ttsid !== tts) return;

      // 暂存subtitles
      const pendingSubtitles = { subtitles, beginIndex, endIndex };

      const processSubtitles = () => {
        let previousEndTime = 0;
        // 只有在beginIndex大于0时才需要等待
        if (beginIndex > 0) {
          previousEndTime =
            timeline.value[pendingSubtitles.beginIndex - 1]?.endTime;
          if (previousEndTime === null || previousEndTime === undefined) {
            requestAnimationFrame(processSubtitles);
            return;
          }
          // 偏移subtitles中的beginTime和endTime
          pendingSubtitles.subtitles = pendingSubtitles.subtitles.map(
            (sub) => ({
              ...sub,
              beginTime: sub.beginTime + previousEndTime,
              endTime: sub.endTime + previousEndTime,
            })
          );
        }

        const rangeKey = `${pendingSubtitles.beginIndex}-${pendingSubtitles.endIndex}`;
        const currentIdx =
          playState.currentIndexMap[rangeKey] !== undefined
            ? playState.currentIndexMap[rangeKey]
            : 0;

        // 处理字幕并更新这个范围的索引
        const newIndex = handleSubtitles(
          pendingSubtitles.subtitles,
          currentIdx,
          timeline.value!.slice(
            pendingSubtitles.beginIndex,
            pendingSubtitles.endIndex + 1
          )
        );

        // 更新这个范围的处理进度
        playState.currentIndexMap[rangeKey] = newIndex;

        if (isPlaying.value) {
          ttsSubSync();
          startPlay();
        }
      };

      // 开始处理subtitles
      processSubtitles();
    }
  );
});
// Modified drawCanvas function
const drawCanvas = () => {
  if (!ctx || !canvas.value) return;
  if (!props.canvasStart) return;

  ctx.clearRect(0, 0, canvas.value.width, canvas.value.height);
  ctx.font = "400 14px Arial";
  ctx.textBaseline = "top";

  const layout = currentPageLayout.value;

  layout.items.forEach((item) => {
    const highlighted = isHighlighted(item.globalIndex);
    ctx.fillStyle = highlighted
      ? highlightColor
      : hasPlayed(item.globalIndex)
        ? highlightColor
        : defaultColor;

    ctx.fillText(item.text, item.x, item.y + topPadding);
  });

  checkPageTransition();
};
const checkPageTransition = () => {
  if (!isPlaying.value || lastHighlightedIndex.value === -1) return;

  const currentLayout = pageLayouts.value[currentPage.value];
  if (!currentLayout) return;

  // 如果最后一个高亮元素超出当前页，找到正确的页面
  if (lastHighlightedIndex.value > currentLayout.endIndex) {
    const nextPage = pageLayouts.value.findIndex(
      (layout) =>
        lastHighlightedIndex.value >= layout.startIndex &&
        lastHighlightedIndex.value <= layout.endIndex
    );

    if (nextPage !== -1) {
      // 计算需要滚动的行数
      // 使用缓存的divLineWidth
      if (isNaN(divLineWidth.value) || divLineWidth.value <= 0) {
        divLineWidth.value = calculateDivLineWidth();
        // 如果重新计算后仍然无效，则返回
        if (isNaN(divLineWidth.value) || divLineWidth.value <= 0) return;
      }
      const charsDiff =
        pageLayouts.value[nextPage].startIndex - currentLayout.startIndex;
      const scrollerNumber = Math.ceil(charsDiff / divLineWidth.value);

      emit("scroller", scrollerNumber);
      currentPage.value = nextPage;
    }
  }
};
const calculateDivLineWidth = () => {
  if (!measureSpan.value || !marqueeContainer.value) return 0;
  const containerWidth = marqueeContainer.value.getBoundingClientRect().width;
  const testChar = "测";
  const charWidth = getTextWidth(testChar);
  return Math.floor(containerWidth / charWidth);
};
onUnmounted(() => {
  if (animationFrameId) {
    cancelAnimationFrame(animationFrameId);
  }
  emitter.off(EventTypes.TTS_SUBTITLES_UPDATE);
});

const hasPlayed = (index: number) => {
  if (!isPlaying.value) return false;

  const timelineItem = timeline.value[index];
  if (!timelineItem) return false;
  if (!(timelineItem.beginTime && timelineItem.endTime)) {
    return false;
  }
  return currentTime.value >= timelineItem.endTime;
};

const isHighlighted = (index: number) => {
  if (!isPlaying.value) return false;

  const timelineItem = timeline.value[index];
  if (!timelineItem) return false;
  if (!(timelineItem.beginTime && timelineItem.endTime)) {
    return false;
  }
  const highlighted =
    currentTime.value >= timelineItem.beginTime &&
    currentTime.value <= timelineItem.endTime;

  if (highlighted) {
    lastHighlightedIndex.value = index;
  }

  return highlighted;
};
</script>

<template>
  <div class="relative">
    <div ref="marqueeContainer" class="marquee-container">
      <span
        v-for="(item, index) in timeline"
        :key="index"
        :class="{ highlight: isHighlighted(index), played: hasPlayed(index) }"
      >
        {{ item.text }}
      </span>
      <!-- 添加用于测量的隐藏 span -->
      <span
        ref="measureSpan"
        class="absolute opacity-0 pointer-events-none whitespace-pre"
        style="top: -9999px; left: -9999px"
      ></span>
    </div>
    <div class="hidden-container">
      <canvas
        ref="canvas"
        class="canvas-container"
        :style="`width: ${props.canvasWidth}px; height: ${props.canvasHeight}px;`"
      ></canvas>
    </div>
  </div>
</template>

<style scoped>
.marquee-container,
.marquee-container-hide {
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
  white-space: pre-wrap;
  word-break: break-all;
  @apply w-full;
}
.marquee-container-hide {
  @apply text-white;
}
.hidden-container {
  height: 0;
  width: 0;
  overflow: hidden;
}
.highlight {
  color: #1890ff !important;
}

.played {
  color: #1890ff !important;
}
/* .canvas-container {
  width: v-bind('props.canvasWidth + "px"');
  height: v-bind('props.canvasHeight + "px"');
} */
</style>
