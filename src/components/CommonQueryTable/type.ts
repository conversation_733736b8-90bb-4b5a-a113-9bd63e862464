import { ControlTypeEnum } from "@/enums/ControlTypeEnum";
import {
  ButtonProps,
  FormItemProps,
  FormRules,
  TableColumnCtx,
} from "element-plus";
import type { Component } from "vue";

// 表单项接口定义
export interface TableFormItem {
  label: string;
  prop: string;
  controlType: ControlTypeEnum;
  formItemProps?: FormItemProps;
  fieldProps?: any;
  // 按钮类型
  type?: string;
  // 点击事件处理函数
  click?: (item: any) => void | Promise<void>;
  // 下拉选项
  options?: Array<{ label: string; value: string | number }>;
  // 是否显示
  hidden?: boolean;
  // 树形选择属性
  treeProps?: {
    label?: string;
    children?: string;
    value?: string;
    class?: string;
    isLeaf?: boolean;
  };
}

// 表格列接口定义，扩展自 Element Plus 的 TableColumnCtx
export interface TableColumn
  extends Partial<
    Omit<TableColumnCtx<any>, "children" | "renderHeader" | "renderCell">
  > {
  // 列类型：show-显示数据，operate-操作列
  type?: string;
  // 字典映射
  dict?: Array<{ ibm: string | number; note: string }>;
  // 自定义渲染函数
  formatter?: (row: any, column: any, cellValue: any, index: number) => any;
  // 自定义渲染函数（支持JSX或h函数）
  render?: (scope: { row: any; column: any; $index: number }) => any;
  // 操作按钮点击事件
  click?: (scope: any) => void | Promise<void>;
  // 表头组件
  headerComponent?: Component;
  // 单元格组件
  cellComponent?: Component;
  // 操作按钮配置
  buttons?: OperateButton[];
}

export interface FormButton extends Partial<ButtonProps> {
  label: string;
  onClick: (params: any, selectedRows: any[]) => void;
}

export interface OperateButton extends Partial<ButtonProps> {
  label?: string;
  onClick?: (scope?: any) => void | Promise<void>;
  // 按钮是否显示的条件函数
  visible?: (row: any) => boolean;
  // 自定义组件，如果提供则使用组件渲染代替按钮
  component?: Component;
}

export interface CommonQueryPagination {
  pageSize: number;
  currentPage: number;
  total?: number;
  /**
   * consists of sizes, prev, pager, next, jumper, ->, total, slot
   */
  layout?: string;
}

export interface CommonQueryTableProps {
  buttons?: FormButton[];
  formItem?: TableFormItem[];
  columns: TableColumn[];
  tableData: any[];
  showSelection?: boolean;
  rules?: FormRules;
  // 分页配置
  pagination?: CommonQueryPagination;
}

export interface DictMapType {
  [key: string]: Array<{ ibm: string | number; note: string }>;
}
