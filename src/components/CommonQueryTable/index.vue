<script lang="ts" setup>
import { ControlTypeEnum } from "@/enums/ControlTypeEnum";
import { TableInstance } from "element-plus";
import { computed, reactive, ref, toRefs } from "vue";
import {
  CommonQueryTableProps,
  FormButton,
  OperateButton,
  TableColumn,
  TableFormItem,
} from "./type";

const multipleTableRef = ref<TableInstance>();

// 定义属性和事件
const props = withDefaults(
  defineProps<{
    rowKey?: string;
    config: CommonQueryTableProps;
    dictMap?: Record<string, any>;
    loading?: boolean;
  }>(),
  {}
);
const emit = defineEmits<{
  (_e: "formStateChange", _value: Record<string, any>): void;
  (
    _e: "pagination-change",
    _value: { currentPage: number; pageSize: number }
  ): void;
}>();

// 从props中解构数据
const { config, dictMap } = toRefs(props);

// 表单状态和选择状态
const formState = reactive<Record<string, any>>({});
const selection = ref<string>("");
const selectedRows = ref<any[]>([]);

// 分页相关状态
// const currentPage = ref(config.value?.pagination?.currentPage || 1);
// const pageSize = ref(config.value?.pagination?.pageSize || 10);

const handleReset = () => {
  for (const key in formState) {
    formState[key] = undefined;
  }
  emit("formStateChange", formState);
};

const handleSearch = () => {
  emit("formStateChange", formState);
};

// 处理表格数据，支持字典转换
const tableData = computed(() => {
  return (
    config.value?.tableData?.map((item: any) => {
      const newObj: Record<string, any> = {};
      for (const key in item) {
        const hasDict = config.value?.columns?.find((i: TableColumn) => {
          return i.prop === key && !!i.dict;
        });

        if (hasDict && hasDict.dict) {
          newObj[key] = translate(
            hasDict.dict,
            String(item[key] || "").split(";")
          );
        }
      }
      return {
        ...item,
        ...newObj,
      };
    }) || []
  );
});

// 字典翻译函数
const translate = (
  dictArr: Array<{ ibm: string | number; note: string }>,
  targetArr: (string | number)[]
) => {
  if (!dictArr || !targetArr || targetArr.length === 0) return "";

  const ibmToFlmcMap = dictArr.reduce(
    (
      map: Record<string, string>,
      item: { ibm: string | number; note: string }
    ) => {
      map[String(item.ibm)] = item.note;
      return map;
    },
    {}
  );
  const result = targetArr
    .map((value) => ibmToFlmcMap[String(value)])
    .filter(Boolean);
  return result.length > 0 ? result.join(";") : "";
};

// 获取placeholder提示文本
const getPlaceholder = (item: TableFormItem): string => {
  let placeholder = "";
  switch (item.controlType) {
    case ControlTypeEnum.INPUT:
      placeholder = "请输入" + item.label;
      break;
    case ControlTypeEnum.SELECT:
      placeholder = "请选择" + item.label;
      break;
    case ControlTypeEnum.TREE_SELECT:
      placeholder = "请选择" + item.label;
      break;
    default:
      placeholder = "请输入" + item.label;
  }
  return placeholder;
};

// 获取树形选择控件的数据
const getTreeData = (item: TableFormItem): any[] => {
  // 如果有自定义选项，使用自定义选项
  if (item.options && item.options.length > 0) {
    return item.options.map((option) => ({
      label: option.label,
      value: option.value,
      children: [],
    }));
  }
  // 否则返回空数组
  return [];
};

// 操作列按钮点击事件
const _handleOperateButtonClick = (scope: any, btn: OperateButton): void => {
  if (btn.onClick) {
    btn.onClick(scope);
  }
};

// 表单按钮点击事件
const _formClick = (item: FormButton): void => {
  if (!item.onClick) return;
  item.onClick(selection.value, selectedRows.value);
};

// 表格选择变更事件
const handleSelectionChange = (val: any[]): void => {
  selection.value = val
    .map((item) => item[props.rowKey || "id"])
    .filter(Boolean)
    .join(";");
  selectedRows.value = val;
};

// 分页大小变更事件
const handleSizeChange = (size: number): void => {
  emit("pagination-change", {
    currentPage: props.config.pagination?.currentPage || 1,
    pageSize: size,
  });
};

// 页码变更事件
const handleCurrentChange = (page: number): void => {
  console.log("🚀 ~ handleCurrentChange ~ page:", page); // 可以保留这个 log 确认事件触发
  emit("pagination-change", {
    currentPage: page,
    pageSize: props.config.pagination?.pageSize || 10,
  });
};

// 清除表格选择
const clearSelection = () => {
  selection.value = "";
  selectedRows.value = [];
  multipleTableRef.value?.clearSelection();
};

defineExpose({
  clearSelection,
});
</script>

<template>
  <div class="commonQueryTable">
    <el-form
      :inline="true"
      :rules="config?.rules"
      :model="formState"
      class="demo-form-inline"
    >
      <template v-for="(item, index) in config?.formItem" :key="index">
        <!-- 输入框控件 -->
        <el-form-item
          v-if="item?.controlType === ControlTypeEnum.INPUT"
          v-show="!item?.hidden"
          :label="item?.label"
          :prop="item?.prop"
          v-bind="item?.formItemProps"
        >
          <el-input
            v-model="formState[item?.prop]"
            :placeholder="getPlaceholder(item)"
            v-bind="item?.fieldProps"
            style="width: 200px"
          />
        </el-form-item>

        <!-- 下拉选择框控件 -->
        <el-form-item
          v-if="item?.controlType === ControlTypeEnum.SELECT"
          v-show="!item?.hidden"
          :label="item?.label"
          :prop="item?.prop"
          v-bind="item?.formItemProps"
        >
          <el-select
            v-model="formState[item?.prop]"
            :placeholder="'请选择' + item?.label"
            v-bind="item?.fieldProps"
          >
            <template v-if="item.options && item.options.length > 0">
              <el-option
                v-for="option in item.options"
                :key="option.value"
                :label="option.label"
                :value="option.value"
              />
            </template>
            <template v-else-if="item.prop && dictMap && dictMap[item.prop]">
              <el-option
                v-for="(dictItem, dictIndex) in dictMap[item.prop]"
                :key="dictIndex"
                :label="dictItem.note"
                :value="dictItem.ibm"
              />
            </template>
          </el-select>
        </el-form-item>
        <!-- 下拉选择框控件2 -->
        <el-form-item
          v-if="item?.controlType === ControlTypeEnum.SELECT2"
          v-show="!item?.hidden"
          :label="item?.label"
          :prop="item?.prop"
          v-bind="item?.formItemProps"
        >
          <el-select-v2
            v-model="formState[item?.prop]"
            :placeholder="'请选择' + item?.label"
            v-bind="item?.fieldProps"
            :options="item.options"
          />
        </el-form-item>

        <!-- 树形选择控件 -->
        <el-form-item
          v-if="item?.controlType === ControlTypeEnum.TREE_SELECT"
          v-show="!item?.hidden"
          :label="item?.label"
          :prop="item?.prop"
          v-bind="item?.formItemProps"
        >
          <el-tree-select
            v-model="formState[item?.prop]"
            :placeholder="'请选择' + item?.label"
            :data="getTreeData(item)"
            :props="
              item.treeProps || {
                label: 'label',
                children: 'children',
                value: 'value',
              }
            "
            v-bind="item?.fieldProps"
          />
        </el-form-item>
      </template>

      <template v-if="config?.formItem">
        <el-form-item>
          <!-- 查询按钮 -->
          <el-button type="primary" @click="handleSearch">查询</el-button>
          <!-- 重置按钮 -->
          <el-button @click="handleReset">重置</el-button>
        </el-form-item>
      </template>

      <!-- 按钮控件 -->
      <div v-if="config?.buttons" class="flex items-center mb-12">
        <el-button
          v-for="(item, index) in config?.buttons"
          v-bind="{ ...item, onClick: undefined }"
          :key="index"
          @click="_formClick(item)"
        >
          {{ item.label }}
        </el-button>
      </div>
    </el-form>

    <div class="line"></div>

    <!-- 表格 -->
    <el-table
      ref="multipleTableRef"
      v-loading="!!loading"
      :data="tableData"
      style="width: 100%"
      :row-key="rowKey"
      :header-cell-class-name="'headerCellClassName'"
      @selection-change="handleSelectionChange"
    >
      <el-table-column
        v-if="config?.showSelection !== false"
        type="selection"
        width="55"
      />
      <template v-for="(item, index) in config?.columns" :key="index">
        <!-- 普通数据列 -->
        <el-table-column
          v-if="item?.type === 'show' && !item?.render"
          v-bind="item"
          :formatter="item?.formatter"
        />

        <!-- 自定义渲染列 -->
        <el-table-column
          v-else-if="item?.type === 'show' && item?.render"
          v-bind="{ ...item, formatter: undefined }"
        >
          <template #default="scope">
            <!-- 使用自定义render函数渲染内容 -->
            <component :is="item.render(scope)" />
          </template>
        </el-table-column>

        <!-- 操作列 -->
        <el-table-column
          v-else-if="item?.type === 'operate'"
          v-bind="{
            ...item,
            align: item?.align || 'center',
          }"
        >
          <template #default="scope">
            <!-- 如果配置了buttons，则使用配置的按钮 -->
            <template v-if="item.buttons && item.buttons.length > 0">
              <template v-for="(btn, btnIndex) in item.buttons" :key="btnIndex">
                <!-- 如果提供了自定义组件，则使用组件渲染 -->
                <component
                  :is="btn.component"
                  v-if="
                    btn.component && (!btn.visible || btn.visible(scope.row))
                  "
                  v-bind="scope"
                />
                <!-- 否则使用默认的按钮渲染 -->
                <el-button
                  v-else-if="!btn.visible || btn.visible(scope.row)"
                  v-bind="{
                    ...btn,
                    onClick: undefined,
                    visible: undefined,
                    component: undefined,
                  }"
                  @click="_handleOperateButtonClick(scope, btn)"
                >
                  {{ btn.label }}
                </el-button>
              </template>
            </template>
          </template>
        </el-table-column>
      </template>
    </el-table>

    <!-- 分页组件 -->
    <div v-if="config?.pagination" class="mt-12 flex justify-end">
      <ElPagination
        :current-page="config.pagination.currentPage"
        :page-size="config.pagination.pageSize"
        :page-sizes="[10, 20, 30, 50]"
        :total="config.pagination.total"
        :layout="
          config.pagination.layout || 'total, sizes, prev, pager, next, jumper'
        "
        :background="true"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>
  </div>
</template>
<style scoped>
.commonQueryTable {
  padding: 1vw;

  .line {
    width: 100%;
    height: 1px;
    margin-bottom: 10px;
    background: #eee;
  }
}

:deep(.headerCellClassName) {
  background: #f5f5f5 !important;
}

:deep(.el-table) {
  :deep(.el-button) {
    padding: 0 !important;
  }
}
</style>
