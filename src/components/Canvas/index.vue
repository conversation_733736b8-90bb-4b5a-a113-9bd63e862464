<script lang="ts">
function drawTimestampWatermark() {
  const canvas = document.getElementById("watermarkCanvas");
  const ctx = canvas.getContext("2d");

  // 获取当前时间戳
  const timestamp = new Date().toLocaleString();

  // 清空画布
  ctx.clearRect(0, 0, canvas.width, canvas.height);

  // 设置字体和样式
  ctx.font = "14px Arial";
  ctx.fillStyle = "rgb(245,15,15)";
  ctx.textAlign = "left";
  ctx.textBaseline = "top";

  // 绘制时间戳（水平）
  ctx.fillText(timestamp, 10, 10);
}

// 设置定时器每秒更新一次水印
setInterval(drawTimestampWatermark, 1000);
</script>
<template>
  <canvas
    id="watermarkCanvas"
    width="200"
    height="25"
    style="background-color: transparent"
  ></canvas>
</template>
