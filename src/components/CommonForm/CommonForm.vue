<script lang="ts" setup>
import { FormItemTypeEnum } from "@/enums/FormItemTypeEnum";
import {
  FormInstance,
  FormItemProp,
  FormValidateCallback,
  FormValidationResult,
} from "element-plus";
import { Arrayable } from "element-plus/es/utils";
import { reactive, ref, watch } from "vue";
import { FormConfig, FormItems } from "./type";

// 定义属性
const props = withDefaults(
  defineProps<{
    // 表单配置
    formItems: FormItems[];
    // 表单初始值
    initialValues?: Record<string, any>;
    // 字典数据数组
    dictObjectArray?: Record<string, any[]>;
    // 表单配置
    rules?: FormConfig["rules"];
    labelWidth?: FormConfig["labelWidth"];
    labelPosition?: FormConfig["labelPosition"];
    size?: FormConfig["size"];
    disabled?: FormConfig["disabled"];
    inline?: FormConfig["inline"];
    showResetButton?: FormConfig["showResetButton"];
    showSubmitButton?: FormConfig["showSubmitButton"];
    submitButtonText?: FormConfig["submitButtonText"];
    resetButtonText?: FormConfig["resetButtonText"];
    gutter?: FormConfig["gutter"];
    // 是否显示按钮
    showButtons?: boolean;
  }>(),
  {
    initialValues: () => ({}),
    dictObjectArray: () => ({}),
    showButtons: true,
  }
);

// 定义事件
const emit = defineEmits<{
  (_e: "submit", _formData: Record<string, any> | undefined): void;
  (_e: "reset"): void;
  (
    _e: "change",
    _prop: string,
    _value: any,
    _formData: Record<string, any>
  ): void;
  (_e: "update:modelValue", _formData: Record<string, any>): void;
}>();

// 表单引用
const formRef = ref<FormInstance>();

// 表单数据
const formData = reactive<Record<string, any>>({});

// 初始化表单数据
const initFormData = () => {
  // 清空表单数据
  Object.keys(formData).forEach((key) => {
    delete formData[key];
  });

  // 设置初始值
  if (props.initialValues) {
    Object.keys(props.initialValues).forEach((key) => {
      formData[key] = props.initialValues[key];
    });
  }

  // 设置默认值
  props.formItems.forEach((item) => {
    if (formData[item.prop] === undefined) {
      // 根据控件类型设置默认值
      switch (item.valueType) {
        // case FormItemTypeEnum.CHECKBOX:
        //   formData[item.prop] = [];
        //   break;
        case FormItemTypeEnum.SWITCH:
          formData[item.prop] = false;
          break;
        case FormItemTypeEnum.RATE:
          formData[item.prop] = 0;
          break;
        // case FormItemTypeEnum.INPUT_NUMBER:
        //   formData[item.prop] = 0;
        //   break;
        // default:
        //   formData[item.prop] = "";
      }
    }
  });
};

// 监听初始值变化
watch(
  () => props.initialValues,
  () => {
    initFormData();
  },
  { deep: true }
);

// 初始化
initFormData();

// 获取占位符
const getPlaceholder = (item: FormItems): string => {
  // 只有这些类型才有 placeholder 属性
  const typesWithPlaceholder = [
    FormItemTypeEnum.INPUT,
    FormItemTypeEnum.TEXTAREA,
    FormItemTypeEnum.SELECT,
    FormItemTypeEnum.DATE_PICKER,
    FormItemTypeEnum.TIME_PICKER,
    FormItemTypeEnum.DATE_TIME_PICKER,
    FormItemTypeEnum.CASCADER,
  ];

  if (
    typesWithPlaceholder.includes(item.valueType) &&
    item.fieldProps &&
    typeof (item.fieldProps as any).placeholder === "string"
  ) {
    return (item.fieldProps as { placeholder: string }).placeholder;
  }

  let prefix = "请输入";
  switch (item.valueType) {
    case FormItemTypeEnum.SELECT:
    case FormItemTypeEnum.CASCADER:
    case FormItemTypeEnum.DATE_PICKER:
    case FormItemTypeEnum.TIME_PICKER:
    case FormItemTypeEnum.DATE_TIME_PICKER:
      prefix = "请选择";
      break;
  }
  return `${prefix}${item.label}`;
};

// 处理表单项变更
const handleChange = (prop: string, value: unknown) => {
  emit("change", prop, value, formData);
  emit("update:modelValue", formData);
};

// 处理表单提交
const handleSubmit = async () => {
  if (!formRef.value) return;

  try {
    await formRef.value.validate();
    emit("submit", formData);
  } catch (error) {
    console.error("表单验证失败", error);
  }
};

// 处理表单重置
const handleReset = () => {
  if (!formRef.value) return;
  formRef.value.resetFields();
  emit("reset");
  emit("update:modelValue", formData);
};

export type CommonFormExpose = {
  formRef: Ref<FormInstance | undefined>;
  formData: Record<string, any>;
  validate: (_callback?: FormValidateCallback) => FormValidationResult;
  validateField: (
    _props?: Arrayable<FormItemProp>,
    _callback?: FormValidateCallback
  ) => FormValidationResult;
  resetFields: (_props?: Arrayable<FormItemProp>) => void;
  scrollToField: (_prop: FormItemProp) => void;
  clearValidate: (_props?: Arrayable<FormItemProp>) => void;
};

// 暴露方法
defineExpose<CommonFormExpose>({
  formRef,
  formData,
  validate: async (callback?: FormValidateCallback) => {
    if (!formRef.value) return false;
    return formRef.value.validate(callback);
  },
  validateField: async (props, callback) => {
    if (!formRef.value) return false;
    return formRef.value.validateField(props, callback);
  },
  resetFields: () => {
    if (!formRef.value) return;
    formRef.value.resetFields();
  },
  scrollToField: (prop: FormItemProp) => {
    if (!formRef.value) return;
    formRef.value.scrollToField(prop);
  },
  clearValidate: (props?: Arrayable<FormItemProp>) => {
    if (!formRef.value) return;
    formRef.value.clearValidate(props);
  },
});
</script>

<template>
  <el-form
    ref="formRef"
    :model="formData"
    :rules="props.rules"
    :label-width="props.labelWidth || '120px'"
    :label-position="props.labelPosition || 'right'"
    :size="props.size || 'default'"
    :disabled="props.disabled"
    :inline="props.inline"
  >
    <el-row :gutter="props.gutter || 20">
      <template v-for="(item, index) in formItems" :key="index">
        <el-col
          v-if="!item.hideInForm"
          :span="item.span || 12"
          v-bind="item.colProps || {}"
        >
          <el-form-item
            :label="item.label"
            :prop="item.prop"
            v-bind="item.formItemProps || {}"
          >
            <!-- 自定义渲染 -->
            <template v-if="item.render">
              <component :is="item.render(formData)" />
            </template>

            <!-- 输入框 -->
            <template v-else-if="item.valueType === FormItemTypeEnum.INPUT">
              <el-input
                v-model="formData[item.prop]"
                :placeholder="getPlaceholder(item)"
                v-bind="item.fieldProps || {}"
                @change="(val) => handleChange(item.prop, val)"
              />
            </template>

            <!-- 文本域 -->
            <template v-else-if="item.valueType === FormItemTypeEnum.TEXTAREA">
              <el-input
                v-model="formData[item.prop]"
                type="textarea"
                :placeholder="getPlaceholder(item)"
                v-bind="item.fieldProps || {}"
                @change="(val) => handleChange(item.prop, val)"
              />
            </template>

            <!-- 下拉选择框 -->
            <template v-else-if="item.valueType === FormItemTypeEnum.SELECT">
              <el-select
                v-model="formData[item.prop]"
                :placeholder="getPlaceholder(item)"
                v-bind="item.fieldProps || {}"
                @change="(val) => handleChange(item.prop, val)"
              >
                <template v-if="Array.isArray(item.options)">
                  <el-option
                    v-for="(option, optionIndex) in item.options"
                    :key="optionIndex"
                    :label="option.label"
                    :value="option.value"
                  />
                </template>
                <template
                  v-else-if="
                    item.options &&
                    !Array.isArray(item.options) &&
                    dictObjectArray
                  "
                >
                  <template v-if="dictObjectArray[item.options.dictCode]">
                    <el-option
                      v-for="(dictItem, dictIndex) in dictObjectArray[
                        item.options.dictCode
                      ]"
                      :key="dictIndex"
                      :label="dictItem.note || dictItem.label"
                      :value="dictItem.ibm || dictItem.value"
                    />
                  </template>
                </template>
              </el-select>
            </template>

            <!-- 单选框组 -->
            <template v-else-if="item.valueType === FormItemTypeEnum.RADIO">
              <el-radio-group
                v-model="formData[item.prop]"
                v-bind="item.fieldProps || {}"
                @change="(val) => handleChange(item.prop, val)"
              >
                <template v-if="Array.isArray(item.options)">
                  <el-radio
                    v-for="(option, optionIndex) in item.options"
                    :key="optionIndex"
                    :label="option.value"
                  >
                    {{ option.label }}
                  </el-radio>
                </template>
                <template
                  v-else-if="
                    item.options &&
                    !Array.isArray(item.options) &&
                    dictObjectArray
                  "
                >
                  <template v-if="dictObjectArray[item.options.dictCode]">
                    <el-radio
                      v-for="(dictItem, dictIndex) in dictObjectArray[
                        item.options.dictCode
                      ]"
                      :key="dictIndex"
                      :label="dictItem.ibm || dictItem.value"
                    >
                      {{ dictItem.note || dictItem.label }}
                    </el-radio>
                  </template>
                </template>
              </el-radio-group>
            </template>

            <!-- 复选框组 -->
            <template v-else-if="item.valueType === FormItemTypeEnum.CHECKBOX">
              <el-checkbox-group
                v-model="formData[item.prop]"
                v-bind="item.fieldProps || {}"
                @change="(val) => handleChange(item.prop, val)"
              >
                <template v-if="Array.isArray(item.options)">
                  <el-checkbox
                    v-for="(option, optionIndex) in item.options"
                    :key="optionIndex"
                    :label="option.value"
                  >
                    {{ option.label }}
                  </el-checkbox>
                </template>
                <template
                  v-else-if="
                    item.options &&
                    !Array.isArray(item.options) &&
                    dictObjectArray
                  "
                >
                  <template v-if="dictObjectArray[item.options.dictCode]">
                    <el-checkbox
                      v-for="(dictItem, dictIndex) in dictObjectArray[
                        item.options.dictCode
                      ]"
                      :key="dictIndex"
                      :label="dictItem.ibm || dictItem.value"
                    >
                      {{ dictItem.note || dictItem.label }}
                    </el-checkbox>
                  </template>
                </template>
              </el-checkbox-group>
            </template>

            <!-- 开关 -->
            <template v-else-if="item.valueType === FormItemTypeEnum.SWITCH">
              <el-switch
                v-model="formData[item.prop]"
                v-bind="item.fieldProps || {}"
                @change="(val) => handleChange(item.prop, val)"
              />
            </template>

            <!-- 日期选择器 -->
            <template
              v-else-if="item.valueType === FormItemTypeEnum.DATE_PICKER"
            >
              <el-date-picker
                v-model="formData[item.prop]"
                :placeholder="getPlaceholder(item)"
                v-bind="item.fieldProps || {}"
                @change="(val: any) => handleChange(item.prop, val)"
              />
            </template>

            <!-- 时间选择器 -->
            <template
              v-else-if="item.valueType === FormItemTypeEnum.TIME_PICKER"
            >
              <el-time-picker
                v-model="formData[item.prop]"
                :placeholder="getPlaceholder(item)"
                v-bind="item.fieldProps || {}"
                @change="(val: any) => handleChange(item.prop, val)"
              />
            </template>

            <!-- 日期时间选择器 -->
            <template
              v-else-if="item.valueType === FormItemTypeEnum.DATE_TIME_PICKER"
            >
              <el-date-picker
                v-model="formData[item.prop]"
                type="datetime"
                :placeholder="getPlaceholder(item)"
                v-bind="item.fieldProps || {}"
                @change="(val: any) => handleChange(item.prop, val)"
              />
            </template>

            <!-- 数字输入框 -->
            <template
              v-else-if="item.valueType === FormItemTypeEnum.INPUT_NUMBER"
            >
              <el-input-number
                v-model="formData[item.prop]"
                v-bind="item.fieldProps || {}"
                @change="(val) => handleChange(item.prop, val)"
              />
            </template>

            <!-- 滑块 -->
            <template v-else-if="item.valueType === FormItemTypeEnum.SLIDER">
              <el-slider
                v-model="formData[item.prop]"
                v-bind="item.fieldProps || {}"
                @change="(val) => handleChange(item.prop, val)"
              />
            </template>

            <!-- 评分 -->
            <template v-else-if="item.valueType === FormItemTypeEnum.RATE">
              <el-rate
                v-model="formData[item.prop]"
                v-bind="item.fieldProps || {}"
                @change="(val) => handleChange(item.prop, val)"
              />
            </template>

            <!-- 颜色选择器 -->
            <template
              v-else-if="item.valueType === FormItemTypeEnum.COLOR_PICKER"
            >
              <el-color-picker
                v-model="formData[item.prop]"
                v-bind="item.fieldProps || {}"
                @change="(val) => handleChange(item.prop, val)"
              />
            </template>

            <!-- 级联选择器 -->
            <template v-else-if="item.valueType === FormItemTypeEnum.CASCADER">
              <el-cascader
                v-model="formData[item.prop]"
                :placeholder="getPlaceholder(item)"
                :options="Array.isArray(item.options) ? item.options : []"
                v-bind="item.fieldProps || {}"
                @change="(val) => handleChange(item.prop, val)"
              />
            </template>

            <!-- 上传 -->
            <template v-else-if="item.valueType === FormItemTypeEnum.UPLOAD">
              <el-upload v-bind="item.fieldProps || {}">
                <el-button type="primary">点击上传</el-button>
              </el-upload>
            </template>

            <!-- 默认输入框 -->
            <template v-else>
              <el-input
                v-model="formData[item.prop]"
                :placeholder="getPlaceholder(item)"
                v-bind="item.fieldProps || {}"
                @change="(val) => handleChange(item.prop, val)"
              />
            </template>
          </el-form-item>
        </el-col>
      </template>
    </el-row>

    <!-- 表单按钮 -->
    <el-form-item v-if="showButtons">
      <el-button
        v-if="props.showSubmitButton !== false"
        type="primary"
        @click="handleSubmit"
      >
        {{ props.submitButtonText || "提交" }}
      </el-button>
      <el-button v-if="props.showResetButton !== false" @click="handleReset">
        {{ props.resetButtonText || "重置" }}
      </el-button>
    </el-form-item>
  </el-form>
</template>
