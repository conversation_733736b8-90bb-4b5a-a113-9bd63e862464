import { FormItemTypeEnum } from "@/enums/FormItemTypeEnum";
import {
  CascaderProps,
  CheckboxGroupProps,
  ColorPickerProps,
  ColProps,
  DatePickerProps,
  FormItemProps,
  FormRules,
  InputNumberProps,
  InputProps,
  ISelectProps,
  RadioGroupProps,
  RateProps,
  RowProps,
  SliderProps,
  SwitchProps,
  TimePickerDefaultProps,
  UploadProps,
} from "element-plus";
import { Component } from "vue";

// 字典选项类型
export interface DictOption {
  label: string;
  value: string | number;
  [key: string]: any;
}

// 字典配置类型
export interface DictConfig {
  dictCode: string;
  dictOptions?: Record<string, any>; // 字典额外配置项
}

// 根据表单项类型定义对应的属性类型
export type FieldPropsType<T extends FormItemTypeEnum> =
  T extends FormItemTypeEnum.INPUT
    ? InputProps
    : T extends FormItemTypeEnum.TEXTAREA
      ? Omit<InputProps, "type"> & {
          rows?: number;
          autosize?: boolean | { minRows: number; maxRows: number };
        }
      : T extends FormItemTypeEnum.SELECT
        ? ISelectProps
        : T extends FormItemTypeEnum.RADIO
          ? RadioGroupProps
          : T extends FormItemTypeEnum.CHECKBOX
            ? CheckboxGroupProps
            : T extends FormItemTypeEnum.SWITCH
              ? SwitchProps
              : T extends FormItemTypeEnum.DATE_PICKER
                ? DatePickerProps
                : T extends FormItemTypeEnum.TIME_PICKER
                  ? TimePickerDefaultProps
                  : T extends FormItemTypeEnum.DATE_TIME_PICKER
                    ? DatePickerProps
                    : T extends FormItemTypeEnum.INPUT_NUMBER
                      ? InputNumberProps
                      : T extends FormItemTypeEnum.SLIDER
                        ? SliderProps
                        : T extends FormItemTypeEnum.RATE
                          ? RateProps
                          : T extends FormItemTypeEnum.COLOR_PICKER
                            ? ColorPickerProps
                            : T extends FormItemTypeEnum.CASCADER
                              ? CascaderProps
                              : T extends FormItemTypeEnum.UPLOAD
                                ? UploadProps
                                : Record<string, any>;

// 表单项基本类型
export interface FormItemsBase {
  // 基本属性
  prop: string;
  label: string;

  // 布局相关
  span?: number; // 栅格占据的列数，默认为 12
  colProps?: ColProps; // 传递给 col 的属性
  rowProps?: RowProps; // 传递给 row 的属性

  // 选项相关
  options?: DictOption[] | DictConfig; // 可以是选项数组或字典配置

  // 显示控制
  hideInForm?: boolean; // 是否在表单中隐藏

  // 表单项属性
  formItemProps?: FormItemProps; // 传递给 el-form-item 的属性
}

// 使用泛型和条件类型定义表单项类型
export interface FormItemsWithType<T extends FormItemTypeEnum>
  extends FormItemsBase {
  valueType: T;
  fieldProps?: FieldPropsType<T>;
  render?: (formData: Record<string, any>) => Component; // 自定义渲染函数
}

// 联合类型，表示所有可能的表单项类型
export type FormItems =
  | FormItemsWithType<FormItemTypeEnum.INPUT>
  | FormItemsWithType<FormItemTypeEnum.TEXTAREA>
  | FormItemsWithType<FormItemTypeEnum.SELECT>
  | FormItemsWithType<FormItemTypeEnum.RADIO>
  | FormItemsWithType<FormItemTypeEnum.CHECKBOX>
  | FormItemsWithType<FormItemTypeEnum.SWITCH>
  | FormItemsWithType<FormItemTypeEnum.DATE_PICKER>
  | FormItemsWithType<FormItemTypeEnum.TIME_PICKER>
  | FormItemsWithType<FormItemTypeEnum.DATE_TIME_PICKER>
  | FormItemsWithType<FormItemTypeEnum.INPUT_NUMBER>
  | FormItemsWithType<FormItemTypeEnum.SLIDER>
  | FormItemsWithType<FormItemTypeEnum.RATE>
  | FormItemsWithType<FormItemTypeEnum.COLOR_PICKER>
  | FormItemsWithType<FormItemTypeEnum.CASCADER>
  | FormItemsWithType<FormItemTypeEnum.UPLOAD>
  | FormItemsWithType<FormItemTypeEnum.CUSTOM>;

// 表单配置类型
export interface FormConfig {
  formItems: FormItems[]; // 表单项配置
  rules?: FormRules; // 表单验证规则
  labelWidth?: string | number; // 标签宽度
  labelPosition?: "left" | "right" | "top"; // 标签位置
  size?: "large" | "default" | "small"; // 表单尺寸
  disabled?: boolean; // 是否禁用整个表单
  inline?: boolean; // 是否行内表单
  showResetButton?: boolean; // 是否显示重置按钮
  showSubmitButton?: boolean; // 是否显示提交按钮
  submitButtonText?: string; // 提交按钮文本
  resetButtonText?: string; // 重置按钮文本
  gutter?: number; // 栅格间隔
}
