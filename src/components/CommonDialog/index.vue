<script lang="ts" setup>
// 使用 defineModel 替代 modelValue prop 和相关的 emit 事件
const modelValue = defineModel<boolean>("modelValue");

// 其他 props 定义
const props = defineProps<{
  title: string; // 对话框标题
  width?: string; // 对话框宽度
  closeOnClickModal?: boolean; // 是否点击遮罩层关闭
  hideBodyPadding?: boolean;
  hideFooter?: boolean;
}>();

// 定义事件
const emit = defineEmits<{
  (e: "confirm"): void; // 确认事件
  (e: "cancel"): void; // 取消事件
  (e: "close"): void; // 关闭事件
}>();

// 关闭对话框
const closeDialog = () => {
  modelValue.value = false;
  emit("cancel");
};

// 确认按钮处理
const handleConfirm = () => {
  emit("confirm");
};
</script>

<template>
  <el-dialog
    v-model="modelValue"
    :width="width || '40%'"
    :close-on-click-modal="closeOnClickModal ?? false"
    style="--el-dialog-padding-primary: 0"
    destroy-on-close
    @close="emit('close')"
  >
    <template #header>
      <div class="flex items-center px-20 py-16 bg-[#F8F8F8] rounded-4">
        <div class="w-3 h-16 bg-[#3686FD] rounded-2"></div>
        <div class="ml-10 text-[#333] text-base font-500">{{ title }}</div>
      </div>
    </template>

    <div :class="hideBodyPadding ? '' : 'p-20'"><slot></slot></div>

    <template v-if="!hideFooter" #footer>
      <slot name="footer">
        <div class="flex-center py-20">
          <el-button @click="closeDialog">取 消</el-button>
          <el-button type="primary" @click="handleConfirm">确 定</el-button>
        </div>
      </slot>
    </template>
  </el-dialog>
</template>
