export default {
  // 路由国际化
  route: {
    dashboard: "首页",
    document: "项目文档",
  },
  // 登录页面国际化
  login: {
    username: "用户名",
    password: "密码",
    login: "登 录",
    captchaCode: "验证码",
    capsLock: "大写锁定已打开",
    message: {
      username: {
        required: "请输入用户名",
      },
      password: {
        required: "请输入密码",
        min: "密码不能少于6位",
      },
      captchaCode: {
        required: "请输入验证码",
      },
    },
  },
  // 导航栏国际化
  navbar: {
    dashboard: "首页",
    logout: "注销登出",
    document: "项目文档",
    gitee: "项目地址",
  },
  sizeSelect: {
    tooltip: "布局大小",
    default: "默认",
    large: "大型",
    small: "小型",
    message: {
      success: "切换布局大小成功！",
    },
  },
  langSelect: {
    message: {
      success: "切换语言成功！",
    },
  },
  settings: {
    project: "项目配置",
    theme: "主题设置",
    interface: "界面设置",
    navigation: "导航设置",
    themeColor: "主题颜色",
    tagsView: "开启 Tags-View",
    fixedHeader: "固定 Header",
    sidebarLogo: "侧边栏 Logo",
    watermark: "开启水印",
  },
  title: "TRTC 实时音视频",
  logo: "https://web.sdk.qcloud.com/trtc/webrtc/assets/logo/trtc-logo-cn-w.png",
  step1: "步骤 1 : 判断当前环境是否满足条件",
  step2: "步骤 2 : 创建新的应用",
  step3: "步骤 3 : 获取 SDKAppID 和 密钥 SecretKey",
  step4: "步骤 4 : 开始视频通话",
  param: "参数",
  alert:
    "注意️：本 Demo 仅用于调试，正式上线前请将 UserSig 计算代码和密钥迁移到您的后台服务器上，以避免加密密钥泄露导致的流量盗用。",
  click: "查看文档",
  url: "https://cloud.tencent.com/document/product/647/17275",
  device: "设备",
  operation: "操作",
  permit: "请授权您的麦克风和摄像头权限",
  paramsNeed: "请检查参数 SDKAppId, secretKey, userId, roomId 是否输入正确！",
  invite:
    "复制链接邀请好友加入视频通话，一条链接仅可邀请一人，复制后自动更新链接。",
  inviteUrl: "你被邀请参加视频通话！",
  check: "请重新获取邀请链接！",
  deviceHint: "PS: 进房之前请确认当前页面允许使用摄像头和麦克风",
};
