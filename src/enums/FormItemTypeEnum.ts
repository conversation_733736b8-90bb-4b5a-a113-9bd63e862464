export const enum FormItemTypeEnum {
  // 基础表单项
  INPUT = "input",
  TEXTAREA = "textarea",
  SELECT = "select",
  RADIO = "radio",
  CHECKBOX = "checkbox",
  SWITCH = "switch",

  // 日期和时间
  DATE_PICKER = "datePicker",
  TIME_PICKER = "timePicker",
  DATE_TIME_PICKER = "dateTimePicker",

  // 数字和范围
  INPUT_NUMBER = "inputNumber",
  SLIDER = "slider",
  RATE = "rate",

  // 其他
  COLOR_PICKER = "colorPicker",
  CASCADER = "cascader",
  UPLOAD = "upload",

  // 自定义
  CUSTOM = "custom",
}
