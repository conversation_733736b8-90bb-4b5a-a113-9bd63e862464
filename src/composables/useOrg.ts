import RemoteWitnessAPI from "@/api/remote-witness";
import { useUserStore } from "@/store";
import { useQuery } from "@tanstack/vue-query";
import { isNil } from "lodash-es";

export const useOrg = () => {
  const userStore = useUserStore();
  const { data: org, isLoading } = useQuery({
    queryKey: ["org", userStore.user.id],
    queryFn: () =>
      RemoteWitnessAPI.queryOrgById({ id: String(userStore.user.id) }),
    select: (res) =>
      res.org?.map((item) => ({ label: item.name, value: item.id })) ?? [],
    enabled: !isNil(userStore.user.id),
  });

  return { org, isLoading };
};
