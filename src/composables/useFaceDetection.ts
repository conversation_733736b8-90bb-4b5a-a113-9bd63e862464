import { WebSocketAction } from "@/enums/WebSocketActionEnum";
import useRemoteWitnessStore from "@/store/modules/remoteWitness";
import { FaceDetectionResult, FaceInfo } from "@/types/FaceDetectionResult";
import { emitter, EventTypes } from "@/utils/eventBus";
import WebSocketSingleton from "@/utils/WebSocketSingleton";
import * as faceapi from "@vladmandic/face-api";
import { FaceDetectionOptions } from "@vladmandic/face-api";
import { onMounted, onUnmounted, ref } from "vue";
interface FrameData {
  binary: Uint8Array;
  format: string;
  width: number;
  height: number;
}

export function useFaceDetection() {
  // Constants
  const MAX_HISTORY_SIZE = 100;
  const CAPTURE_INTERVAL = 2000;
  const DETECTION_INTERVAL = 300;
  const REQUIRED_FACE_COUNT = 1; // 添加规定的人脸数量常量
  const INIT_BUFFER_COUNT = 4; // 添加初始化缓冲次数常量

  const FACE_QUALITY_THRESHOLDS = {
    SCORE: 40,
    BRIGHTNESS: {
      MIN: 30,
      MAX: 70,
    },
    SHARPNESS: 60,
    COMPLETENESS: {
      EYE: 45,
      EYEBROW: 80,
      NOSE: 60,
      MOUTH: 50,
      CHEEK: 50,
      CHIN: 70,
    },
  } as const;

  const COMPARE_SCORE_THRESHOLDS = {
    EXCELLENT: 90,
    GOOD: 70,
    FAIR: 50,
  } as const;

  const MESSAGE_TEMPLATES = {
    NO_FACE: "视频中未检测到人脸",
    FACE_PREFIX: (index: number) => `人脸${index + 1}: `,
    MULTIPLE_FACES: (num: number) => `视频中检测到${num}人在框`,
    QUALITY: {
      GOOD: "人脸质量良好",
      POOR: "图像质量较差",
      DARK: "光线太暗",
      BRIGHT: "光线太强",
      BLUR: "图像不够清晰",
    },
    COMPARE: {
      EXCELLENT: "", //人脸对比结果：匹配度非常高, 检测通过就不展示了
      GOOD: "", //人脸对比结果：匹配度较高, 检测通过就不展示了
      FAIR: "人脸对比结果：匹配度一般",
      POOR: "人脸对比结果：匹配度较低",
    },
    OCCLUSION: (parts: string[]) => `${parts.join("、")}被遮挡`,
    ADJUST: "请调整",
  } as const;

  const MESSAGE_TEMPLATES_PAD = {
    NO_FACE: "视频中未检测到人脸，请确保本人在框",
    FACE_PREFIX: (index: number) => `人脸${index + 1}: `,
    MULTIPLE_FACES: (num: number) =>
      `视频中检测到${num}人在框，请确保仅本人在框`,
    QUALITY: {
      GOOD: "人脸质量良好",
      POOR: "图像质量较差",
      DARK: "光线太暗",
      BRIGHT: "光线太强",
      BLUR: "图像不够清晰",
    },
    COMPARE: {
      EXCELLENT: "", //人脸对比结果：匹配度非常高, 检测通过就不展示了
      GOOD: "", //人脸对比结果：匹配度较高, 检测通过就不展示了
      FAIR: "人脸比对匹配度一般, 请确保本人在框",
      POOR: "人脸比对识别度太低，请确保本人在框", //对比结果：匹配度较低
    },
    OCCLUSION: (parts: string[]) => `${parts.join("、")}被遮挡`,
    ADJUST: "请调整",
  } as const;

  const store = useRemoteWitnessStore();
  const qualityAlert = ref<string | null>(null);
  // State refs
  const detectionResult = ref<FaceDetectionResult>(
    createInitialDetectionResult()
  );

  const detectionHistory = ref<FaceDetectionResult[]>([]);
  const isProcessing = ref(false);
  let worker: Worker | null = null;
  // Private state
  const modelsLoaded = false;
  let canvas: OffscreenCanvas | HTMLCanvasElement | null = null;
  let ctx: CanvasRenderingContext2D | null = null;
  let captureInterval: number | null = null;
  const regularUpdateInterval: number | null = null;
  const detectFacesTimeout: number | null = null;
  let hasSendReferenceImage = false;
  let optionsSSDMobileNet: FaceDetectionOptions;
  const ws = WebSocketSingleton.getInstance();
  let wsCleanup: (() => void) | null = null;

  function createInitialDetectionResult(): FaceDetectionResult {
    return {
      faceCount: 0,
      faces: [],
      compareScore: 0,
      detail: "",
      timestamp: "",
      elapsedSeconds: 0,
    };
  }
  // 将二进制数据转换为base64（如果WebSocket API需要）
  function binaryToBase64(data: Uint8Array, format: string): string {
    const bytes = String.fromCharCode(...data);
    return `data:${format};base64,${btoa(bytes)}`;
  }

  function updateHistory(
    result: FaceDetectionResult,
    forceUpdate: boolean = false,
    resultForPad: FaceDetectionResult = result
  ) {
    // 为坐席端生成的详细信息，保持原有的逻辑
    const newHistoryItem = {
      ...result,
      timestamp: store.formattedTime,
      elapsedSeconds: store.elapsedSeconds,
    };

    const newHistoryItemForPad = {
      ...resultForPad,
      timestamp: store.formattedTime,
      elapsedSeconds: store.elapsedSeconds,
    };

    // 检查是否需要更新历史记录
    const shouldUpdate =
      forceUpdate ||
      hasResultChanged(result) ||
      hasFaceQualityIssues(result) ||
      hasLowCompareScore(result);

    if (shouldUpdate) {
      detectionHistory.value.push(newHistoryItem);
      store.updateDetectionHistory(newHistoryItem);
      if (!store.useSocketNotification) {
        return;
      }

      // 发送原始的详细信息到坐席端
      ws.publish({
        destination: "/message",
        body: JSON.stringify({
          recipient: store.connectId,
          content: JSON.stringify({
            action: WebSocketAction.FACE_DETECTION_RESULT,
            content: newHistoryItemForPad.detail,
            asrFail: newHistoryItemForPad.detail.includes("客户回答内容"),
          }),
        }),
      });

      // // 为客户端生成的详细信息，使用 MESSAGE_TEMPLATES_PAD
      // let padDetail = "";

      // // 如果结果中有比较分数，则包含比较结果
      // if (result.compareScore > 0) {
      //   // 生成客户端的人脸识别结果
      //   const faceDetail = genDetail(true);
      //   // 生成客户端的比较结果
      //   const compareDetail = genCompareDetail(result.compareScore, true);

      //   // 如果比较结果不为空，则添加到详细信息中
      //   if (compareDetail) {
      //     padDetail = `${faceDetail}；${compareDetail}`;
      //   } else {
      //     padDetail = faceDetail;
      //   }
      // } else {
      //   // 只生成人脸识别结果
      //   padDetail = genDetail(true);
      // }

      // console.log("🚀 ~ useFaceDetection ~ padDetail:", padDetail);

      // // 发送为客户端定制的详细信息
      // ws.publish({
      //   destination: "/message",
      //   body: JSON.stringify({
      //     recipient: store.connectId,
      //     content: JSON.stringify({
      //       action: WebSocketAction.FACE_DETECTION_RESULT,
      //       content: padDetail,
      //       asrFail: padDetail.includes("客户回答内容"),
      //     }),
      //   }),
      // });
    }
  }

  // 检查人脸质量问题
  function hasFaceQualityIssues(result: FaceDetectionResult): boolean {
    return result.faces.some((face) => {
      const quality = face.faceQualityInfo;
      const completeness = quality.completeness;
      const thresholds = FACE_QUALITY_THRESHOLDS;

      // 检查基本质量指标
      if (quality.score < thresholds.SCORE) return true;
      if (
        quality.brightness < thresholds.BRIGHTNESS.MIN ||
        quality.brightness > thresholds.BRIGHTNESS.MAX
      )
        return true;
      if (quality.sharpness < thresholds.SHARPNESS) return true;

      // 检查五官遮挡
      if (completeness.eye < thresholds.COMPLETENESS.EYE) return true;
      if (completeness.eyebrow < thresholds.COMPLETENESS.EYEBROW) return true;
      if (completeness.nose < thresholds.COMPLETENESS.NOSE) return true;
      if (completeness.mouth < thresholds.COMPLETENESS.MOUTH) return true;
      if (completeness.cheek < thresholds.COMPLETENESS.CHEEK) return true;
      if (completeness.chin < thresholds.COMPLETENESS.CHIN) return true;

      return false;
    });
  }

  // 检查人脸对比分数
  function hasLowCompareScore(result: FaceDetectionResult): boolean {
    return (
      hasSendReferenceImage &&
      result.compareScore < COMPARE_SCORE_THRESHOLDS.GOOD
    );
  }
  function hasResultChanged(result: FaceDetectionResult): boolean {
    // 检查当前人脸数量是否等于规定数量
    return result.faceCount !== REQUIRED_FACE_COUNT;
  }

  async function setupTensorflowEnvironment() {
    await faceapi.tf.setBackend("webgl");
    await faceapi.tf.ready();

    const env = faceapi.tf?.env();
    if (!env) return;

    const optimizations = {
      WEBGL_VERSION: 2,
      WEBGL_FORCE_F16_TEXTURES: true,
      WEBGL_EXP_CONV: true,
    };

    Object.entries(optimizations).forEach(([flag, value]) => {
      if (env.flagRegistry[flag]) {
        env.set(flag, value);
      }
    });
  }

  async function loadModels() {
    // if (modelsLoaded) return;
    //
    // try {
    //   await setupTensorflowEnvironment();
    //   const baseUrl = `${import.meta.env.BASE_URL}models`;
    //
    //   await Promise.all([
    //     faceapi.nets.ssdMobilenetv1.load(baseUrl),
    //     faceapi.nets.faceLandmark68Net.load(baseUrl),
    //   ]);
    //
    //   optionsSSDMobileNet = new faceapi.SsdMobilenetv1Options({
    //     minConfidence: 0.5,
    //     maxResults: 3,
    //   });
    //
    //   modelsLoaded = true;
    //   console.log("Face detection models loaded successfully");
    // } catch (error) {
    //   console.error("Failed to load face-api models:", error);
    //   throw error;
    // }
  }

  // 添加初始化计数器
  const initDetectionCount = 0;

  async function detectFaces(
    video: HTMLVideoElement,
    init: boolean
  ): Promise<void> {
    // if (!modelsLoaded) {
    //   await loadModels();
    // }
    //
    // if (detectFacesTimeout) {
    //   clearTimeout(detectFacesTimeout);
    // }
    //
    // try {
    //   const detections = await faceapi
    //     .detectAllFaces(video, optionsSSDMobileNet)
    //     .withFaceLandmarks();
    //   detectionResult.value.faceCount = detections.length;
    //   detectionResult.value = {
    //     ...detectionResult.value,
    //     faceCount: detections.length,
    //     detail: genDetail(),
    //     timestamp: store.formattedTime,
    //   };
    //
    //   // 只有在初始化缓冲期结束后才更新历史
    //   if (init) {
    //     if (initDetectionCount >= INIT_BUFFER_COUNT) {
    //       updateHistory(detectionResult.value);
    //     } else {
    //       initDetectionCount++;
    //     }
    //   }
    //
    //   detectFacesTimeout = window.setTimeout(() => {
    //     detectFaces(video, true);
    //   }, DETECTION_INTERVAL);
    // } catch (error) {
    //   console.error("Face detection failed:", error);
    //   detectionResult.value = createInitialDetectionResult();
    //   // 错误情况下也要考虑初始化缓冲期
    //   if (initDetectionCount >= INIT_BUFFER_COUNT) {
    //     updateHistory(detectionResult.value);
    //   }
    // }
  }

  function initializeWorker(video: HTMLVideoElement) {
    if (worker) return;

    return new Promise<Worker>((resolve, reject) => {
      worker = new Worker(
        new URL("../worker/captureWorker.workers.ts", import.meta.url)
      );

      // 创建优化尺寸的canvas（可以根据需要调整分辨率）
      const width = Math.min(video.videoWidth, 640); // 限制最大宽度
      const height = Math.floor(video.videoHeight * (width / video.videoWidth));
      const canvas = new OffscreenCanvas(width, height);

      worker.onmessage = (e) => {
        const { type, data } = e.data;

        switch (type) {
          case "initialized":
            resolve(worker!);
            break;
          case "frame":
            const frameData = data as FrameData;
            WebSocketSingleton.getInstance().publish({
              destination: "faceDetection",
              body: frameData.binary,
              contentType: frameData.format,
            });
            // 如果WebSocket支持二进制传输
            // if (WebSocketSingleton.getInstance().supportsBinary()) {
            //
            // } else {
            //   // 如果需要base64
            //   const base64 = binaryToBase64(frameData.binary, frameData.format);
            //   WebSocketSingleton.getInstance().publish({
            //     destination: "faceDetection",
            //     body: { base64Image: base64 },
            //   });
            // }
            break;
          case "error":
            console.error(data);
            break;
        }
      };

      worker.postMessage(
        {
          type: "init",
          data: { canvas },
        },
        [canvas]
      );
    });
  }
  async function startCapture(video: HTMLVideoElement) {
    try {
      // 如果WebSocket订阅已被清理，则重新初始化它们。
      if (!wsCleanup) {
        wsCleanup = initializeWebSocket();
      }

      const initializedWorker = await initializeWorker(video);

      captureInterval = window.setInterval(async () => {
        if (!video.videoWidth || !video.videoHeight) return;

        // if (!detectionResult.value.faceCount) {
        //   updateHistory({ ...detectionResult.value, detail: genDetail() });
        //   return;
        // }
        try {
          // 使用 requestAnimationFrame 来优化性能
          requestAnimationFrame(async () => {
            const newFrame = await createImageBitmap(video, {
              resizeQuality: "medium", // 可选：'pixelated'|'low'|'medium'|'high'
            });

            initializedWorker?.postMessage(
              {
                type: "frame",
                data: { videoFrame: newFrame },
              },
              [newFrame]
            );
          });
        } catch (error) {
          console.error("Failed to create video frame:", error);
        }
      }, CAPTURE_INTERVAL);
    } catch (error) {
      console.error("Failed to initialize worker:", error);
    }
  }
  function setupResizeObserver(video: HTMLVideoElement) {
    const observer = new ResizeObserver(async (entries) => {
      for (const entry of entries) {
        if (entry.target === video && worker) {
          stopCapture();
          await startCapture(video);
        }
      }
    });

    observer.observe(video);
    return observer;
  }

  function stopCapture() {
    if (captureInterval) {
      clearInterval(captureInterval);
      captureInterval = null;
    }

    if (worker) {
      worker.postMessage({ type: "stop" });
      worker.terminate();
      worker = null;
    }
  }
  // 发送图片
  const sendReferenceImage = async (filepath: string) => {
    hasSendReferenceImage = false;
    // const imageElement = document
    //   .getElementById("myImage")
    //   ?.querySelector("img"); // 获取 <img> 元素
    // if (imageElement) {
    //   const base64Image = await convertImageToBase64(imageElement); // 转换为 Base64
    if (filepath) {
      // 发送 Base64 图片数据
      const res = await WebSocketSingleton.getInstance().publish({
        destination: "/set_reference_face",
        body: filepath, // 发送 filepath 数据
      });
      hasSendReferenceImage = true;
    } else {
      console.error("无filepath");
    }
    // } else {
    //   console.error("未找到图片元素");
    // }
  };
  // 将图片转换为 Base64
  const convertImageToBase64 = (
    imageElement: HTMLImageElement
  ): Promise<string> => {
    return new Promise((resolve) => {
      const canvas = document.createElement("canvas");
      const ctx = canvas.getContext("2d");

      if (!ctx) {
        resolve("");
        return;
      }

      // 设置 canvas 的宽高与图片一致
      canvas.width = imageElement.naturalWidth;
      canvas.height = imageElement.naturalHeight;

      // 将图片绘制到 canvas 上
      ctx.drawImage(imageElement, 0, 0, canvas.width, canvas.height);

      // 将 canvas 转换为 Base64
      const base64 = canvas.toDataURL("image/jpeg"); // 可以指定格式，如 "image/png"
      resolve(base64);
    });
  };

  function processDetectionResponse(data: any) {
    if (!Array.isArray(data)) {
      console.error("Invalid detection data format");
      // 更新检测结果
      detectionResult.value.faceCount = 0;
      detectionResult.value = {
        faces: [],
        faceCount: 0,
        timestamp: store.formattedTime,
        elapsedSeconds: store.elapsedSeconds,
        compareScore: 0,
        detail: "",
      };
      const detail = data?.error || genDetail(false);
      if (detail) {
        detectionResult.value.detail = detail;
        updateHistory(detectionResult.value, false, {
          ...detectionResult.value,
          detail: data?.error || genDetail(true),
        });
      }
      return;
    }
    // if (detectionResult.value.faceCount === 0) {
    //   return;
    // }

    const faces: FaceInfo[] = data.map((face) => ({
      width: face.width,
      height: face.height,
      x: face.x,
      y: face.y,
      faceAttributesInfo: {
        mask: face.faceAttributesInfo.mask,
        eyeOpen: face.faceAttributesInfo.eyeOpen,
        age: face.faceAttributesInfo.age,
        pitch: face.faceAttributesInfo.pitch,
        yaw: face.faceAttributesInfo.yaw,
        roll: face.faceAttributesInfo.roll,
        glass: face.faceAttributesInfo.glass,
        beauty: face.faceAttributesInfo.beauty,
        hat: face.faceAttributesInfo.hat,
        expression: face.faceAttributesInfo.expression,
        gender: face.faceAttributesInfo.gender,
      },
      faceQualityInfo: {
        score: face.faceQualityInfo.score,
        brightness: face.faceQualityInfo.brightness,
        sharpness: face.faceQualityInfo.sharpness,
        completeness: {
          eyebrow: face.faceQualityInfo.completeness.eyebrow,
          eye: face.faceQualityInfo.completeness.eye,
          nose: face.faceQualityInfo.completeness.nose,
          cheek: face.faceQualityInfo.completeness.cheek,
          mouth: face.faceQualityInfo.completeness.mouth,
          chin: face.faceQualityInfo.completeness.chin,
        },
      },
    }));

    // 更新检测结果
    detectionResult.value.faceCount = faces.length;
    detectionResult.value = {
      faces,
      faceCount: faces.length,
      timestamp: store.formattedTime,
      elapsedSeconds: store.elapsedSeconds,
      compareScore: detectionResult.value.compareScore,
      detail: "",
    };
    const detail = genDetail(false);
    if (detail) {
      detectionResult.value.detail = detail;
      updateHistory(detectionResult.value, false, {
        ...detectionResult.value,
        detail: genDetail(true),
      });
    }
  }
  function initializeWebSocket() {
    const clearSubscribe = WebSocketSingleton.getInstance().subscribe(
      "/facedetection_result",
      (data) => {
        processDetectionResponse(data);
      }
    );

    const clearSubscribe2 = WebSocketSingleton.getInstance().subscribe(
      "/facecompare_result",
      (data) => {
        processCompareResponse(data);
      }
    );
    const handleFaceDetectionUpdate = (data: any) => {
      if (data.result) {
        detectionResult.value = {
          ...detectionResult.value,
          detail: data.result.detail,
          timestamp: store.formattedTime,
          elapsedSeconds: store.elapsedSeconds,
        };
        updateHistory(detectionResult.value, data.forceUpdate);
      }
    };
    // 监听语音识别
    emitter.on(EventTypes.FACE_DETECTION_UPDATE, handleFaceDetectionUpdate);

    return () => {
      emitter.off(EventTypes.FACE_DETECTION_UPDATE, handleFaceDetectionUpdate);
      clearSubscribe();
      clearSubscribe2();
    };
  }
  function processCompareResponse(data: any) {
    if (typeof data !== "number") {
      console.error("Invalid compare data format");
      return;
    }
    if (detectionResult.value.faceCount === 0) {
      return;
    }
    // 获取对比分数
    const compareScore = data;

    // 更新检测结果中的对比分数
    detectionResult.value.compareScore = compareScore;

    // 生成对比结果的提示信息
    const compareDetail = genCompareDetail(compareScore);

    // 更新检测结果的详细信息
    detectionResult.value.detail = `${detectionResult.value.detail}；${compareDetail}`;

    // 更新历史记录
    updateHistory(detectionResult.value, false, {
      ...detectionResult.value,
      detail: genDetail(true),
    });
  }
  function genCompareDetail(score: number, forPad: boolean = false): string {
    const { EXCELLENT, GOOD, FAIR } = COMPARE_SCORE_THRESHOLDS;
    const messages = forPad
      ? MESSAGE_TEMPLATES_PAD.COMPARE
      : MESSAGE_TEMPLATES.COMPARE;

    if (score >= EXCELLENT) {
      return messages.EXCELLENT;
    } else if (score >= GOOD) {
      return messages.GOOD;
    } else if (score >= FAIR) {
      return messages.FAIR;
    } else {
      return messages.POOR;
    }
  }

  function genDetail(forPad: boolean = false): string {
    const faces = detectionResult.value.faces;
    const templates = forPad ? MESSAGE_TEMPLATES_PAD : MESSAGE_TEMPLATES;

    if (detectionResult.value.faceCount === 0) {
      return templates.NO_FACE;
    }
    if (!faces || faces.length === 0) {
      return templates.NO_FACE;
    }

    const faceAlerts: string[] = faces.map((face, index) => {
      const alerts: string[] = [];
      const quality = face.faceQualityInfo;
      const completeness = quality.completeness;
      const thresholds = FACE_QUALITY_THRESHOLDS;
      const messages = templates.QUALITY;

      // 添加人脸序号（当有多张人脸时）
      const facePrefix = faces.length > 1 ? templates.FACE_PREFIX(index) : "";

      // 检查基本质量指标
      if (quality.score < thresholds.SCORE) {
        alerts.push(messages.POOR);
      } else if (quality.brightness < thresholds.BRIGHTNESS.MIN) {
        alerts.push(messages.DARK);
      } else if (quality.brightness > thresholds.BRIGHTNESS.MAX) {
        alerts.push(messages.BRIGHT);
      } else if (quality.sharpness < thresholds.SHARPNESS) {
        alerts.push(messages.BLUR);
      }

      // 检查五官遮挡
      const occlusionAlerts: string[] = [];
      if (completeness.eye < thresholds.COMPLETENESS.EYE)
        occlusionAlerts.push("眼睛");
      if (completeness.eyebrow < thresholds.COMPLETENESS.EYEBROW)
        occlusionAlerts.push("眉毛");
      if (completeness.nose < thresholds.COMPLETENESS.NOSE)
        occlusionAlerts.push("鼻子");
      if (completeness.mouth < thresholds.COMPLETENESS.MOUTH)
        occlusionAlerts.push("嘴巴");
      if (completeness.cheek < thresholds.COMPLETENESS.CHEEK)
        occlusionAlerts.push("脸颊");
      if (completeness.chin < thresholds.COMPLETENESS.CHIN)
        occlusionAlerts.push("下巴");

      // 如果有遮挡，添加遮挡提示
      if (occlusionAlerts.length > 0) {
        alerts.push(templates.OCCLUSION(occlusionAlerts));
      }

      // 如果没有任何问题
      if (alerts.length === 0) {
        // return `${facePrefix}${messages.GOOD}`;
        return ""; // Good的信息就不要记录了
      }

      // 拼接所有提示
      return `${facePrefix}${alerts.join("，")}，${templates.ADJUST}`;
    });

    if (faces.length > 1) {
      // 如果有超过1张人脸，添加提示
      faceAlerts.unshift(templates.MULTIPLE_FACES(faces.length));
    }

    // 返回所有人脸的提示信息
    return faceAlerts.filter((alert) => alert).join("；");
  }
  onMounted(async () => {
    await loadModels();
    wsCleanup = initializeWebSocket();
  });

  //清理函数
  function clearupFaceDetection() {
    stopCapture();
    // 清理WebSocket订阅并重置清理函数。
    if (wsCleanup) {
      wsCleanup();
      wsCleanup = null; // 重要：设置为null，以便知道稍后需要重新初始化
    }
    if (detectFacesTimeout) {
      clearTimeout(detectFacesTimeout);
    }
    if (regularUpdateInterval) {
      clearTimeout(regularUpdateInterval);
    }
    if (canvas instanceof HTMLCanvasElement) {
      canvas.remove();
    }
    canvas = null;
    ctx = null;
  }
  onUnmounted(() => {
    clearupFaceDetection();
  });
  // // 启动定期更新历史（可选，根据需求保留或删除）
  // function startRegularUpdates() {
  //   regularUpdateInterval = window.setInterval(() => {
  //     if (detectionResult.value) {
  //       updateHistory(detectionResult.value, true);
  //     }
  //   }, 2000);
  // }
  return {
    detectFaces,
    startCapture,
    sendReferenceImage,
    stopCapture,
    detectionResult,
    detectionHistory,
    isProcessing,
    qualityAlert,
    clearupFaceDetection,
  };
}
