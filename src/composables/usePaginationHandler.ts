import { Ref } from "vue";

export enum OperationType {
  CREATE = "create",
  UPDATE = "update",
  DELETE = "delete",
  BATCH_DELETE = "batch_delete",
}

export interface PaginationInfo {
  currentPage: number;
  pageSize: number;
  total: number;
}

export interface PaginationHandlerOptions {
  // 新增操作后的跳转策略
  createStrategy?: "first" | "last" | "stay";
  // 是否自动重新获取数据
  autoRefetch?: boolean;
}

export function usePaginationHandler(
  queryParams: Ref<{
    currentPage: number;
    pageSize: number;
    [key: string]: any;
  }>,
  refetch: () => void,
  options: PaginationHandlerOptions = {}
) {
  const { createStrategy = "first", autoRefetch = true } = options;

  /**
   * 处理操作后的分页逻辑
   * @param operationType 操作类型
   * @param pagination 当前分页信息
   * @param deletedCount 删除的数据数量（删除操作时需要）
   */
  const handleOperationSuccess = (
    operationType: OperationType,
    pagination: PaginationInfo,
    deletedCount: number = 1
  ) => {
    const { currentPage, pageSize, total } = pagination;

    switch (operationType) {
      case OperationType.CREATE:
        // 新增操作：根据策略跳转页面
        if (createStrategy === "first") {
          queryParams.value.currentPage = 1;
        } else if (createStrategy === "last") {
          // 计算最后一页
          const lastPage = Math.ceil((total + 1) / pageSize);
          queryParams.value.currentPage = lastPage;
        }
        // "stay" 策略保持当前页不变
        break;

      case OperationType.DELETE:
      case OperationType.BATCH_DELETE:
        // 删除操作：检查当前页是否还有数据
        const newTotal = total - deletedCount;
        const currentPageStartIndex = (currentPage - 1) * pageSize;

        if (currentPageStartIndex >= newTotal && newTotal > 0) {
          // 当前页没有数据了，需要回到上一页
          const newPage = Math.ceil(newTotal / pageSize);
          queryParams.value.currentPage = Math.max(1, newPage);
        }
        // 如果当前页还有数据，保持不变
        break;

      case OperationType.UPDATE:
        // 修改操作：保持当前页不变
        break;
    }

    // 自动重新获取数据
    if (autoRefetch) {
      refetch();
    }
  };

  /**
   * 创建操作成功的处理函数
   */
  const handleCreateSuccess = (pagination: PaginationInfo) => {
    handleOperationSuccess(OperationType.CREATE, pagination);
  };

  /**
   * 更新操作成功的处理函数
   */
  const handleUpdateSuccess = () => {
    if (autoRefetch) {
      refetch();
    }
  };

  /**
   * 删除操作成功的处理函数
   */
  const handleDeleteSuccess = (
    pagination: PaginationInfo,
    deletedCount: number = 1
  ) => {
    const operationType =
      deletedCount > 1 ? OperationType.BATCH_DELETE : OperationType.DELETE;
    handleOperationSuccess(operationType, pagination, deletedCount);
  };

  /**
   * 通用的操作成功处理函数
   */
  const handleOperationSuccessGeneric = (
    operationType: OperationType,
    pagination?: PaginationInfo,
    deletedCount?: number
  ) => {
    if (operationType === OperationType.UPDATE) {
      handleUpdateSuccess();
    } else if (pagination) {
      handleOperationSuccess(operationType, pagination, deletedCount);
    }
  };

  return {
    handleCreateSuccess,
    handleUpdateSuccess,
    handleDeleteSuccess,
    handleOperationSuccessGeneric,
    OperationType,
  };
}
