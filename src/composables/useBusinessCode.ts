import ClientApi from "@/api/client";
import { useQuery } from "@tanstack/vue-query";

export const useBusinessCode = () => {
  const { data: bizCodeList } = useQuery({
    queryKey: [
      "/witness/client/getWitnessDict",
      { tableName: "business_code" },
    ],
    queryFn: () =>
      ClientApi.getWitnessDict<{
        bizCodeList: { bizCode: string; bizName: string; bizLevel: string }[];
      }>({ tableName: "business_code" }),
    select: (res) => {
      return res?.bizCodeList;
    },
    staleTime: 1000 * 60 * 60, // 1小时
  });
  return bizCodeList;
};
