import RemoteWitnessAPI from "@/api/remote-witness";
import { useQuery } from "@tanstack/vue-query";
import type { Ref } from "vue";

export type DictItem = {
  fldm: string;
  note: string;
  flag: string;
  ibm: string;
  flmc: string;
  type: string;
  cbm: string;
};

export interface UseDictOptions {
  enabled?: boolean;
  staleTime?: number;
  cacheTime?: number;
}

/**
 * 通用字典获取 hooks
 * @param fldm 字典字段代码，多个用分号分隔，如 "GT_ZJLB;GT_KHZT"
 * @param options 查询配置选项
 * @returns 返回字典数据和查询状态
 */
export function useDict(
  fldm: Ref<string> | string,
  options: UseDictOptions = {}
) {
  const {
    enabled = true,
    staleTime = 5 * 60 * 1000, // 默认5分钟缓存
    cacheTime = 10 * 60 * 1000, // 默认10分钟垃圾回收
  } = options;

  const fldmValue = typeof fldm === "string" ? fldm : fldm.value;

  const queryResult = useQuery({
    queryKey: ["dict", fldmValue],
    queryFn: async () => {
      const response = await RemoteWitnessAPI.getDict({ fldm: fldmValue });
      return response.dictionary as Record<string, DictItem[]>;
    },
    enabled: enabled && !!fldmValue,
    staleTime,
    gcTime: cacheTime, // vue-query v5 使用 gcTime 替代 cacheTime
    retry: 2,
    retryDelay: 1000,
  });

  return {
    dictMap: computed(() => queryResult.data.value || {}),
    isLoading: queryResult.isLoading,
    isError: queryResult.isError,
    error: queryResult.error,
    refetch: queryResult.refetch,
  };
}
