# usePaginationHandler 分页处理工具

## 概述

`usePaginationHandler` 是一个用于统一处理增删改操作后分页逻辑的工具函数。它能够智能地处理不同操作类型对分页的影响，提供最佳的用户体验。

## 功能特性

- ✅ **新增操作**：支持跳转到第一页、最后一页或保持当前页
- ✅ **删除操作**：智能处理单个/批量删除后的分页调整
- ✅ **修改操作**：保持当前页不变
- ✅ **自动重新获取数据**：可配置是否自动调用 refetch
- ✅ **TypeScript 支持**：完整的类型定义

## 基本用法

```typescript
import { usePaginationHandler } from "@/composables/usePaginationHandler";

// 在组件中使用
const queryParams = ref({
  currentPage: 1,
  pageSize: 10,
  // 其他查询参数...
});

const { data, refetch } = useQuery({
  // 查询配置...
});

// 初始化分页处理工具
const { handleCreateSuccess, handleUpdateSuccess, handleDeleteSuccess } = usePaginationHandler(
  queryParams,
  refetch,
  { 
    createStrategy: "first", // 新增后跳转到第一页
    autoRefetch: true        // 自动重新获取数据
  }
);
```

## 配置选项

### PaginationHandlerOptions

```typescript
interface PaginationHandlerOptions {
  // 新增操作后的跳转策略
  createStrategy?: "first" | "last" | "stay";
  // 是否自动重新获取数据
  autoRefetch?: boolean;
}
```

- `createStrategy`: 新增成功后的分页跳转策略
  - `"first"`: 跳转到第一页（默认）
  - `"last"`: 跳转到最后一页
  - `"stay"`: 保持当前页不变
- `autoRefetch`: 是否自动调用 refetch 函数（默认为 true）

## API 参考

### 返回的处理函数

#### handleCreateSuccess(pagination)

处理新增操作成功后的分页逻辑。

```typescript
const handleRevealInfoSuccess = () => {
  if (dialogTitle.value === "新增") {
    handleCreateSuccess({
      currentPage: queryParams.value.currentPage,
      pageSize: queryParams.value.pageSize,
      total: data.value?.total ?? 0,
    });
  } else {
    handleUpdateSuccess();
  }
};
```

#### handleUpdateSuccess()

处理修改操作成功后的分页逻辑（保持当前页不变）。

```typescript
const handleScriptConfigSuccess = () => {
  handleUpdateSuccess();
};
```

#### handleDeleteSuccess(pagination, deletedCount?)

处理删除操作成功后的分页逻辑。

```typescript
// 单个删除
handleDeleteSuccess({
  currentPage: queryParams.value.currentPage,
  pageSize: queryParams.value.pageSize,
  total: data.value?.total ?? 0,
});

// 批量删除
handleDeleteSuccess(
  {
    currentPage: queryParams.value.currentPage,
    pageSize: queryParams.value.pageSize,
    total: data.value?.total ?? 0,
  },
  selectedRows.length // 删除的数量
);
```

## 完整示例

```typescript
<script setup lang="ts">
import { usePaginationHandler } from "@/composables/usePaginationHandler";

const queryParams = ref({
  businessCode: "",
  currentPage: 1,
  pageSize: 10,
});

const { data, refetch } = useQuery({
  queryKey: ["list", queryParams],
  queryFn: async () => {
    // 获取数据的逻辑
  },
});

// 初始化分页处理工具
const { handleCreateSuccess, handleUpdateSuccess, handleDeleteSuccess } = usePaginationHandler(
  queryParams,
  refetch,
  { createStrategy: "first" }
);

// 删除操作
const handleDelete = async (selectedRows: any[]) => {
  try {
    if (selectedRows.length > 1) {
      // 批量删除
      await Promise.all(selectedRows.map(row => api.delete(row.id)));
      handleDeleteSuccess(
        {
          currentPage: queryParams.value.currentPage,
          pageSize: queryParams.value.pageSize,
          total: data.value?.total ?? 0,
        },
        selectedRows.length
      );
    } else {
      // 单个删除
      await api.delete(selectedRows[0].id);
      handleDeleteSuccess({
        currentPage: queryParams.value.currentPage,
        pageSize: queryParams.value.pageSize,
        total: data.value?.total ?? 0,
      });
    }
    ElMessage.success("删除成功");
  } catch (error) {
    ElMessage.error("删除失败");
  }
};

// 新增/修改成功回调
const handleFormSuccess = () => {
  if (dialogTitle.value === "新增") {
    handleCreateSuccess({
      currentPage: queryParams.value.currentPage,
      pageSize: queryParams.value.pageSize,
      total: data.value?.total ?? 0,
    });
  } else {
    handleUpdateSuccess();
  }
};
</script>
```

## 分页逻辑说明

### 新增操作
- **第一页策略 (first)**：跳转到第一页，适用于按时间倒序排列的列表
- **最后一页策略 (last)**：跳转到最后一页，适用于按时间正序排列的列表
- **保持策略 (stay)**：保持当前页不变

### 删除操作
- **单个删除**：检查删除后当前页是否还有数据，如果没有则回到上一页
- **批量删除**：根据删除数量计算新的分页位置
- **智能计算**：确保用户始终能看到有效的数据页面

### 修改操作
- **保持当前页**：修改操作不影响数据总量，保持用户当前的浏览上下文

## 注意事项

1. **分页信息准确性**：确保传入的 `total` 值是操作前的数据总量
2. **删除数量**：批量删除时需要准确传入删除的数据数量
3. **异步操作**：在异步操作成功后再调用处理函数
4. **错误处理**：只在操作成功时调用分页处理函数 