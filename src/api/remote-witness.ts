import {
  GetYxrwmxParams,
  GetYxrwmxResponse,
  GetZsrwjgResponse,
} from "@/types/remote-witness";
import request from "@/utils/request";

const RemoteWitnessAPI = {
  invite(userid: any, message: any) {
    return request<any, any>({
      url: `/send/invite`,
      method: "post",
      data: { message, userid },
    });
  },
  genInviteUrl(userid: any, message: any) {
    return request<any, any>({
      url: `/inviteUrl`,
      method: "post",
      data: { message, userid },
    });
  },
  getBusinessData(enywqqid: string) {
    return request<any, any>({
      url: `/test/getBusinessData`,
      method: "post",
      data: { enywqqid },
    });
  },
  getBusinessSummary(ywqqid: string) {
    return request<any, any>({
      url: `/test/getBusinessSummary`,
      method: "post",
      data: { ywqqid },
    });
  },

  /**
   * 查询退回原因
   * @param khh 客户号
   * @param ywdm 业务代码
   */
  cxkhdrywfqcsjthyy(khh: string, ywdm: string) {
    return request<any, any>({
      url: `/test/cxkhdrywfqcsjthyy`,
      method: "post",
      data: { khh, ywdm },
    });
  },

  /**
   * 获取字典
   * @returns
   */
  getDict({ fldm }: { fldm: string }) {
    return request<{ fldm: string }, { dictionary: Record<string, any[]> }>({
      url: `/test/queryDictionary`,
      method: "post",
      data: { fldm },
    });
  },

  /**公民身份验证 */
  mpsVerificationApplication(data: {
    sfzh: string;
    xm: string;
    cxlx: string;
    cfbz: string;
    zjlb: string;
  }) {
    return request<any, any>({
      url: `/test/mpsVerificationApplication`,
      method: "post",
      data,
    });
  },

  /**查询公民身份验证申请表*/
  mpsVerificationResult(data: { id: string; sfzh: string; xm: string }) {
    return request<any, any>({
      url: `/test/mpsVerificationResult`,
      method: "post",
      data,
    });
  },

  /**
   *  修改reqdata
   * {
                "ywqqid": "440592",
                "data" : {
                	"ceshiceshi" : "132123"
                }
            }
   */
  modifyAcceptantApi(data: { ywqqid: string; data: Record<string, any> }) {
    return request<any, any>({
      url: `/test/modifyAccept`,
      method: "post",
      data,
    });
  },

  /**
   * 根据id查询营业部
   * @param id
   * @returns
   */
  queryOrgById({ id }: { id: string }) {
    return request<
      any,
      { org: { fid: string; orgcode: string; name: string; id: string }[] }
    >({
      url: `/test/queryOrgById`,
      method: "post",
      data: { gyid: id },
    });
  },
  /**
   * 图片比对
   * @param base641 图片1的Base64数据
   * @param base642 图片2的Base64数据
   * @returns
   */
  comparePhoto(data: { base641: string; base642: string }) {
    return request<any, any>({
      url: `/test/comparePhoto`,
      method: "post",
      data,
    });
  },

  /**
   * 根据业务请求ID获取影像任务明细
   * @param data.ywqqid 业务请求ID
   * @param data.rwlx 任务类型 3-助审任务
   * @returns 影像任务明细列表
   */
  getYxrwmxByYwqqid(data: GetYxrwmxParams) {
    return request<GetYxrwmxParams, GetYxrwmxResponse>({
      url: `/test/getYxrwmxByYwqqid`,
      method: "post",
      data,
    });
  },

  /**
   * 根据业务请求ID获取助审任务结果
   * @param data.ywqqid 业务请求ID
   * @param data.rwlx 任务类型 3-助审任务
   * @returns 助审任务结果
   */
  getZsrwjgByYwqqid(data: GetYxrwmxParams) {
    return request<GetYxrwmxParams, GetZsrwjgResponse>({
      url: `/test/getZsrwjgByYwqqid`,
      method: "post",
      data,
    });
  },

  // 查询业务请求要素
  queryYwqqyscs(data: { ywdm: string }) {
    return request<any, any>({
      url: `/test/cxywqqyscs`,
      method: "post",
      data,
    });
  },

  async cxCoworkInfo(data: {
    /**排队类型 */
    pdlx: string;
    /**uuid */
    uuid: string;
  }) {
    const res = await request<
      any,
      {
        khxxInfo: string;
        ywqqList: string;
      }
    >({
      url: `/test/cxCoworkInfo`,
      method: "post",
      data,
    });
    const khxxInfo = JSON.parse(res.khxxInfo);
    const ywqqList = JSON.parse(res.ywqqList);
    return {
      khxxInfo,
      ywqqList: ywqqList.map((item: any) => ({
        ...item,
        REQ_DATA: JSON.parse(item.REQ_DATA),
      })),
    } as {
      khxxInfo: {
        /** 证件起始日期名称 */
        ZJQSRQMC?: number;
        /** 营业部 */
        YYB?: number;
        /** 证件发证机关 */
        ZJFZJG?: string;
        /** 客户号 */
        KHH?: string;
        /** 性别 */
        XB?: string;
        /** 证件类别 */
        ZJLB?: number;
        /** 证件截止日期名称 */
        ZJJZRQMC?: number;
        /** 客户名称 */
        KHMC?: string;
        /** 客户类别 */
        KHLB?: number;
        /** 客户状态 */
        KHZT?: number;
        /** 证件类别名称 */
        ZJLBMC?: string;
        /** 营业部名称 */
        YYBMC?: string;
        /** 手机 */
        SJ?: string;
        /** 证件编号 */
        ZJBH?: string;
        /** 开户日期 */
        KHRQ?: number;
      };
      ywqqList: {
        /** 关联业务 */
        GLYW?: number;
        /** 营业部 */
        YYB?: number;
        /** 发起人 */
        FQR?: number;
        /** 客户号 */
        KHH?: string;
        /** 请求数据 */
        REQ_DATA?: {
          sqsj: string;
          khmc: string;
          yyb: string;
          fsyybdm: string;
          fsyyb: string;
          fqr: string;
          remark: string;
          clzt: string;
          khdlfs: string;
          zjbh: string;
          czzd: string;
          blms: string;
          zjlb: string;
          ywdm: string;
          image: {
            yxlx: string;
            filepath: string;
            yxlxmc: string;
          }[];
          ywqqid: string;
          hasYxsc: string;
          yfwxm: string;
          STR4YSH_ORG: string;
          sqrq: number;
          khh: string;
          xxgwzhlx: string;
          nzyshtags: string;
          fqqd: string;
          lgshgy: string;
          kzsyb: number;
          fwxm: string;
        };
        /** 证件类别 */
        ZJLB?: number;
        /** 股股权类别 */
        GGQLB?: number;
        /** 业务代码 */
        YWDM?: string;
        /** 办理等级 */
        BLDJ?: number;
        /** 办理模式 */
        BLMS?: number;
        /** 客户名称 */
        KHMC?: string;
        /** 申请时间 */
        SQSJ?: string;
        /** 分送营业部 */
        FSYYB?: number;
        /** 主办类别 */
        ZBLB?: number;
        /** ID */
        ID?: number;
        /** 申请日期 */
        SQRQ?: number;
        /** 处理状态 */
        CLZT?: number;
        /** 证件编号 */
        ZJBH?: string;
        /** 业务名称 */
        YWMC?: string;
        /** 操作终端 */
        CZZD?: string;
        /** 发起渠道 */
        FQQD?: number;
        /** 摘要 */
        ZY?: string;
      }[];
    };
  },
};

export default RemoteWitnessAPI;
