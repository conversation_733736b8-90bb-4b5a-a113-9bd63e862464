import AesUtil from "@/utils/aes/aesUtil";
import request from "@/utils/request";

class AuthAPI {
  /** 登录 接口*/
  static login(data: LoginData) {
    const formData = new FormData();

    let params;
    if (data.mode === "cifToken") {
      formData.append("userId", data.userId);
      formData.append("token", data.token);
    } else {
      params = {
        user: data.username,
        password: data.password,
        timestamp: new Date().getTime(),
      };
    }

    const signature = AesUtil.encrypt(JSON.stringify(params));
    formData.append("signature", signature);
    formData.append("ext", JSON.stringify({ czzd: "ceshi" }));
    formData.append("mode", data.mode);
    return request<any, LoginResult>({
      url: "/auth/login",
      method: "post",
      data: formData,
      headers: {
        "Content-Type": "multipart/form-data",
      },
    });
  }

  /** 注销 接口*/
  static logout() {
    return request({
      url: "auth/logout",
      method: "delete",
    });
  }
  /** 注销 接口*/
  static checkAuth() {
    return request({
      url: "/user/checkAuth",
      method: "post",
    });
  }

  /** 获取验证码 接口*/
  static getCaptcha() {
    return request<any, CaptchaResult>({
      url: "/api/v1/auth/captcha",
      method: "get",
    });
  }
}

export default AuthAPI;

/** 登录请求参数 */
export interface LoginData {
  /** 用户名 */
  username: string;
  /** 密码 */
  password: string;
  /** 验证码缓存key */
  captchaKey?: string;
  /** 验证码 */
  captchaCode?: string;
  mode?: string;
  cifToken?: string;
  userId?: string;
  token?: string;
}

/** 登录响应 */
export interface LoginResult {
  /** 访问token */
  accessToken?: string;
  /** 过期时间(单位：毫秒) */
  expires?: number;
  /** 刷新token */
  refreshToken?: string;
  /** token 类型 */
  tokenType?: string;
}

/** 验证码响应 */
export interface CaptchaResult {
  /** 验证码缓存key */
  captchaKey: string;
  /** 验证码图片Base64字符串 */
  captchaBase64: string;
}
