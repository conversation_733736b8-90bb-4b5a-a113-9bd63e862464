import request from "@/utils/request";

export interface Root {
  requestId: number;
  callingTime: string;
  callingAgentId: string;
  callResponse: number;
  canServeAgents: Record<string, boolean>;
  appointAgentId: any;
  witnessRequest: WitnessRequest;
}

export interface WitnessRequest {
  id: number;
  clientId: string;
  joinTime: string;
  priority: number;
  state: number;
  serveTime: any;
  serveAgentId: any;
  serveEndTime: any;
  result: any;
  reason: any;
  clientName: any;
  org: number;
  bizType: number;
  bizCode: string;
  cusLevel: number;
  reqData: any;
  bizRequestId: number;
  techCode: any;
  currentQueueInfo: any;
  markerData: any;
}

class WitnessQueryAPI {
  /**
   * 坐席调度管理-查询队列情况
   */
  static queryQueue(params: {
    clientId?: string;
    clientName?: string;
    org?: string;
  }) {
    return request<any, { records: Root[] }>({
      url: `/witness/query/queue`,
      method: "get",
      params,
    });
  }

  /**
   * 坐席任务查询
   */
  static queryFinishedWitness(params: {
    clientId?: string;
    clientName?: string;
    org?: string;
    agentId?: string;
    agentName?: string;
    serveTimeBegin?: string;
    serveTimeEnd?: string;
    pageNumber?: number;
    pageSize?: number;
  }) {
    return request<any, { records: Root[]; total: number }>({
      url: `/witness/query/finishedWitness`,
      method: "get",
      params: {
        clientId: params.clientId || "",
        clientName: params.clientName || "",
        org: params.org || "",
        agentId: params.agentId || "",
        agentName: params.agentName || "",
        serveTimeBegin: params.serveTimeBegin || "",
        serveTimeEnd: params.serveTimeEnd || "",
        pageNumber: params.pageNumber || 1,
        pageSize: params.pageSize || 10,
      },
    });
  }

  /**
   * 置顶见证请求
   */
  static topRequest(params: { requestId: number }) {
    return request<any, any>({
      url: `/witness/query/topRequest`,
      method: "get",
      params,
    });
  }
}
export default WitnessQueryAPI;
