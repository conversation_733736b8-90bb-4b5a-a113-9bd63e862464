import request from "@/utils/request";

class SeatApi {
  static queryAll(data: {}) {
    return request<
      any,
      {
        agentTechAll: {
          techCode: string;
          cusLevel: string;
          techName: string;
          id: number;
          bizLevel: string;
          techType: number;
        }[];
      }
    >({
      url: `/witness/agenttech/queryAll`,
      method: "get",
      data,
    }).then((data) => {
      return data;
    });
  }

  static add(data: {
    techCode: string;
    techName: string;
    techType: number;
    cusLevel: string;
    bizLevel: string;
  }) {
    return request<any, any>({
      url: `/witness/agenttech/add`,
      method: "post",
      data,
    }).then((data) => {
      return data;
    });
  }

  static update(data: {
    techCode: string;
    techName: string;
    techType: number;
    cusLevel: string;
    bizLevel: string;
  }) {
    return request<any, any>({
      url: `/witness/agenttech/update`,
      method: "post",
      data,
    }).then((data) => {
      return data;
    });
  }

  static remove(data: { techCode: string }) {
    return request<any, any>({
      url: `/witness/agenttech/remove`,
      method: "post",
      data,
    }).then((data) => {
      return data;
    });
  }
}

export default SeatApi;
