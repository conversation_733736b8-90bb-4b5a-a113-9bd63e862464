/* eslint-disable @typescript-eslint/no-extraneous-class */
import request from "@/utils/request";

export interface IWitnessScript {
  /** 业务类型 */
  bizType: number;
  /** 业务类型名称 */
  bizTypeName?: string;
  /** 业务代码 */
  bizCode?: string;
  /** 帧率，字典项 */
  fps?: number;
  /** 分辨率，字典项 */
  resolution?: number;
  /** 状态 */
  state?: number;
  /** 话术修改日期 */
  scriptModifyDate?: string;
  /** 话术修改时间 */
  scriptModifyTime?: string;
  /** 话术配置，json数组格式 */
  scriptConfig?: string;
}

export interface DictItem {
  /** 字典代码 */
  dicCode: string;
  /** 字典名称 */
  dicName: string;
  /** IBM值 */
  ibm: number;
  /** CBM值 */
  cbm: string;
  /** 备注 */
  note: string;
  /** 标志 */
  flag: number;
}

export interface TodayServiceListItem {
  /** 客户名称 */
  clientName: string;
  /** 服务时间 */
  serveTime: string;
  /** 客户身份证 */
  sfzh: string;
}

export interface ICoworkScript {
  /** 业务代码 */
  businessCode: string;
  /** 发起渠道 */
  initiateChannel: number;
  /** 渠道名称 */
  channelName?: string;
  /** 脚本内容 */
  script: string;
}
class ClientApi {
  /**
   * 发起见证请求
   * @param data
   * @returns
   */
  static addWitnessRequest(data: {
    clientId: string;
    org: string;
    bizRequestId: string;
  }) {
    return request<any, any>({
      url: `/witness/client/addWitnessRequest`,
      method: "get",
      params: {
        clientId: data.clientId,
        org: data.org,
        bizRequestId: data.bizRequestId,
      },
    }).then((data) => {
      return data;
    });
  }
  /**
   * 取消见证请求
   * @param requestId
   * @returns
   */
  static cancelWitnessRequest(requestId: string) {
    return request<any, any>({
      url: `/witness/client/cancelWitnessRequest`,
      method: "get",
      params: {
        requestId,
      },
    }).then((data) => {
      return data;
    });
  }

  /**
   * 获取见证请求状态
   * @param data
   * @returns
   */
  static getWitnessRequest(data: { requestId: string }) {
    return request<any, any>({
      url: `/witness/client/getWitnessRequest`,
      method: "get",
      params: {
        requestId: data.requestId,
      },
    }).then((data) => {
      return data;
    });
  }

  /**
   * 获取客户端ws腾讯连接要用的数据
   * @param data
   */
  static getClientSocketInfo(data: { clientId: string }) {
    return request<any, any>({
      url: `/witness/client/getClientSocketInfo`,
      method: "get",
      params: {
        clientId: data.clientId,
      },
    }).then((data) => {
      return data;
    });
  }

  /**
   * 修改见证锚点
   * @param data
   * @returns
   */
  static modifyWitness(data: { id: number; markerData?: string }) {
    return request<any, any>({
      url: `/witness/client/modifyWitness`,
      method: "post",
      data: {
        id: data.id,
        markerData: data.markerData,
      },
    }).then((data) => {
      return data;
    });
  }

  /**
   * 获取见证锚点信息
   * @param data
   * @returns
   */
  static getWitness(id: number) {
    return request<any, any>({
      url: `/witness/client/getWitness`,
      method: "post",
      data: {
        id,
      },
    }).then((data) => {
      return data;
    });
  }

  static downloadEsb =
    import.meta.env.VITE_APP_BASE_API +
    "/witness/client/downLoadOfEsb?filepath=";

  /**
   * 下载预览图片
   * @param filepath
   * @returns
   */
  static downLoadOfEsb({ filepath }: { filepath: string }) {
    return request<any, any>({
      url: `/witness/client/downLoadOfEsb?filepath=${filepath}`,
      method: "get",
    }).then((data) => {
      return data;
    });
  }

  /**
   * 上传图片
   */
  static uploadImage({ file }: { file: Blob }) {
    const formData = new FormData();
    formData.append("file", file);
    return request<any, any>({
      url: `/witness/client/image`,
      method: "post",
      data: formData,
      headers: {
        "Content-Type": "multipart/form-data",
      },
    });
  }
  /**
   * 获取服务的客户数量
   * @returns
   */
  static getWitnessCount({ serveAgentId }: { serveAgentId: string }) {
    return request<any, any>({
      url: `/witness/client/getWitnessCount`,
      method: "post",
      data: { serveAgentId },
    }).then((data) => {
      return data.witnessRequestCount;
    });
  }

  /**
   * 通过code查询字典
   * @returns
   */
  static getWitnessDict<T>({
    dicCode,
    dicCodes,
    tableName,
  }: {
    dicCode?: string;
    dicCodes?: string[];
    tableName?: string;
  }): Promise<T extends { dict: DictItem[] } ? DictItem[] : T> {
    return request<any, T>({
      url: `/witness/client/getWitnessDict`,
      method: "post",
      data: { dicCode, dicCodes, tableName },
    }).then((data) => {
      if (tableName) {
        return data as any;
      }
      // 使用类型守卫确保安全访问dict属性
      const hasDict = (obj: any): obj is { dict: DictItem[] } =>
        obj && typeof obj === "object" && "dict" in obj;

      return (hasDict(data) ? data.dict : []) as any;
    });
  }

  /**
   * 通过code查询分页字典
   * @returns
   */
  static getWitnessDictList({
    dicCode,
    pageNumber,
    pageSize,
  }: {
    dicCode: string;
    pageNumber: number;
    pageSize: number;
  }) {
    return request<any, any>({
      url: `/witness/client/getWitnessDictList`,
      method: "post",
      data: { dicCode, pageNumber, pageSize },
    }).then((data) => {
      return data;
    });
  }

  /**
   * 查询系统参数
   * /witness/client/listProperties"
   */
  static listProperties({ code, name }: { code?: string; name?: string }) {
    return request<any, any>({
      url: `/witness/client/listProperties`,
      method: "post",
      data: { code, name },
    }).then((data) => {
      return data;
    });
  }

  /**
   * 新增系统参数
   * /witness/client/addProperty
   */
  static addProperty({
    code,
    name,
    value,
    description,
  }: {
    code: string;
    name: string;
    value: string;
    description: string;
  }) {
    return request<any, any>({
      url: `/witness/client/addProperty`,
      method: "post",
      data: { code, name, value, description },
    }).then((data) => {
      return data;
    });
  }

  /**
   * 修改系统参数
   * /witness/client/updateProperty
   */
  static updateProperty({
    code,
    name,
    value,
    description,
  }: {
    code: string;
    name: string;
    value: string;
    description: string;
  }) {
    return request<any, any>({
      url: `/witness/client/updateProperty`,
      method: "post",
      data: { code, name, value, description },
    }).then((data) => {
      return data;
    });
  }

  /**
   * 删除系统参数
   * /witness/client/deleteProperty
   */
  static deleteProperty({ code }: { code: string[] }) {
    return request<any, any>({
      url: `/witness/client/deleteProperty`,
      method: "post",
      data: { code },
    }).then((data) => {
      return data;
    });
  }

  /**
   * 获取话术
   * @returns
   */
  static getScriptConfig({
    bizType,
    bizCode,
  }: {
    bizType?: string;
    bizCode?: string;
  }) {
    return request<any, any>({
      url: `/witness/client/getScriptConfig`,
      method: "post",
      data: { bizType, bizCode },
    });
  }

  /**
   * 分页获取话术配置
   */
  static getScriptConfigList({
    state,
    pageNumber,
    pageSize,
  }: {
    state: number | "";
    pageNumber: number;
    pageSize: number;
  }) {
    return request<any, PageResult<IWitnessScript>>({
      url: `/witness/client/getScriptConfigList`,
      method: "post",
      data: { pageNumber, pageSize, state },
    });
  }

  /**
   * 更新话术配置的函数
   * @param data 包含话术配置的对象
   * @returns 返回请求对象，包含请求的状态和数据
   */
  static updateScriptConfig(data: IWitnessScript) {
    return request<any, any>({
      url: `/witness/client/updateScriptConfig`,
      method: "post",
      data,
    });
  }

  /**
   * 删除话术配置的函数
   * @param bizType 业务类型，用于指定要删除的配置
   * @returns 返回请求对象，包含请求的状态和数据
   */
  static deleteScriptConfig(bizType: number) {
    return request<any, any>({
      url: `/witness/client/deleteScriptConfig`,
      method: "post",
      data: { bizType },
    });
  }

  /**
   * 添加话术配置的函数
   * @param data 包含话术配置的对象
   * @returns 返回请求对象，包含请求的状态和数据
   */
  static addScriptConfig(data: IWitnessScript) {
    return request<any, any>({
      url: `/witness/client/addScriptConfig`,
      method: "post",
      data,
    });
  }

  /**
   * 获取协作脚本配置列表（分页）
   * @param params
   * @returns
   */
  static getCoworkScriptList({
    businessCode,
    initiateChannel,
    pageNumber,
    pageSize,
  }: {
    businessCode?: string;
    initiateChannel?: number;
    pageNumber: number;
    pageSize: number;
  }) {
    return request<any, PageResult<ICoworkScript>>({
      url: `/witness/client/getCoworkScriptList`,
      method: "post",
      data: { businessCode, initiateChannel, pageNumber, pageSize },
    });
  }

  /**
   * 新增协作脚本配置
   * @param data
   * @returns
   */
  static addCoworkScript(data: ICoworkScript) {
    return request<any, any>({
      url: `/witness/client/addCoworkScript`,
      method: "post",
      data,
    });
  }

  /**
   * 更新协作脚本配置
   * @param data
   * @returns
   */
  static updateCoworkScript(data: ICoworkScript) {
    return request<any, any>({
      url: `/witness/client/updateCoworkScript`,
      method: "post",
      data,
    });
  }

  /**
   * 删除协作脚本配置
   * @param businessCode
   * @returns
   */
  static deleteCoworkScript(businessCode: string) {
    return request<any, any>({
      url: `/witness/client/deleteCoworkScript`,
      method: "post",
      data: { businessCode },
    });
  }

  /**
   * 获取柜员信息
   * @param param0
   * @returns
   */
  static queryUserById({ userId }: { userId: string }) {
    return request<any, { userInfo: Record<string, string>[] }>({
      url: `/witness/client/queryUserById`,
      method: "post",
      data: { userid: userId },
    });
  }

  static getTodayServiceList({ serveAgentId }: { serveAgentId: string }) {
    return request<any, { list: TodayServiceListItem[] }>({
      url: `/witness/client/queryTodayServiceList`,
      method: "post",
      data: { serveAgentId },
    }).then((data) => {
      return data?.list || [];
    });
  }
}

export default ClientApi;
