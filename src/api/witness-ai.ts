import request from "@/utils/request";

export interface QualityItem {
  /** 参数代码 */
  code: string;
  /** 参数名称 */
  name: string;
  /** 参数值 */
  value: string | null;
  /** 总分 */
  totalScore: number;
  /** 单次扣分 */
  onceDeductedScore: number;
  /** 两次扣分 */
  twiceDeductedScore: number | null;
  /** 通过分 */
  passScore: number;
}
export const WitnessAI = {
  queryAllQualityCheck() {
    return request<any, { records: QualityItem[] }>({
      url: `/witness/ai/queryAllQualityCheck`,
      method: "get",
    });
  },
  updateQualityCheck(data: { values: QualityItem[] }) {
    return request<any, { record: QualityItem[] }>({
      url: `/witness/ai/updateQualityCheck`,
      method: "post",
      data,
    });
  },
};
