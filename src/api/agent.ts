import request from "@/utils/request";

export interface AgentItem {
  agentId: string;
  org: number;
  agentName: string;
  techName: string;
  id: number;
  encryptedPassword: string;
  techs: string;
}

class AgentApi {
  /**
   * pageSize 传-1 是返回所有数据，这时数据类型就不是 PageResult，需要泛型传入
   */
  static query<T = PageResult<AgentItem>>(data: {}) {
    return request<any, T>({
      url: `/witness/agent/query`,
      method: "post",
      data,
    }).then((data) => {
      return data;
    });
  }

  static update(data: { userIds: string; techCodes: string }) {
    return request<any, any>({
      url: `/witness/agent/update`,
      method: "get",
      params: data,
    }).then((data) => {
      return data;
    });
  }
}

export default AgentApi;
