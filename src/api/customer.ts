import { ISearchMedia } from "@/api/user";
import request from "@/utils/request";

class CustomerAPI {
  static ttem(token: string) {
    return request<any, any>({
      url: `/customer/ttem`,
      method: "post",
      headers: { token },
      data: { token },
    }).then((data) => {
      return data;
    });
  }

  static startRecord(
    roomId: string,
    userId: string,
    subId: string,
    recordMode: string,
    token: string
  ) {
    return request<{ roomId: string }, any>({
      url: `/customer/start-record`,
      method: "post",
      headers: { token },
      data: { roomId, userId: userId, subId: subId, recordMode: recordMode },
    }).then((data) => {
      return data;
    });
  }

  static stopRecord(taskId: string, token: string) {
    return request<{ taskId: string }, any>({
      url: `/customer/stop-record`,
      method: "post",
      headers: { token },
      data: { taskId },
    }).then((data) => {
      return data;
    });
  }
  /**
   * 修改见证锚点
   * @param data
   * @returns
   */
  static modifyWitness(
    data: { id: number; markerData?: string },
    token: string
  ) {
    return request<any, any>({
      url: `/customer/modifyWitness`,
      method: "post",
      headers: { token },
      data: {
        id: data.id,
        markerData: data.markerData,
      },
    }).then((data) => {
      return data;
    });
  }
  static dissmissRoom(roomId: string, token: string) {
    return request<{ roomId: string }, any>({
      url: `/customer/dismiss-room`,
      method: "post",
      headers: { token },
      data: { roomId },
    }).then((data) => {
      return data;
    });
  }

  static searchMedia(roomId: string, token: string) {
    return request<{ roomId: string }, ISearchMedia>({
      url: `/customer/search-media`,
      method: "post",
      headers: { token },
      data: { roomId: roomId },
    }).then((data) => {
      return {
        coverUrl: data.coverUrl,
        mediaUrl: data.mediaUrl,
      };
    });
  }
}

export default CustomerAPI;
