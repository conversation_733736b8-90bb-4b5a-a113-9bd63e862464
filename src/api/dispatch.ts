import request from "@/utils/request";

export interface DispatchRuleItem {
  ruleCode: string;
  ruleName: string;
  priority: number;
  ruleDictType: number;
  ruleDictKey: string;
  ruleType: number;
  htmlElementType: number;
  state: number;
  customizeSortEnable: number;
  defaultRule: number;
  ruleValue: string;
}
const DispatchAPI = {
  queryAllRules() {
    return request<any, { allRules: DispatchRuleItem[] }>({
      url: `/witness/dispatch/queryAllRules`,
      method: "post",
    });
  },

  updateRules(values: {
    values: Array<{ ruleCode: string; ruleValue: string }>;
  }) {
    return request<any, any>({
      url: `/witness/dispatch/updateRules`,
      method: "post",
      data: values,
    });
  },
};

export default DispatchAPI;
