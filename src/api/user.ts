import request from "@/utils/request";

class UserAPI {
  /**
   * 获取当前登录用户信息
   *
   * @returns 登录用户昵称、头像信息，包括角色和权限
   */
  static getInfo() {
    return request<any, UserInfo>({
      url: "/user/me",
      method: "get",
    }).then((data) => {
      return {
        ...data,
        roles: ["ROOT"],
        avatar:
          data.photo ||
          "https://foruda.gitee.com/images/1723603502796844527/03cdca2a_716974.gif",
      };
    });
  }
  /**
   * 获取trtc
   *
   * @returns 登录用户昵称、头像信息，包括角色和权限
   */
  static getGenSign() {
    return request<any, string>({
      url: `/user/gen-trtc-sign`,
      method: "get",
    }).then((data) => {
      return data.userSign;
    });
  }
  /**
   * 获取trtc
   *
   * @returns 登录用户昵称、头像信息，包括角色和权限
   */
  static getCustomerGenSign(ids) {
    return request<any, string>({
      url: `/user/gen-trtc-sign/${ids}`,
      method: "get",
    }).then((data) => {
      console.log(data);
      return data.userSign;
    });
  }
  /**
   * 获取trtc
   *
   * @returns 登录用户昵称、头像信息，包括角色和权限
   */
  static getSocketToken() {
    return request<any, GetSocketToken>({
      url: `/user/socket-token`,
      method: "get",
    }).then((data) => {
      return data;
    });
  }
  /**
   * 获取trtc
   *
   * @returns 登录用户昵称、头像信息，包括角色和权限
   */
  static startRecord(roomId: string, connectId: string) {
    return request<{ roomId: string }, GetSocketToken>({
      url: `/user/start-record`,
      method: "post",
      data: { roomId, connectId: connectId },
    }).then((data) => {
      return data;
    });
  }
  static stopRecord(taskId: string) {
    return request<{ taskId: string }, GetSocketToken>({
      url: `/user/stop-record`,
      method: "post",
      data: { taskId },
    }).then((data) => {
      return data;
    });
  }
  static dissmissRoom(roomId: string) {
    return request<{ roomId: string }, GetSocketToken>({
      url: `/user/dismiss-room`,
      method: "post",
      data: { roomId },
    }).then((data) => {
      return data;
    });
  }
  static ttem() {
    return request<any, any>({
      url: `/user/ttem`,
      method: "post",
      data: {},
    }).then((data) => {
      return data;
    });
  }
  static searchMedia(roomId: string) {
    return request<{ roomId: string }, ISearchMedia>({
      url: `/user/search-media`,
      method: "post",
      data: { roomId: roomId },
    }).then((data) => {
      return {
        coverUrl: data.coverUrl,
        mediaUrl: data.mediaUrl,
      };
    });
  }
}

export default UserAPI;

/** 登录用户信息 */
export interface UserInfo {
  /** 用户ID */
  userid?: number;

  /** 用户名 */
  name?: string;

  /** 昵称 */
  nickname?: string;

  /** 头像URL */
  avatar?: string;
  photo?: string;

  /** 角色 */
  roles: string[];

  /** 权限 */
  perms: string[];
  zqcyzsid: string;
  fj: string;
  zyjg: string;
  zsbh: string;
  gyfl: string;
  certno: string;
  password: string;
  zygw: string;
  sj: string;
  id: number;
  email: string;
  chgpwdlimit: string;
  yhpz: string;
  kczyyb: string;
  gysx: string;
  chgpwdtime: string;
  xb: string;
  zsyxrq: string;
  bxxgmm: string;
  orgid: string;
  myyyb: string;
  zsqdrq: string;
  sfzh: string;
  xl: string;
  grade: string;
  cz: string;
  dlyzfs: string;
  zt: string;
  czqx_fjqx: string;
  zw: string;
  zx: string;
  status: string;
  loginIp: string;
  simulation: boolean;
  SDKAppID: number;
}
/** 登录用户信息 */
export interface UserSign {
  userSign?: string;
}
/** 获取getSocketToken */
export interface GetSocketToken {
  customerSign?: string;
  customerToken?: string;
  userToken?: string;
  customerId?: string;
  taskId: string;
}

export interface ISearchMedia {
  coverUrl?: string;
  mediaUrl?: string;
}
