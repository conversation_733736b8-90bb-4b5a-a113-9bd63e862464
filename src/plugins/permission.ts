import {
  NavigationGuardNext,
  RouteLocationNormalized,
  RouteRecordRaw,
} from "vue-router";

import AuthAPI from "@/api/auth";
import { TOKEN_KEY } from "@/enums/CacheEnum";
import router from "@/router";
import { usePermissionStore, useUserStore } from "@/store";
import NProgress from "@/utils/nprogress";

export function setupPermission() {
  // 白名单路由
  const whiteList = [
    "/login",
    "/pad-witness/pwait",
    "/pad-witness/connecting",
    "/pad-witness/client",
    "/pad-witness/aisl",
    "/video-preview",
    "/remote-witness/wait",
  ];

  // 处理单点登录
  const handleSsoLogin = async (to: RouteLocationNormalized) => {
    const userId = to.query.userId as string;
    const token = to.query.token as string;

    if (userId && token) {
      try {
        // 调用 AuthAPI.login 进行单点登录，使用 any 类型来绕过类型检查
        const loginData = await AuthAPI.login({
          userId,
          token,
          mode: "cifToken",
        } as any);

        // 保存登录信息，假设 loginData 有 accessToken 字段
        const tokenValue =
          typeof loginData === "string"
            ? loginData
            : loginData.accessToken || token;
        localStorage.setItem(TOKEN_KEY, tokenValue);

        // 获取用户信息
        const userStore = useUserStore();
        await userStore.getUserInfo();
        const permissionStore = usePermissionStore();
        const dynamicRoutes = await permissionStore.generateRoutes();
        dynamicRoutes.forEach((route: RouteRecordRaw) =>
          router.addRoute(route)
        );

        return true;
      } catch (error) {
        console.error("单点登录失败:", error);
        return false;
      }
    }
    return false;
  };

  router.beforeEach(async (to, from, next) => {
    NProgress.start();

    // 检查是否需要进行单点登录
    const userId = to.query.userId as string;
    const token = to.query.token as string;
    const hasToken = localStorage.getItem(TOKEN_KEY);

    // 如果URL中有 userId 和 token 参数，且当前未登录，则进行单点登录
    if (userId && token && !hasToken) {
      try {
        const ssoSuccess = await handleSsoLogin(to);
        if (ssoSuccess) {
          // 单点登录成功，清除URL参数并重定向
          const newQuery = { ...to.query };
          delete newQuery.userId;
          delete newQuery.token;

          next({
            path: to.path,
            query: newQuery,
            replace: true,
          });
          return;
        }
      } catch (error) {
        console.error("单点登录失败:", error);
      }
    }

    if (whiteList.includes(to.path)) {
      next(); // 在白名单，直接进入
      return;
    }

    if (hasToken) {
      if (to.path === "/login") {
        // 如果已登录，跳转到首页
        next({ path: "/" });
        NProgress.done();
      } else {
        const userStore = useUserStore();
        const hasRoles =
          userStore.user.roles && userStore.user.roles.length > 0;

        if (hasRoles) {
          // 如果未匹配到任何路由，跳转到404页面
          if (to.matched.length === 0) {
            next(from.name ? { name: from.name } : "/404");
          } else {
            // 如果路由参数中有 title，覆盖路由元信息中的 title
            const title =
              (to.params.title as string) || (to.query.title as string);
            if (title) {
              to.meta.title = title;
            }
            next();
          }
        } else {
          const permissionStore = usePermissionStore();
          try {
            await userStore.getUserInfo();
            const dynamicRoutes = await permissionStore.generateRoutes();
            dynamicRoutes.forEach((route: RouteRecordRaw) =>
              router.addRoute(route)
            );
            next({ ...to, replace: true });
          } catch (error) {
            // 移除 token 并重定向到登录页，携带当前页面路由作为跳转参数
            await userStore.resetToken();
            redirectToLogin(to, next);
            NProgress.done();
          }
        }
      }
    } else {
      // 未登录
      if (whiteList.includes(to.path)) {
        next(); // 在白名单，直接进入
      } else {
        // 不在白名单，重定向到登录页
        redirectToLogin(to, next);
        NProgress.done();
      }
    }
  });

  router.afterEach(() => {
    NProgress.done();
  });
}

/** 重定向到登录页 */
function redirectToLogin(
  to: RouteLocationNormalized,
  next: NavigationGuardNext
) {
  const params = new URLSearchParams(to.query as Record<string, string>);
  const queryString = params.toString();
  const redirect = queryString ? `${to.path}?${queryString}` : to.path;
  next(`/login?redirect=${encodeURIComponent(redirect)}`);
}

/** 判断是否有权限 */
export function hasAuth(
  value: string | string[],
  type: "button" | "role" = "button"
) {
  const { roles, perms } = useUserStore().user;

  // 超级管理员 拥有所有权限
  if (type === "button" && roles.includes("ROOT")) {
    return true;
  }

  const auths = type === "button" ? perms : roles;
  return typeof value === "string"
    ? auths.includes(value)
    : value.some((perm) => auths.includes(perm));
}
