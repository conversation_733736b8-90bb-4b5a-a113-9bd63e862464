export interface ClientOptions {
  sdkAppId: number;
  userId: string;
  roomId: number;
  sdkSecretKey?: string;
  userSig?: string;
}
export type DeviceItem = Record<string, any>;
export interface HSNRItem {
  nr: string;
  /**
   * 客户回答
   */
  khhd?: string;
  isPlaying?: boolean;
  /**
   * 是否已播放过
   */
  isPlayed?: boolean;
  asr_time?: number;
  start_time?: number;
  asr_content?: string;
  timeline?: Array<{
    text: string;
    beginTime: number;
    endTime: number;
    beginIndex: number;
    endIndex: number;
    phoneme: string;
  }>;
  currentTime?: number;
}
