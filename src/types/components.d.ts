/* eslint-disable */
// @ts-nocheck
// Generated by unplugin-vue-components
// Read more: https://github.com/vuejs/core/pull/3399
// biome-ignore lint: disable
export {}

/* prettier-ignore */
declare module 'vue' {
  export interface GlobalComponents {
    AgentApp: typeof import('./../views/remote-witness/components/AgentApp/AgentApp.vue')['default']
    AppLink: typeof import('./../components/AppLink/index.vue')['default']
    AppMain: typeof import('./../layout/components/AppMain/index.vue')['default']
    AuditControl: typeof import('./../views/remote-witness/components/AuditControl.vue')['default']
    Breadcrumb: typeof import('./../components/Breadcrumb/index.vue')['default']
    BusinessSummary: typeof import('./../views/remote-witness/components/BusinessSummary.vue')['default']
    Buttons: typeof import('./../components/remoteWitness/Buttons.vue')['default']
    Canvas: typeof import('./../components/Canvas/index.vue')['default']
    CommonDialog: typeof import('./../components/CommonDialog/index.vue')['default']
    CommonForm: typeof import('./../components/CommonForm/CommonForm.vue')['default']
    CommonQueryTable: typeof import('./../components/CommonQueryTable/index.vue')['default']
    ConnectConfirmDialog: typeof import('./../components/remoteWitness/ConnectConfirmDialog.vue')['default']
    CustomerInfo: typeof import('./../views/remote-witness/components/CustomerInfo.vue')['default']
    CustomerInfoConfirm: typeof import('./../views/remote-witness/components/CustomerInfoConfirm.vue')['default']
    CustomerInfoPanel: typeof import('./../views/remote-witness/components/CustomerInfoPanel.vue')['default']
    DetectionResult: typeof import('./../components/remoteWitness/DetectionResult.vue')['default']
    Device: typeof import('./../components/remoteWitness/Device.vue')['default']
    DeviceDetection: typeof import('./../components/remoteWitness/DeviceDetection.vue')['default']
    DialogItem: typeof import('./../components/remoteWitness/DialogItem.vue')['default']
    ElAlert: typeof import('element-plus/es')['ElAlert']
    ElBacktop: typeof import('element-plus/es')['ElBacktop']
    ElBreadcrumb: typeof import('element-plus/es')['ElBreadcrumb']
    ElBreadcrumbItem: typeof import('element-plus/es')['ElBreadcrumbItem']
    ElButton: typeof import('element-plus/es')['ElButton']
    ElCard: typeof import('element-plus/es')['ElCard']
    ElCascader: typeof import('element-plus/es')['ElCascader']
    ElCheckbox: typeof import('element-plus/es')['ElCheckbox']
    ElCheckboxGroup: typeof import('element-plus/es')['ElCheckboxGroup']
    ElCol: typeof import('element-plus/es')['ElCol']
    ElColorPicker: typeof import('element-plus/es')['ElColorPicker']
    ElConfigProvider: typeof import('element-plus/es')['ElConfigProvider']
    ElDatePicker: typeof import('element-plus/es')['ElDatePicker']
    ElDialog: typeof import('element-plus/es')['ElDialog']
    ElDivider: typeof import('element-plus/es')['ElDivider']
    ElDrawer: typeof import('element-plus/es')['ElDrawer']
    ElDropdown: typeof import('element-plus/es')['ElDropdown']
    ElDropdownItem: typeof import('element-plus/es')['ElDropdownItem']
    ElDropdownMenu: typeof import('element-plus/es')['ElDropdownMenu']
    ElEmpty: typeof import('element-plus/es')['ElEmpty']
    ElForm: typeof import('element-plus/es')['ElForm']
    ElFormItem: typeof import('element-plus/es')['ElFormItem']
    ElIcon: typeof import('element-plus/es')['ElIcon']
    ElImage: typeof import('element-plus/es')['ElImage']
    ElInput: typeof import('element-plus/es')['ElInput']
    ElInputNumber: typeof import('element-plus/es')['ElInputNumber']
    ElLink: typeof import('element-plus/es')['ElLink']
    ElMenu: typeof import('element-plus/es')['ElMenu']
    ElMenuItem: typeof import('element-plus/es')['ElMenuItem']
    ElOption: typeof import('element-plus/es')['ElOption']
    ElPagination: typeof import('element-plus/es')['ElPagination']
    ElPopover: typeof import('element-plus/es')['ElPopover']
    ElRadio: typeof import('element-plus/es')['ElRadio']
    ElRadioGroup: typeof import('element-plus/es')['ElRadioGroup']
    ElRate: typeof import('element-plus/es')['ElRate']
    ElRow: typeof import('element-plus/es')['ElRow']
    ElScrollbar: typeof import('element-plus/es')['ElScrollbar']
    ElSelect: typeof import('element-plus/es')['ElSelect']
    ElSelectV2: typeof import('element-plus/es')['ElSelectV2']
    ElSkeleton: typeof import('element-plus/es')['ElSkeleton']
    ElSlider: typeof import('element-plus/es')['ElSlider']
    ElSubMenu: typeof import('element-plus/es')['ElSubMenu']
    ElSwitch: typeof import('element-plus/es')['ElSwitch']
    ElTable: typeof import('element-plus/es')['ElTable']
    ElTableColumn: typeof import('element-plus/es')['ElTableColumn']
    ElTabPane: typeof import('element-plus/es')['ElTabPane']
    ElTabs: typeof import('element-plus/es')['ElTabs']
    ElTimePicker: typeof import('element-plus/es')['ElTimePicker']
    ElTooltip: typeof import('element-plus/es')['ElTooltip']
    ElTreeSelect: typeof import('element-plus/es')['ElTreeSelect']
    ElUpload: typeof import('element-plus/es')['ElUpload']
    ElWatermark: typeof import('element-plus/es')['ElWatermark']
    Free: typeof import('./../views/remote-witness/components/Free.vue')['default']
    Hamburger: typeof import('./../components/Hamburger/index.vue')['default']
    Header: typeof import('./../layout/components/Header/index.vue')['default']
    HeaderLogo: typeof import('./../layout/components/Header/components/HeaderLogo/index.vue')['default']
    HeaderRight: typeof import('./../layout/components/Header/components/HeaderRight/index.vue')['default']
    IEpClose: typeof import('~icons/ep/close')['default']
    ImageDisplay: typeof import('./../views/remote-witness/components/ImageDisplay.vue')['default']
    Inputs: typeof import('./../components/remoteWitness/Inputs.vue')['default']
    LangSelect: typeof import('./../components/LangSelect/index.vue')['default']
    LayoutSelect: typeof import('./../layout/components/Settings/components/LayoutSelect.vue')['default']
    MarqueeText: typeof import('./../components/MarqueeText.vue')['default']
    NavBar: typeof import('./../layout/components/NavBar/index.vue')['default']
    NavbarAction: typeof import('./../layout/components/NavBar/components/NavbarAction.vue')['default']
    Player: typeof import('./../components/remoteWitness/Player.vue')['default']
    RealtimeDetection: typeof import('./../components/remoteWitness/RealtimeDetection.vue')['default']
    RemoteWitnessContainer: typeof import('./../components/remoteWitness/remoteWitnessContainer.vue')['default']
    RemoteWitnessHeader: typeof import('./../layout/components/remoteWitnessHeader/index.vue')['default']
    RevealInfoDialog: typeof import('./../views/witness-config/reveal-info/components/RevealInfoDialog.vue')['default']
    RouterLink: typeof import('vue-router')['RouterLink']
    RouterView: typeof import('vue-router')['RouterView']
    ScriptConfigDialog: typeof import('./../views/witness-config/witness-script/components/ScriptConfigDialog.vue')['default']
    ScriptVar: typeof import('./../views/witness-config/witness-script/components/ScriptVar.vue')['default']
    ServiceCustomerDrawer: typeof import('./../layout/components/remoteWitnessHeader/components/ServiceCustomerDrawer.vue')['default']
    Settings: typeof import('./../layout/components/Settings/index.vue')['default']
    Sidebar: typeof import('./../layout/components/Sidebar/index.vue')['default']
    SidebarLogo: typeof import('./../layout/components/Sidebar/components/SidebarLogo.vue')['default']
    SidebarMenu: typeof import('./../layout/components/Sidebar/components/SidebarMenu.vue')['default']
    SidebarMenuItem: typeof import('./../layout/components/Sidebar/components/SidebarMenuItem.vue')['default']
    SidebarMenuItemTitle: typeof import('./../layout/components/Sidebar/components/SidebarMenuItemTitle.vue')['default']
    SidebarMixTopMenu: typeof import('./../layout/components/Sidebar/components/SidebarMixTopMenu.vue')['default']
    SiderMenu: typeof import('./../layout/components/SiderMenu/index.vue')['default']
    SimpleDetectionResult: typeof import('./../views/remote-witness/components/SimpleDetectionResult.vue')['default']
    SizeSelect: typeof import('./../components/SizeSelect/index.vue')['default']
    SpecialSpeechDialog: typeof import('./../components/remoteWitness/SpecialSpeechDialog.vue')['default']
    StatusDisplay: typeof import('./../components/remoteWitness/StatusDisplay.vue')['default']
    SvgIcon: typeof import('./../components/SvgIcon/index.vue')['default']
    TagsView: typeof import('./../layout/components/TagsView/index.vue')['default']
    TagWithIndicator: typeof import('./../components/TagWithIndicator/index.vue')['default']
    ThemeColorPicker: typeof import('./../layout/components/Settings/components/ThemeColorPicker.vue')['default']
    TTSPlayer: typeof import('./../components/remoteWitness/TTSPlayer.vue')['default']
    TtsSubtitle: typeof import('./../components/remoteWitness/TtsSubtitle.vue')['default']
    UserCamera: typeof import('./../components/remoteWitness/user-camera.vue')['default']
    VideoPreview: typeof import('./../components/remoteWitness/VideoPreview.vue')['default']
    VideoPreviewDialog: typeof import('./../components/remoteWitness/VideoPreviewDialog.vue')['default']
    VideoUploadDrawer: typeof import('./../layout/components/remoteWitnessHeader/components/VideoUploadDrawer.vue')['default']
    VoiceAssistantIcon: typeof import('./../components/remoteWitness/VoiceAssistantIcon.vue')['default']
    WebrtcCheck: typeof import('./../components/remoteWitness/Webrtc-check.vue')['default']
    WitnessConfigDialog: typeof import('./../views/witness-config/witness-script/components/WitnessConfigDialog.vue')['default']
  }
  export interface ComponentCustomProperties {
    vLoading: typeof import('element-plus/es')['ElLoadingDirective']
  }
}
