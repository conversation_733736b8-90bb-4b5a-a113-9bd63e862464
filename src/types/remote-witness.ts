/**
 * 影像任务明细数组项
 */
export interface YxrwmxItem {
  /** 影像类型 */
  YXLX: string;
  /** 协议类型代码 */
  XYLXDM: string;
  /** 处理结果说明 */
  CLJGSM: string;
  /** 任务处理开始时间 */
  RWCLKSSJ: string;
  /** 任务处理时长 */
  RWCLSC: string;
  /** 任务生成时间 */
  RWSCSJ: string;
  /** 业务请求ID */
  YWQQID: string;
  /** 任务类型 */
  RWLX: string;
  /** 文件路径 */
  FILEPATH: string;
  /** 影像类型名称 */
  YXLXMC: string;
  /** ID */
  ID: string;
  /** 任务处理完成时间 */
  RWCLWCSJ: string;
  /** 处理状态 */
  CLZT: string;
  /** 模板分类 */
  MBFL: string;
  /** 明细数组 */
  mxArray: YxrwmxDetailItem[];
}

/**
 * 影像任务明细子项
 */
export interface YxrwmxDetailItem {
  /** 比对结果 0-不一致 1-一致 */
  BDJG: string;
  /** 文件路径 */
  FilePath: string;
  /** 比对数据源 1-OCR 2-人工 */
  BDSJY: string;
  /** 认定值 */
  RDZ: string;
  /** 原始代码 */
  YSDM: string;
  /** 页码 */
  PAGE: string;
  /** 详细信息JSON字符串 */
  DETAIL: string;
  /** 业务原始值 */
  YWYSZ: string;
  /** 认定名称 */
  RDMC: string;
  /** 主任务ID */
  ZSRWID: string;
  /** 原始名称 */
  YSMC: string;
  /** ID */
  ID: string;
  /** 认定标识 */
  RDBM: string;
}

/**
 * 影像任务明细接口参数
 */
export interface GetYxrwmxParams {
  /** 业务请求ID */
  ywqqid: string;
  /** 任务类型 */
  rwlx: number;
}

/**
 * 影像任务明细接口返回
 */
export interface GetYxrwmxResponse {
  /** 记录列表 */
  records: YxrwmxItem[];
}

/**
 * 助审任务结果接口返回
 */
export interface GetZsrwjgResponse {
  /** 记录列表 */
  records: ZsrwjgItem[];
}

/**
 * 助审任务结果接口返回
 */
export interface ZsrwjgItem {
  /**
   * 助审任务ID
   */
  zsrwid: string;
  /**
   * 分类
   */
  fl: string;
  /**
   * 分类名称
   */
  flmc: string;
  /**
   * 类型
   */
  type: string;
  /**
   * 结果
   */
  jg: string;
  /**
   * 助审任务明细
   */
  mxArray: {
    /**
     * 结果内容 (JGNR)
     */
    JGNR: { code: number; note: string; sqbh: string; value: any };
    /**
     * 助审任务ID (ZSRWID)
     */
    ZSRWID: string;
    /**
     * 审核部门 (SHBM)
     */
    SHBM: string;
    /**
     * 助审任务类型 (ZSRWLX)
     */
    ZSRWLX: string;
    /**
     * 受理业务类别 (SLYWLB)
     */
    SLYWLB: string;
    /**
     * ID
     */
    ID: string;
    /**
     * 结果值 (JGZ)
     */
    JGZ: string;
    /**
     * 类型 (TYPE)
     */
    TYPE: string;
    /**
     * 审核分类 (SHFL)
     */
    SHFL: string;
    /**
     * 规则代码 (GZDM)
     */
    GZDM: string;
    /**
     * 审核部门名称 (SHBMMC)
     */
    SHBMMC: string;
    /**
     * 审核部门名称临时 (SHBMMC_TEMP)
     */
    SHBMMC_TEMP: string;
    /**
     * 影像类型名称
     */
    YXLXMC: string;
  }[];
}

/**
 * 业务请求要素
 */
export interface YwqqyscsItem {
  /** 显示控件 0|不显示;1|显示 - 当为'1'且qzfs为'1'或'2'时使用radio控件 */
  xskj: string;
  /** 取值方式 0|普通;1|数据字典;2|个性字典;3|虚拟值;4|SQL值 - '1'或'2'且xskj为'1'时使用radio控件渲染 */
  qzfs: string;
  /** 显示 */
  display: string;
  /** 要素名称 */
  ysmc: string;
  /** 排序 */
  px: string;
  /** 启用 */
  qy: string;
  /** 父子要素代码 */
  fzysdm: string;
  /** 取值参数 - radio控件时用于定义选项，支持内联格式如"1|男;2|女"或字典代码 */
  qzcs: string;
  /** 子要素 */
  childys: string;
  /** 隐藏公式 */
  ycgs: string;
  /** 场景 */
  scene: string;
  /** 列跨度 */
  colspan: string;
  /** 检查 */
  fcheck: string;
  /** 字段长度 */
  zdcd: string;
  /** 数据类型 1:字符 2:数值 3:数组 4:分组 5:JSON数组 */
  sjlx: string;
  /** 默认初始值 */
  mncsz: string;
  /** ID */
  id: string;
  /** 要素代码 */
  ysdm: string;
  /** 显示 */
  xs: string;
  /** 备注说明 */
  bzsm: string;
  /** 办理模式 */
  blms: string;
  /** 要素类型 */
  yslx: string;
  /** 业务代码 */
  ywdm: string;
  /** 父要素代码 */
  fysdm: string;
}

/**
 * 客户业务摘要项
 */
export interface ClientUserBusinessSummaryItem {
  /** 申请时间 */
  sqsj: string;
  /** 客户名称 */
  khmc: string;
  /** 发起人代码 */
  fqrdm: string;
  /** 营业部 */
  yyb: string;
  /** 发送营业部 */
  fsyyb: string;
  /** 发起人 */
  fqr: string;
  /** 业务名称 */
  ywmc: string;
  /** 处理状态 */
  clzt: string;
  /** 证件编号 */
  zjbh: string;
  /** 处理结果说明 */
  cljgsm: string;
  /** 在线修改 */
  zxxg: string;
  /** 结转时间 */
  jzsj: string;
  /** 是否受理 */
  sfsl: string;
  /** 办理模式 */
  blms: string;
  /** 业务代码 */
  ywdm: string;
  /** 证件类别 */
  zjlb: string;
  /** 客户类别 */
  khlb: string;
  /** 处理状态名称 */
  clztmc: string;
  /** 结转人 */
  jzr: string;
  /** 业务请求ID */
  ywqqid: string;
  /** 申请日期 */
  sqrq: string;
  /** 办理人 */
  blr: string;
  /** 客户号 */
  khh: string;
  /** 产品标识 */
  cpbz: string;
  /** 发起渠道 */
  fqqd: string;
  /** cdata */
  cdata: string;
  /** 结转方式 */
  jzfs: string;
  /** 关联业务 */
  glyw: string;
  /** 营业部名称 */
  yybmc: string;
  /** 结转日期 */
  jzrq: string;
  /** 办理等级 */
  bldj: string;
}

export interface ReturnReasonsItem {
  fqcs: string; // 发起次数
  scthyy: string; // 上次退回原因
  ywdm: string; // 业务代码
}

export interface RecordFileInfoItem {
  /** 时长 */
  elapse: number;
  /** 文件路径 */
  filePath: string;
  /** 文件大小 */
  filelength: number;
  /** 文件MD5 */
  filemd5: string;
  /** 用户ID */
  userid: string;
}
