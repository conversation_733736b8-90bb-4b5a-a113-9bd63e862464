// export interface FaceDetectionResult {
//   faceCount: number;
//   hasOcclusion: boolean;
//   isHeadTilted: boolean;
//   details: string;
//   timestamp: number;
// }

export interface FaceQualityCompleteness {
  eyebrow: number;
  eye: number;
  nose: number;
  cheek: number;
  mouth: number;
  chin: number;
}

export interface FaceQualityInfo {
  score: number;
  brightness: number;
  sharpness: number;
  completeness: FaceQualityCompleteness;
}

export interface FaceAttributesInfo {
  mask: boolean;
  eyeOpen: boolean;
  age: number;
  pitch: number;
  yaw: number;
  roll: number;
  glass: boolean;
  beauty: number;
  hat: boolean;
  expression: number;
  gender: number;
}

export interface FaceInfo {
  width: number;
  height: number;
  x: number;
  y: number;
  faceAttributesInfo: FaceAttributesInfo;
  faceQualityInfo: FaceQualityInfo;
}

export interface FaceDetectionResult {
  faces: FaceInfo[];
  faceCount: number;
  compareScore: number;
  elapsedSeconds: number;
  detail: string;
  timestamp: string;
}
