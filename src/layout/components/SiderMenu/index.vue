<script setup lang="ts">
import { usePermissionStore } from "@/store/modules/permission";
import { onMounted, ref, watch } from "vue";
import type { RouteRecordRaw } from "vue-router";
import { useRoute, useRouter } from "vue-router";

defineOptions({
  name: "SiderMenu",
});

const route = useRoute();
const router = useRouter();
const permissionStore = usePermissionStore();

// 获取路由列表
const routes = ref<RouteRecordRaw[]>([]);

// 当前选中的一级菜单
const activeRoute = ref<RouteRecordRaw | null>(null);

// 处理一级菜单点击
const handlePrimaryMenuClick = (route: RouteRecordRaw) => {
  if (route.path === "/sso") {
    ElMessage({
      message: `功能暂不开放`,
      type: "warning",
    });
    return;
  }
  if (route.meta?.target === "_blank") {
    window.open(route.path);
    return;
  }

  if (activeRoute.value === route) {
    activeRoute.value = null;
    return;
  } else {
    activeRoute.value = route;
    if (!route.children) {
      router.push(route.path);
    }
  }
};

// const handleClickSubMenu = () => {
//   console.log("activeRoute", activeRoute.value);

//   // ElMessage({
//   //   message: `功能暂不开放`,
//   //   type: "warning",
//   // });
// };

// 初始化选中状态
const initActiveRoute = () => {
  const currentPath = route.path;
  const currentRoute = routes.value.find((route) =>
    currentPath.startsWith(route.path)
  );
  if (currentRoute) {
    activeRoute.value = currentRoute;
  }
};

// 监听路由变化
watch(
  () => route.path,
  () => {
    initActiveRoute();
  }
);

onMounted(async () => {
  const dynamicRoutes = await permissionStore.generateRoutes();
  routes.value = dynamicRoutes.filter((route) => !route.meta?.hidden);
  // 初始化
  initActiveRoute();
});
</script>

<template>
  <div class="flex">
    <!-- 左侧一级菜单 -->
    <div class="w-60 flex flex-col gap-26 py-20 shadow-lg bg-#FDFEFF">
      <div
        v-for="route in routes"
        :key="route.path"
        :class="{ 'text-primary': activeRoute?.path === route.path }"
        @click="handlePrimaryMenuClick(route)"
      >
        <div class="flex flex-col justify-center items-center cursor-pointer">
          <div
            class="w-32 h-32 rounded-6 flex-center"
            :class="activeRoute?.path === route.path ? 'bg-primary' : ''"
          >
            <svg-icon
              v-if="route.meta?.icon"
              :icon-class="route.meta.icon"
              size="20"
              :color="activeRoute?.path === route.path ? '#fff' : '#454A55'"
            />
          </div>
          <span class="w-28 whitespace-break-spaces text-sm mt-5">
            {{ route.meta?.title }}
          </span>
        </div>
      </div>
    </div>

    <!-- 右侧二级菜单 -->
    <div
      v-if="activeRoute && activeRoute.children"
      class="w-240 flex flex-col shadow-lg bg-#FDFEFF box-border px-10 py-20"
    >
      <div class="flex flex-col justify-center gap-10">
        <app-link
          v-for="child in activeRoute.children"
          :key="child.path"
          :to="{ path: `${activeRoute?.path}/${child.path}`, params: {} }"
        >
          <div
            class="px-13 py-10 rounded-4 transition-colors duration-300 cursor-pointer"
            :class="{
              'bg-#F0F6FF text-primary':
                route.path === activeRoute.path + '/' + child.path,
            }"
          >
            <!-- @click="handleClickSubMenu" -->
            <!-- <app-link :to="{ path: activeRoute.path + '/' + child.path }"> -->

            <svg-icon
              v-if="child.meta?.icon"
              :icon-class="child.meta.icon"
              size="16"
            />
            <span
              class="text-sm text-#454A55 ml-10"
              :class="{
                'text-primary':
                  route.path === activeRoute.path + '/' + child.path,
              }"
            >
              {{ child.meta?.title }}
            </span>
          </div>
        </app-link>
      </div>
    </div>
  </div>
</template>
