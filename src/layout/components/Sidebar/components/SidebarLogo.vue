<script lang="ts" setup>
import logo from "@/assets/images/home-logo.png";
import defaultSettings from "@/settings";

defineProps({
  collapse: {
    type: Boolean,
    required: true,
  },
});
</script>

<template>
  <div class="logo-container">
    <transition enter-active-class="animate__animated animate__fadeInLeft">
      <router-link :key="+collapse" class="wh-full flex-center" to="/">
        <img :src="logo" class="logo-image" />
        <span v-if="!collapse" class="logo-title">
          <!--          {{ defaultSettings.title }}-->
          统一见证服务系统
        </span>
      </router-link>
    </transition>
  </div>
</template>

<style lang="scss" scoped>
.logo-container {
  width: 100%;
  height: $navbar-height;
  background-color: $sidebar-logo-background;

  .logo-image {
    width: 80px;
    height: 30px;
  }

  .logo-title {
    flex-shrink: 0; /* 防止容器在空间不足时缩小 */
    margin-left: 10px;
    font-size: 14px;
    font-weight: bold;
    color: #000;
  }
}

.layout-top,
.layout-mix {
  .logo-container {
    width: $sidebar-width;
  }

  &.hideSidebar {
    .logo-container {
      width: $sidebar-width-collapsed;
    }
  }
}
</style>
