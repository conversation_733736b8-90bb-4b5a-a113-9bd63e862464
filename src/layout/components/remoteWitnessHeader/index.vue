<script setup lang="ts">
import ClientApi from "@/api/client";
import { Status } from "@/config/appConfig";
import { usePermissionStore, usePwaitStore, useUserStore } from "@/store";
import useRemoteWitnessStore from "@/store/modules/remoteWitness";
import { getAllFailedUploads, getAllPendingUploads } from "@/utils/indexedDB";
import { ApexQueueCowork } from "agent-queue-ts-websdk";
import { ElMessage } from "element-plus";
import { onMounted, onUnmounted, ref, watch } from "vue";
import { RouteRecordRaw } from "vue-router";
import ServiceCustomerDrawer from "./components/ServiceCustomerDrawer.vue";
import VideoUploadDrawer from "./components/VideoUploadDrawer.vue";

const pwaitStore = usePwaitStore();
const remoteWitnessStore = useRemoteWitnessStore();
const router = useRouter();
const route = useRoute();

const showServiceCustomerDrawer = ref(false);
const showVideoUploadDrawer = ref(false);
const failedVideoUploads = ref<any[]>([]);
const pendingVideoUploads = ref<any[]>([]);

const handleOpenServiceCustomerDrawer = () => {
  showServiceCustomerDrawer.value = true;
};

const handleOpenVideoUploadDrawer = () => {
  showVideoUploadDrawer.value = true;
};

// 加载失败的视频上传记录
const loadFailedVideoUploads = async () => {
  try {
    const failedUploads = await getAllFailedUploads();
    failedVideoUploads.value = failedUploads;
  } catch (error) {
    console.error("加载失败的上传记录失败:", error);
    failedVideoUploads.value = [];
  }
};

// 加载待上传的视频记录
const loadPendingVideoUploads = async () => {
  try {
    const pendingUploads = await getAllPendingUploads();
    pendingVideoUploads.value = pendingUploads.filter(
      (upload) =>
        upload.uploadStatus === "pending" || upload.uploadStatus === "failed"
    );
  } catch (error) {
    console.error("加载待上传记录失败:", error);
    pendingVideoUploads.value = [];
  }
};

// 定义一个响应式变量来控制取消按钮的显示
const shouldShowInfo = ref(false);

// 监听路由变化
watch(
  () => route.path,
  (newPath) => {
    // 当路由为 /remote-witness/wait 时，不显示取消按钮
    if (newPath === "/remote-witness/wait") {
      shouldShowInfo.value = false;
    } else {
      shouldShowInfo.value = true;
    }
  },
  { immediate: true } // 立即执行一次回调函数，确保初始路由状态也能正确处理
);

const onDomainClick = async (type: string) => {
  if (remoteWitnessStore.callStatus !== Status.Idle) {
    ElMessage({
      message: `通话中不允许签出`,
      type: "warning",
    });
    return;
  }
  if (type === "pic_qc") {
    await pwaitStore.disconnect();
    await router.push("/remote-witness/wait");
  }
};

const userStore = useUserStore();
const witnessRequestCount = ref();
const currentStatus = ref("空闲");
const currentStatusIcon = computed(() => {
  if (currentStatus.value === "空闲") {
    return "pic_kx";
  } else {
    return "busy";
  }
});

// 处理状态变更
const handleStatusChange = (command: string) => {
  currentStatus.value = command;
  console.log("状态已更改为:", command);
  if (command === "空闲") {
    // 坐席登录成功后，切换坐席状态的方法----切换到“示闲”状态
    ApexQueueCowork.agentStatus();
  } else {
    // 坐席登录成功后，切换坐席状态的方法----切换到“繁忙”状态
    ApexQueueCowork.agentStatusDnd();
  }
};

const goHome = async () => {
  //FIXME:待会后续对接正的菜单的时候再验证一下
  //回到首页需要加载一下所有菜单，不然首页菜单菜单跳转会找不到路由
  if (remoteWitnessStore.callStatus !== Status.Idle) {
    ElMessage({
      message: `通话中不允许签出`,
      type: "warning",
    });
    return;
  }
  const permissionStore = usePermissionStore();
  const dynamicRoutes = await permissionStore.generateRoutes();
  dynamicRoutes.forEach((route: RouteRecordRaw) => router.addRoute(route));
  await router.push("/");
};

// 处理页面关闭/刷新事件
const handleBeforeUnload = (event: BeforeUnloadEvent) => {
  // 检查是否在通话中
  if (remoteWitnessStore.callStatus !== Status.Idle) {
    // 设置提示信息
    const message = "通话中不允许关闭页面，确定要离开吗？";
    event.preventDefault();
    event.returnValue = message;
    return message;
  }
};

onMounted(async () => {
  witnessRequestCount.value = await ClientApi.getWitnessCount({
    serveAgentId: String(userStore.user.userid),
  });
  console.log(
    "🚀 ~ onMounted ~ witnessRequestCount.value:",
    witnessRequestCount.value
  );

  // 添加beforeunload事件监听
  window.addEventListener("beforeunload", handleBeforeUnload);

  // 初始化加载失败上传记录和待上传记录
  await loadFailedVideoUploads();
  await loadPendingVideoUploads();

  // 定期刷新失败上传计数和待上传计数
  setInterval(async () => {
    if (remoteWitnessStore.callStatus === Status.Idle) {
      await loadFailedVideoUploads();
      await loadPendingVideoUploads();
    }
  }, 30000); // 每30秒刷新一次
});

// 监听通话状态变化，当变为空闲状态时关闭视频上传抽屉
watch(
  () => remoteWitnessStore.callStatus,
  (newStatus) => {
    if (newStatus !== Status.Idle && showVideoUploadDrawer.value) {
      showVideoUploadDrawer.value = false;
    }
  }
);

// 组件卸载时移除事件监听
onUnmounted(() => {
  window.removeEventListener("beforeunload", handleBeforeUnload);
});
</script>
<template>
  <div class="h-75 w-full flex-y-center bg-white flex-x-between">
    <div class="flex cursor-pointer block" @click="goHome">
      <div class="mr-1">
        <img
          class="w-40 h-40 ml-13"
          src="@/assets/pic_%20logo.png"
          alt="Logo"
        />
      </div>
      <div>
        <div
          class="w-72 h-25 fw-600 text-18 color-hex-333333 font-leading-25 text-left"
        >
          远程见证
        </div>
        <div
          class="w-106 h-17 fw-400 text-12 color-hex-333333 font-leading-17 text-left"
        >
          Remote witnessing
        </div>
      </div>
    </div>
    <div v-show="shouldShowInfo" class="flex-y-center">
      <div class="fw-500 text-18 color-hex-666666 font-leading-25 text-center">
        在线等待客户数
      </div>
      <span class="fw-500 text-30 color-hex-3686FD font-leading-42 m-inline-10">
        {{ pwaitStore.waiterNum }}
      </span>
      <div class="fw-500 text-18 color-hex-666666 font-leading-25 text-center">
        位
      </div>
    </div>

    <div v-show="shouldShowInfo" class="flex-y-center mr-25">
      <!-- 视频上传 -->
      <div
        v-if="remoteWitnessStore.callStatus === Status.Idle"
        class="flex-y-center h-40 rounded-8 pl-16 pr-2 relative w-140 mr-8 cursor-pointer"
        @click="handleOpenVideoUploadDrawer"
      >
        <svg-icon icon-class="cloud-upload" size="26" />
        <div class="pl-6 flex items-center">
          <span class="flex-shrink-0">视频上传</span>
          <span
            v-if="
              failedVideoUploads.length > 0 || pendingVideoUploads.length > 0
            "
            class="color-hex-FF4D4F"
          >
            ({{ failedVideoUploads.length + pendingVideoUploads.length }})
          </span>
        </div>
        <!-- 竖线 -->
        <div class="absolute right--10 top-10 bottom-10 w-px bg-gray-400"></div>
      </div>

      <!-- 服务客户 -->
      <div
        class="flex-y-center h-40 rounded-8 pl-16 pr-2 relative w-140 mr-8 cursor-pointer"
        @click="handleOpenServiceCustomerDrawer"
      >
        <svg-icon icon-class="pic_kf" size="26" />
        <div class="pl-6 flex items-center">
          <span class="flex-shrink-0">服务客户</span>
          <span class="color-hex-3686FD">{{ witnessRequestCount || 0 }}</span>
        </div>
        <!-- 竖线 -->
        <div class="absolute right--10 top-10 bottom-10 w-px bg-gray-400"></div>
      </div>

      <!-- 空闲 -->
      <div class="flex-y-center h-40 rounded-8 pl-16 pr-2 relative mr-8">
        <el-dropdown @command="handleStatusChange">
          <div class="flex-y-center cursor-pointer">
            <svg-icon :icon-class="currentStatusIcon" size="26" />
            <div class="px-6">{{ currentStatus }}</div>
            <el-icon><ArrowDownBold /></el-icon>
          </div>
          <template #dropdown>
            <el-dropdown-menu>
              <el-dropdown-item command="空闲">
                <svg-icon icon-class="pic_kx" size="18" />
                <div class="ml-4">空闲</div>
              </el-dropdown-item>
              <el-dropdown-item command="繁忙">
                <svg-icon icon-class="busy" size="18" />
                <div class="ml-4">繁忙</div>
              </el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
        <!-- 竖线 -->
        <div class="absolute right--10 top-10 bottom-10 w-px bg-gray-400"></div>
      </div>

      <!-- 签出 -->
      <div class="flex-y-center h-40 rounded-8 pl-16 pr-2 relative">
        <svg-icon icon-class="pic_qc" size="26" />
        <div class="pl-6 cursor-pointer" @click="onDomainClick('pic_qc')">
          签出
        </div>
      </div>
    </div>

    <!-- 视频上传抽屉 -->
    <VideoUploadDrawer v-model="showVideoUploadDrawer" />

    <!-- 服务客户抽屉 -->
    <ServiceCustomerDrawer v-model="showServiceCustomerDrawer" />
  </div>
</template>
