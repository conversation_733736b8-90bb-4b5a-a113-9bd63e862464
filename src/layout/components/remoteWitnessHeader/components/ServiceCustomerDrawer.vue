<script setup lang="ts">
import ClientA<PERSON>, { TodayServiceListItem } from "@/api/client";
import { useUserStore } from "@/store";
import { dayjs } from "element-plus";
import { ref, watch } from "vue";

const props = defineProps<{
  modelValue: boolean;
}>();

const emit = defineEmits<{
  "update:modelValue": [value: boolean];
}>();

const serviceCustomerList = ref<TodayServiceListItem[]>([]);

// 监听抽屉开关状态
watch(
  () => props.modelValue,
  async (newVal) => {
    if (newVal) {
      // 打开抽屉时加载数据
      try {
        const userStore = useUserStore();
        const data = await ClientApi.getTodayServiceList({
          serveAgentId: String(userStore.user.userid),
        });
        serviceCustomerList.value = data;
      } catch (error) {
        console.error("加载服务客户列表失败:", error);
        serviceCustomerList.value = [];
      }
    }
  },
  { immediate: true }
);

// 关闭抽屉
const handleClose = () => {
  emit("update:modelValue", false);
};
</script>

<template>
  <el-drawer
    :model-value="modelValue"
    size="500px"
    :with-header="false"
    :destroy-on-close="true"
    @update:model-value="$emit('update:modelValue', $event)"
  >
    <div
      class="w-full flex-row flex-y-center border-b-1 border-hex-DDDDDD pb-14"
    >
      <div class="flex-y-center flex-1">
        <div class="ml-8 text-16 color-hex-333 font-leading-25 text-left">
          当天服务人数：{{ serviceCustomerList.length }}人
        </div>
      </div>
      <div class="cursor-pointer" @click="handleClose">
        <el-icon><Close /></el-icon>
      </div>
    </div>
    <div class="p-16">
      <div
        v-for="(item, index) in serviceCustomerList"
        :key="index"
        class="mb-16 pb-16 border-b border-gray-200"
      >
        <div class="flex items-center">
          <svg-icon icon-class="khxx" size="26" color="#7F7F7F" class="mr-24" />
          <div>
            <div class="flex-y-center mb-8">
              <div
                class="fw-400 text-14 color-hex-20212B font-leading-20 text-left"
              >
                {{ item.clientName }}
              </div>
              <div
                class="ml-8 fw-400 text-14 color-hex-666666 font-leading-20 text-left"
              >
                {{ item.sfzh }}
              </div>
            </div>
            <div
              class="fw-400 text-14 color-hex-999999 font-leading-17 text-left"
            >
              处理时间：{{
                dayjs(item.serveTime).format("YYYY-MM-DD HH:mm:ss")
              }}
            </div>
          </div>
        </div>
      </div>
      <div
        v-if="serviceCustomerList.length === 0"
        class="text-center color-hex-999999 py-32"
      >
        暂无服务客户记录
      </div>
    </div>
  </el-drawer>
</template>
