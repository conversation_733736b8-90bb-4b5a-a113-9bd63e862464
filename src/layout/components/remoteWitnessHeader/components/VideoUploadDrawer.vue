<script setup lang="ts">
import {
  addFailedUpload,
  deleteFailedUploadByRoomAndFile,
  deletePendingUploadByRoomAndFile,
  getAllFailedUploads,
  getAllPendingUploads,
  updatePendingUploadStatusByRoomAndFile,
} from "@/utils/indexedDB";
import {
  uploadFile,
  type UploadConfig,
  type UploadProgress,
  type UploadResult,
} from "@/utils/wrlMessage";
import { dayjs, ElMessage } from "element-plus";
import { ref, watch } from "vue";

const props = defineProps<{
  modelValue: boolean;
}>();

const emit = defineEmits<{
  "update:modelValue": [value: boolean];
}>();

const failedVideoUploads = ref<any[]>([]);
const pendingVideoUploads = ref<any[]>([]);
const isUploadingAll = ref(false);

// 加载失败的视频上传记录
const loadFailedVideoUploads = async () => {
  try {
    const failedUploads = await getAllFailedUploads();
    failedVideoUploads.value = failedUploads;
  } catch (error) {
    console.error("加载失败的上传记录失败:", error);
    failedVideoUploads.value = [];
  }
};

// 加载待上传的视频记录
const loadPendingVideoUploads = async () => {
  try {
    const pendingUploads = await getAllPendingUploads();
    pendingVideoUploads.value = pendingUploads.filter(
      (upload) =>
        upload.uploadStatus === "pending" || upload.uploadStatus === "failed"
    );
  } catch (error) {
    console.error("加载待上传记录失败:", error);
    pendingVideoUploads.value = [];
  }
};

// 重新上传视频
const retryVideoUpload = async (uploadItem: any) => {
  try {
    console.log(
      `重新上传视频: ${uploadItem.fileName}, 房间号: ${uploadItem.roomId}`
    );

    // 构建上传配置
    const uploadConfig: UploadConfig = {
      url: "/api/upload/video", // TODO: 替换为实际的上传地址
      filePath: uploadItem.filePath || uploadItem.fileName, // 使用文件路径或文件名
      isForm: true, // 使用表单上传
      formFields: [
        {
          key: "videoFile",
          value: uploadItem.filePath || uploadItem.fileName,
          type: "file",
        },
        {
          key: "roomId",
          value: uploadItem.roomId,
          type: "content",
        },
        {
          key: "fileName",
          value: uploadItem.fileName,
          type: "content",
        },
      ],
      onProgress: (progress: UploadProgress) => {
        console.log(
          `重新上传进度: ${progress.ratio.toFixed(2)}% - ${uploadItem.fileName}`
        );
      },
      onComplete: (result: UploadResult) => {
        console.log(`重新上传完成: ${uploadItem.fileName}`, result);
      },
      onError: (error: string) => {
        console.error(`重新上传错误: ${uploadItem.fileName}`, error);
      },
    };

    // 执行上传
    const result = await uploadFile(uploadConfig);

    if (result.success) {
      // 上传成功，从失败列表中移除
      await removeFailedUpload(uploadItem);
      ElMessage.success(`视频 ${uploadItem.fileName} 重新上传成功`);
    } else {
      throw new Error(result.error || "上传失败");
    }
  } catch (error) {
    console.error(`重新上传失败: ${uploadItem.fileName}`, error);
    ElMessage.error(
      `视频 ${uploadItem.fileName} 重新上传失败: ${error instanceof Error ? error.message : "未知错误"}`
    );
  }
};

// 重新上传待上传视频
const retryPendingUpload = async (uploadItem: any) => {
  try {
    // 更新状态为上传中
    await updatePendingUploadStatusByRoomAndFile(
      uploadItem.roomId,
      uploadItem.fileName,
      "uploading"
    );

    console.log(
      `重新上传待上传视频: ${uploadItem.fileName}, 房间号: ${uploadItem.roomId}`
    );

    // 构建上传配置
    const uploadConfig: UploadConfig = {
      url: "/api/upload/video", // TODO: 替换为实际的上传地址
      filePath: uploadItem.filePath || uploadItem.fileName, // 使用文件路径或文件名
      isForm: true, // 使用表单上传
      formFields: [
        {
          key: "videoFile",
          value: uploadItem.filePath || uploadItem.fileName,
          type: "file",
        },
        {
          key: "roomId",
          value: uploadItem.roomId,
          type: "content",
        },
        {
          key: "fileName",
          value: uploadItem.fileName,
          type: "content",
        },
      ],
      onProgress: (progress: UploadProgress) => {
        console.log(
          `重新上传待上传视频进度: ${progress.ratio.toFixed(2)}% - ${uploadItem.fileName}`
        );
      },
      onComplete: (result: UploadResult) => {
        console.log(`重新上传待上传视频完成: ${uploadItem.fileName}`, result);
      },
      onError: (error: string) => {
        console.error(`重新上传待上传视频错误: ${uploadItem.fileName}`, error);
      },
    };

    // 执行上传
    const result = await uploadFile(uploadConfig);

    if (result.success) {
      // 上传成功，从待上传列表中移除
      await deletePendingUploadByRoomAndFile(
        uploadItem.roomId,
        uploadItem.fileName
      );

      // 重新加载列表
      await loadPendingVideoUploads();
      ElMessage.success(`视频 ${uploadItem.fileName} 重新上传成功`);
    } else {
      throw new Error(result.error || "上传失败");
    }
  } catch (error) {
    console.error(`重新上传待上传视频失败: ${uploadItem.fileName}`, error);

    // 存储到失败列表
    await addFailedUpload({
      roomId: uploadItem.roomId,
      fileName: uploadItem.fileName,
      failureReason: error instanceof Error ? error.message : "未知错误",
      timestamp: new Date().toISOString(),
    });

    // 从待上传列表中删除（因为已经移到失败列表了）
    await deletePendingUploadByRoomAndFile(
      uploadItem.roomId,
      uploadItem.fileName
    );

    // 重新加载列表
    await loadPendingVideoUploads();
    await loadFailedVideoUploads();

    ElMessage.error(
      `视频 ${uploadItem.fileName} 重新上传失败: ${error instanceof Error ? error.message : "未知错误"}`
    );
  }
};

// 移除失败的上传记录
const removeFailedUpload = async (uploadItem: any) => {
  try {
    await deleteFailedUploadByRoomAndFile(
      uploadItem.roomId,
      uploadItem.fileName
    );
    // 重新加载列表
    await loadFailedVideoUploads();
  } catch (error) {
    console.error("移除失败上传记录失败:", error);
  }
};

// 全部上传
const uploadAllVideos = async () => {
  if (isUploadingAll.value) {
    ElMessage.warning("正在上传中，请稍候...");
    return;
  }

  const totalVideos =
    failedVideoUploads.value.length + pendingVideoUploads.value.length;
  if (totalVideos === 0) {
    ElMessage.info("没有需要上传的视频");
    return;
  }

  isUploadingAll.value = true;
  let successCount = 0;
  let failCount = 0;

  try {
    ElMessage.info(`开始上传 ${totalVideos} 个视频`);

    // 上传失败的视频
    for (const item of failedVideoUploads.value) {
      try {
        console.log(
          `批量上传失败视频: ${item.fileName}, 房间号: ${item.roomId}`
        );

        // 构建上传配置
        const uploadConfig: UploadConfig = {
          url: "/api/upload/video", // TODO: 替换为实际的上传地址
          filePath: item.filePath || item.fileName,
          isForm: true,
          formFields: [
            {
              key: "videoFile",
              value: item.filePath || item.fileName,
              type: "file",
            },
            {
              key: "roomId",
              value: item.roomId,
              type: "content",
            },
            {
              key: "fileName",
              value: item.fileName,
              type: "content",
            },
          ],
          onProgress: (progress: UploadProgress) => {
            console.log(
              `批量上传进度: ${progress.ratio.toFixed(2)}% - ${item.fileName}`
            );
          },
        };

        // 执行上传
        const result = await uploadFile(uploadConfig);

        if (result.success) {
          // 上传成功，从失败列表中移除
          await removeFailedUpload(item);
          successCount++;
        } else {
          throw new Error(result.error || "上传失败");
        }
      } catch (error) {
        console.error(`批量上传失败视频失败: ${item.fileName}`, error);
        failCount++;
      }
    }

    // 上传待上传的视频
    for (const item of pendingVideoUploads.value) {
      try {
        // 更新状态为上传中
        await updatePendingUploadStatusByRoomAndFile(
          item.roomId,
          item.fileName,
          "uploading"
        );

        console.log(
          `批量上传待上传视频: ${item.fileName}, 房间号: ${item.roomId}`
        );

        // 构建上传配置
        const uploadConfig: UploadConfig = {
          url: "/api/upload/video", // TODO: 替换为实际的上传地址
          filePath: item.filePath || item.fileName,
          isForm: true,
          formFields: [
            {
              key: "videoFile",
              value: item.filePath || item.fileName,
              type: "file",
            },
            {
              key: "roomId",
              value: item.roomId,
              type: "content",
            },
            {
              key: "fileName",
              value: item.fileName,
              type: "content",
            },
          ],
          onProgress: (progress: UploadProgress) => {
            console.log(
              `批量上传待上传视频进度: ${progress.ratio.toFixed(2)}% - ${item.fileName}`
            );
          },
        };

        // 执行上传
        const result = await uploadFile(uploadConfig);

        if (result.success) {
          // 上传成功，从待上传列表中移除
          await deletePendingUploadByRoomAndFile(item.roomId, item.fileName);
          // 实时更新待上传列表
          await loadPendingVideoUploads();
          successCount++;
        } else {
          throw new Error(result.error || "上传失败");
        }
      } catch (error) {
        console.error(`批量上传待上传视频失败: ${item.fileName}`, error);

        // 存储到失败列表
        await addFailedUpload({
          roomId: item.roomId,
          fileName: item.fileName,
          failureReason: error instanceof Error ? error.message : "未知错误",
          timestamp: new Date().toISOString(),
        });

        // 从待上传列表中删除（因为已经移到失败列表了）
        await deletePendingUploadByRoomAndFile(item.roomId, item.fileName);
        // 实时更新待上传列表
        await loadPendingVideoUploads();

        failCount++;
      }
    }

    // 重新加载列表
    await loadFailedVideoUploads();
    await loadPendingVideoUploads();

    if (failCount === 0) {
      ElMessage.success(`全部上传完成！成功上传 ${successCount} 个视频`);
    } else {
      ElMessage.warning(
        `上传完成！成功 ${successCount} 个，失败 ${failCount} 个`
      );
    }
  } catch (error) {
    console.error("批量上传失败:", error);
    ElMessage.error("批量上传过程中发生错误");
  } finally {
    isUploadingAll.value = false;
  }
};

// 格式化文件大小
const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return "0 B";
  const k = 1024;
  const sizes = ["B", "KB", "MB", "GB"];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i];
};

// 获取上传状态文本
const getUploadStatusText = (status: string): string => {
  switch (status) {
    case "pending":
      return "等待上传";
    case "uploading":
      return "上传中";
    case "completed":
      return "已完成";
    case "failed":
      return "上传失败";
    default:
      return "未知状态";
  }
};

// 监听抽屉开关状态
watch(
  () => props.modelValue,
  async (newVal) => {
    if (newVal) {
      // 打开抽屉时加载数据
      await loadFailedVideoUploads();
      await loadPendingVideoUploads();
    }
  },
  { immediate: true }
);

// 关闭抽屉
const handleClose = () => {
  emit("update:modelValue", false);
};
</script>

<template>
  <el-drawer
    :model-value="modelValue"
    size="700px"
    :with-header="false"
    :destroy-on-close="true"
    @update:model-value="$emit('update:modelValue', $event)"
  >
    <div
      class="w-full flex-row flex-y-center border-b-1 border-hex-DDDDDD pb-14"
    >
      <div class="flex-y-center flex-1">
        <div class="ml-8 text-16 color-hex-333 font-leading-25 text-left">
          视频上传管理：失败 {{ failedVideoUploads.length }} 个，待上传
          {{ pendingVideoUploads.length }} 个
        </div>
      </div>
      <div class="flex items-center gap-8 mr-8">
        <!--FIXME: 要开启的话需要处理一下，坐席接通后要暂停上传，避免影响通话 -->
        <!-- <el-button
          type="primary"
          size="small"
          :loading="isUploadingAll"
          @click="uploadAllVideos"
        >
          全部上传
        </el-button> -->
        <div class="cursor-pointer" @click="handleClose">
          <el-icon><Close /></el-icon>
        </div>
      </div>
    </div>

    <!-- 失败上传列表 -->
    <div class="m-16">
      <div class="mb-16 text-16 color-hex-333 font-bold">
        上传失败的视频：{{ failedVideoUploads.length }}个
      </div>
      <div
        v-for="(item, index) in failedVideoUploads"
        :key="index"
        class="mb-16 pb-16 border-b border-gray-200"
      >
        <div class="flex items-center justify-between">
          <div class="flex items-center">
            <svg-icon
              icon-class="upload-failure"
              size="26"
              color="#FF4D4F"
              class="mr-24"
            />
            <div>
              <div class="flex-y-center mb-8">
                <div
                  class="fw-400 text-14 color-hex-20212B font-leading-20 text-left"
                >
                  {{ item.fileName }}
                </div>
                <div
                  class="ml-8 fw-400 text-12 color-hex-666666 font-leading-17 text-left"
                >
                  房间号: {{ item.roomId }}
                </div>
              </div>
              <div
                class="fw-400 text-14 color-hex-FF4D4F font-leading-17 text-left"
              >
                失败原因：{{ item.failureReason }}
              </div>
              <div
                class="fw-400 text-12 color-hex-999999 font-leading-17 text-left"
              >
                失败时间：{{
                  dayjs(item.timestamp).format("YYYY-MM-DD HH:mm:ss")
                }}
              </div>
            </div>
          </div>
          <div class="flex items-center">
            <el-button
              type="primary"
              size="small"
              @click="retryVideoUpload(item)"
            >
              重新上传
            </el-button>
          </div>
        </div>
      </div>
      <div
        v-if="failedVideoUploads.length === 0"
        class="text-center color-hex-999999 py-32"
      >
        暂无上传失败的视频
      </div>
    </div>

    <!-- 待上传视频列表 -->
    <div class="m-16">
      <div class="mb-16 text-16 color-hex-333 font-bold">
        待上传视频：{{ pendingVideoUploads.length }}个
      </div>
      <div
        v-for="(item, index) in pendingVideoUploads"
        :key="index"
        class="mb-16 pb-16 border-b border-gray-200"
      >
        <div class="flex items-center justify-between">
          <div class="flex items-center">
            <svg-icon
              :icon-class="
                item.uploadStatus === 'failed'
                  ? 'upload-failure'
                  : 'cloud-upload'
              "
              size="26"
              :color="item.uploadStatus === 'failed' ? '#FF4D4F' : '#3686FD'"
              class="mr-24"
            />
            <div>
              <div class="flex-y-center mb-8">
                <div
                  class="fw-400 text-14 color-hex-20212B font-leading-20 text-left"
                >
                  {{ item.fileName }}
                </div>
                <div
                  class="ml-8 fw-400 text-12 color-hex-666666 font-leading-17 text-left"
                >
                  房间号: {{ item.roomId }}
                </div>
                <div
                  class="ml-8 fw-400 text-12 color-hex-666666 font-leading-17 text-left"
                >
                  大小: {{ formatFileSize(item.fileSize) }}
                </div>
              </div>
              <div
                class="fw-400 text-14 color-hex-3686FD font-leading-17 text-left"
              >
                状态: {{ getUploadStatusText(item.uploadStatus) }}
              </div>
              <div
                class="fw-400 text-12 color-hex-999999 font-leading-17 text-left"
              >
                创建时间：{{
                  dayjs(item.timestamp).format("YYYY-MM-DD HH:mm:ss")
                }}
              </div>
            </div>
          </div>
          <div class="flex items-center">
            <el-button
              type="primary"
              size="small"
              @click="retryPendingUpload(item)"
            >
              重新上传
            </el-button>
          </div>
        </div>
      </div>
      <div
        v-if="pendingVideoUploads.length === 0"
        class="text-center color-hex-999999 py-32"
      >
        暂无待上传的视频
      </div>
    </div>
  </el-drawer>
</template>
