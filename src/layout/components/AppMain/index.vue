<script setup lang="ts">
import { useSettingsStore, useTagsViewStore } from "@/store";
import variables from "@/styles/variables.module.scss";
defineProps<{
  hasPadding?: boolean;
}>();

const router = useRouter();
const isHome = computed(() => router.currentRoute.value.name === "Dashboard");

const cachedViews = computed(() => useTagsViewStore().cachedViews); // 缓存页面集合
const isMix2 = computed(() => unref(useSettingsStore().layout) === "mix2");
const minHeight = computed(() => {
  if (useSettingsStore().tagsView) {
    return `calc(100vh - ${variables["navbar-height"]} - ${variables["tags-view-height"]})`;
  } else {
    return `calc(100vh - ${variables["navbar-height"]})`;
  }
});

const maxHeight = computed(() => {
  if (useSettingsStore().tagsView) {
    return `calc(100vh - ${variables["navbar-height"]} - ${variables["tags-view-height"]} - 24px)`;
  } else {
    return `calc(100vh - ${variables["navbar-height"]} - 24px)`;
  }
});
</script>

<template>
  <section
    class="app-main"
    :class="{ 'flex-1': isMix2, 'p-12': hasPadding }"
    :style="{ minHeight: minHeight }"
  >
    <router-view>
      <template #default="{ Component, route }">
        <transition
          enter-active-class="animate__animated animate__fadeIn"
          mode="out-in"
        >
          <div
            class="bg-white rounded-6 shadow-sm h-full overflow-auto"
            :class="{ '!bg-transparent': isHome }"
            :style="{ maxHeight: hasPadding ? maxHeight : undefined }"
          >
            <!-- 有hasPadding是主页菜单界面 才需要设置最大高度 -->
            <keep-alive :include="cachedViews">
              <component :is="Component" :key="route.path" />
            </keep-alive>
          </div>
        </transition>
      </template>
    </router-view>
  </section>
</template>

<style lang="scss" scoped>
.app-main {
  box-sizing: border-box;
  position: relative;
  background-color: var(--el-bg-color-page);
  flex: 1;
  overflow: auto;
}
</style>
