<script setup>
import { useSettingsStore } from "@/store";
import Header<PERSON>ogo from "./components/HeaderLogo/index.vue";
import HeaderRight from "./components/HeaderRight/index.vue";
const settingsStore = useSettingsStore();
const sidebarLogo = computed(() => settingsStore.sidebarLogo);
</script>

<template>
  <div class="flex items-center justify-between h-48 box-border px-16">
    <HeaderLogo v-if="sidebarLogo" />
    <HeaderRight />
  </div>
</template>

<style lang="scss" scoped></style>
