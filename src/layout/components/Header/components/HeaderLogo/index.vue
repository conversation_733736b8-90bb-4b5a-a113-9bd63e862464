<script lang="ts" setup>
import logo from "@/assets/images/home-logo.png";

defineProps({
  collapse: {
    type: Boolean,
    required: true,
  },
});
</script>

<template>
  <div class="flex-center">
    <router-link :key="+collapse" class="wh-full flex-center" to="/">
      <img :src="logo" class="logo-image" />
      <span class="ml-10">
        <!--          {{ defaultSettings.title }}-->
        统一见证服务系统
      </span>
    </router-link>
  </div>
</template>

<style lang="scss" scoped></style>
