let canvas: OffscreenCanvas | null = null;
let ctx: OffscreenCanvasRenderingContext2D | null = null;
const CAPTURE_INTERVAL = 2000;
const JPEG_QUALITY = 0.7; // 图片质量 0-1

// 压缩并转换图片为二进制数据
async function compressAndGetBinary(
  canvas: OffscreenCanvas,
  format: "image/jpeg" | "image/webp" = "image/jpeg",
  quality: number = JPEG_QUALITY
): Promise<Uint8Array> {
  const blob = await canvas.convertToBlob({
    type: format,
    quality: quality,
  });

  return new Uint8Array(await blob.arrayBuffer());
}

async function captureAndSendFrame(videoFrame: ImageBitmap) {
  if (!canvas || !ctx) {
    throw new Error("Canvas not initialized");
  }

  try {
    // 绘制帧到canvas
    ctx.drawImage(videoFrame, 0, 0, canvas.width, canvas.height);

    // 压缩并获取二进制数据
    const binaryData = await compressAndGetBinary(canvas);

    // 发送二进制数据
    self.postMessage(
      {
        type: "frame",
        data: {
          binary: binaryData,
          format: "image/jpeg",
          width: canvas.width,
          height: canvas.height,
        },
      },
      [binaryData.buffer]
    ); // 转移二进制数据所有权
  } catch (error) {
    self.postMessage({
      type: "error",
      data: "Failed to capture and send frame: " + error,
    });
  }
}

self.onmessage = async (e: MessageEvent) => {
  const { type, data } = e.data;

  switch (type) {
    case "init":
      canvas = data.canvas;
      ctx = canvas.getContext("2d") as OffscreenCanvasRenderingContext2D;

      if (!ctx) {
        self.postMessage({
          type: "error",
          data: "Failed to get canvas context",
        });
        return;
      }
      self.postMessage({ type: "initialized" });
      break;

    case "frame":
      const { videoFrame } = data;
      await captureAndSendFrame(videoFrame);
      videoFrame.close(); // 释放资源
      break;

    case "stop":
      canvas = null;
      ctx = null;
      break;
  }
};
