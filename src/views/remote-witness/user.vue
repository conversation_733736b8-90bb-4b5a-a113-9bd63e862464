<script setup lang="ts">
import { Status } from "@/config/appConfig";
import { useIntervalFn } from "@vueuse/core";
import { computed, onBeforeUnmount, onMounted, ref, watch } from "vue";

import { usePwaitStore } from "@/store";
import useRemoteWitnessStore from "@/store/modules/remoteWitness";
import type { BusinessTabChangeData } from "@/utils/eventBus";
import { emitter, EventTypes } from "@/utils/eventBus";

import CommonDialog from "@/components/CommonDialog/index.vue";
import ConnectConfirmDialog from "@/components/remoteWitness/ConnectConfirmDialog.vue";
import RemoteWitnessContainer from "@/components/remoteWitness/remoteWitnessContainer.vue";
import AgentApp from "./components/AgentApp/AgentApp.vue";
import AuditControl from "./components/AuditControl.vue";
import CustomerInfoPanel from "./components/CustomerInfoPanel.vue";
import Free from "./components/Free.vue";

// 定义类型
const pwaitStore = usePwaitStore();
const remoteWitnessStore = useRemoteWitnessStore();

// 解析bizRequestId获取业务请求ID数组
const bizRequestIdArray = computed(() => {
  if (!pwaitStore.bizRequestId) return [];
  return pwaitStore.bizRequestId.split(";").filter((id) => id.trim() !== "");
});

// 当前激活的tab索引
const currentTabIndex = ref(0);

// 根据当前激活的tab索引获取对应的bizRequestId
const currentBizRequestId = computed(() => {
  return (
    bizRequestIdArray.value[currentTabIndex.value] || bizRequestIdArray.value[0]
  );
});

// 动态生成aiApp iframe的src
const aiAppSrc = computed(() => {
  return `/bss/aizs/page/shaizsxx.sdo?ywqqid=${currentBizRequestId.value}`;
});

// aiApp 系统保活功能
const keepAiAppAlive = async () => {
  try {
    await fetch("/jsp/keepSession.jsp");
  } catch (error) {
    console.warn("aiApp 保活请求失败:", error);
  }
};

// 使用 useIntervalFn 实现30秒轮询保活
const { pause: pauseKeepAlive, resume: resumeKeepAlive } = useIntervalFn(
  keepAiAppAlive,
  30000, // 30秒
  { immediate: false } // 不立即执行
);

// 退回原因弹窗相关
const showCheckDetailDialog = ref(false);
const checkDetailUrl = ref("");

function handleCheckDetailMessage(event: MessageEvent) {
  // 只处理 aiApp iframe 的 CHECK_DETAIL 消息
  if (
    event.data &&
    event.data.type === "CHECK_DETAIL" &&
    typeof event.data.data === "string"
  ) {
    checkDetailUrl.value = event.data.data;
    showCheckDetailDialog.value = true;
  }
}

// 监听通话状态变化
watch(
  () => remoteWitnessStore.callStatus,
  async (newval) => {
    if (newval === Status.Idle) {
      remoteWitnessStore.clearRoomState();
    } else if (newval === Status.Connecting) {
      console.log("🚀 ~ newval:", newval);
      // 非空闲状态启动保活
      resumeKeepAlive();
      if (!pwaitStore.bizRequestId) return;
      //调试时跳过排队使用，直接获取业务摘要信息
      // await remoteWitnessStore.fetchBusinessSummary(pwaitStore.bizRequestId);
      await remoteWitnessStore.fetchBusinessInfo(pwaitStore.bizRequestId);
      // 从 clientUserBusinessSummary 数组中提取 ywdm 字段
      const ywdmArray = remoteWitnessStore.clientUserBusinessSummary
        .map((item) => item.ywdm)
        .filter(Boolean);
      if (ywdmArray.length > 0) {
        remoteWitnessStore.fetchYwqqyscs(ywdmArray);
      }
      remoteWitnessStore.fetchYxrwmx(pwaitStore.bizRequestId);
      remoteWitnessStore.fetchZsrwjg(pwaitStore.bizRequestId);
    } else if (newval === Status.Recording) {
      // Recording specific logic
      // 确保保活处于激活状态
      resumeKeepAlive();
    }
  },
  { immediate: true }
);

// 修复动画相关函数
onMounted(async () => {
  // 初始化播放状态
  pwaitStore.init();
  window.addEventListener("message", handleCheckDetailMessage);

  // 监听业务tab切换事件
  emitter.on(EventTypes.BUSINESS_TAB_CHANGE, (data: BusinessTabChangeData) => {
    currentTabIndex.value = data.tabIndex;
  });
});

onBeforeUnmount(() => {
  // 组件卸载时停止保活
  pauseKeepAlive();
  window.removeEventListener("message", handleCheckDetailMessage);
  // 移除mitt事件监听
  emitter.off(EventTypes.BUSINESS_TAB_CHANGE);
});
</script>
<template>
  <RemoteWitnessContainer bg-color="#F5F5F5">
    <div class="flex flex-col h-full gap-10">
      <!-- 头部 客户信息 退回原因-->
      <div
        v-if="remoteWitnessStore.callStatus !== Status.Idle"
        class="header h-50 flex-y-center bg-white rounded-8"
      >
        <svg-icon icon-class="pic_dn" size="46" />
        <div
          v-if="remoteWitnessStore.businessDataSupplement"
          class="fw-400 text-14color-hex-20212B font-leading-28 text-left"
        >
          {{
            `【${remoteWitnessStore.clientUserBusinessSummary?.[0]?.ywmc || ""}】${remoteWitnessStore.businessDataSupplement.khmc}${remoteWitnessStore.tellerInfo?.khjlmc ? ` ，客户经理 【${remoteWitnessStore.tellerInfo.khjlmc}】` : ""}
                营业部【${remoteWitnessStore.businessDataSupplement.yybmc}】`
          }}
          业务发起次数【{{ remoteWitnessStore.returnReasons.fqcs }}】
          <template v-if="remoteWitnessStore.returnReasons.scthyy">
            上次退回原因【
            <span class="text-red-500">
              {{ remoteWitnessStore.returnReasons.scthyy }}
            </span>
            】
          </template>
        </div>
      </div>

      <div class="flex flex-1 w-full overflow-hidden gap-10">
        <!-- anychat iframe 占位待后续对接-->
        <div
          v-if="remoteWitnessStore.callStatus === Status.Idle"
          class="w-500 h-full flex flex-col bg-white rounded-8"
        >
          <StatusDisplay :status="remoteWitnessStore.callStatus" />
        </div>
        <AgentApp v-else />

        <div
          class="flex-1 rounded-8 flex-center overflow-hidden"
          :class="
            remoteWitnessStore.callStatus === Status.Idle
              ? 'bg-white'
              : 'bg-[#f5f5f5]  max-w-[calc(100vw-640px)] '
          "
        >
          <!-- 空闲状态始终显示 Free 组件 -->
          <Free v-if="remoteWitnessStore.callStatus === Status.Idle" />
          <!-- 非空闲状态：如果有 centerUrl 显示 iframe，否则显示 CustomerInfoPanel -->
          <iframe
            v-else-if="pwaitStore.centerUrl"
            :src="pwaitStore.centerUrl"
            class="w-full h-full border-none"
          />
          <CustomerInfoPanel v-else />
        </div>

        <!-- aiApp iframe 占位待后续对接 -->
        <div
          v-if="remoteWitnessStore.callStatus !== Status.Idle"
          class="min-w-400 rounded-8 overflow-hidden"
        >
          <iframe id="aiApp" :src="aiAppSrc" class="w-full h-full" />
        </div>
      </div>
      <AuditControl />
    </div>
    <ConnectConfirmDialog />
    <!-- 详情预览弹窗 -->
    <CommonDialog
      v-model="showCheckDetailDialog"
      title="助审详情"
      width="80%"
      @close="showCheckDetailDialog = false"
    >
      <iframe
        v-if="checkDetailUrl"
        :src="checkDetailUrl"
        style="width: 100%; height: 65vh; border: none"
      />
    </CommonDialog>
  </RemoteWitnessContainer>
</template>

<style scoped lang="scss">
.border-shadow {
  border-bottom: 1px solid #ddd; /* 下边框颜色 */
  box-shadow: 0 1px 2px rgb(0 0 0 / 10%); /* 阴影效果 */
}

.customer-image {
  @apply w-45 h-45;

  background: url("@/assets/pic_hs.png") 100% no-repeat;
}

.user-image {
  @apply w-45 h-45;

  background: url("@/assets/pic_tzz.png") 100% no-repeat;
}

.triangle-left {
  width: 0;
  height: 0;
  border-top: 1.5rem solid transparent; /* 左边透明 */
  border-right: 1.5rem solid #e6f4ff; /* 三角形颜色 */
  border-bottom: 1.5rem solid transparent; /* 右边透明 */
  border-left: 1.5rem solid transparent; /* 右边透明 */
}

.triangle-right {
  width: 0;
  height: 0;
  border-top: 1.5rem solid transparent;
  border-right: 1.5rem solid transparent;
  border-bottom: 1.5rem solid transparent;
  border-left: 1.5rem solid #f2f2f1;
}
</style>
