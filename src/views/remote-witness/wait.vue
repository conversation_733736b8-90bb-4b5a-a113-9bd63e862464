<script setup lang="ts">
import DeviceDetection from "@/components/remoteWitness/DeviceDetection.vue";
import { TOKEN_KEY } from "@/enums/CacheEnum";
import { useUserStore } from "@/store";

const router = useRouter();
const userStore = useUserStore();

onMounted(async () => {
  // 如果有token，尝试获取用户信息
  if (localStorage.getItem(TOKEN_KEY)) {
    try {
      await userStore.getUserInfo();
    } catch (error) {
      console.warn("获取用户信息失败:", error);
    }
  }
});

// 处理设备检测完成事件
const handleDeviceReady = (deviceInfo: any) => {
  console.log("设备检测完成:", deviceInfo);
  router.push("/remote-witness/user");
};

// 处理设备异常事件
const handleDeviceError = (error: any) => {
  console.warn("设备异常:", error);
};
</script>

<template>
  <remote-witness-container>
    <DeviceDetection
      @device-ready="handleDeviceReady"
      @device-error="handleDeviceError"
    />
  </remote-witness-container>
</template>

<!-- <style lang="scss" scoped>
.bg-image {
  @apply wh-full;

  background: url("@/assets/pic_bj.png") 100% no-repeat;
  background-size: 100vw 113.5rem;
}

.image-qr {
  width: 120rem;
  height: 65rem;
  background: url("@/assets/pic_kxtb.png") 100% no-repeat;
  //background-size: 120rem 65rem;
}
</style> -->
