<script lang="ts" setup>
interface Props {
  detectionRes: boolean | null;
}
defineProps<Props>();
</script>

<template>
  <div class="flex items-center">
    <el-icon :color="detectionRes ? '#52C41A' : '#F5222D'" size="14px">
      <CircleCheckFilled v-if="detectionRes" />
      <CircleCloseFilled v-else-if="detectionRes === false" />
      <div v-else></div>
    </el-icon>
    <div
      class="ml-8 fw-400 text-14 font-leading-22 text-left"
      :class="detectionRes ? 'color-hex-52C41A' : 'color-hex-F5222D'"
    >
      {{
        detectionRes === null ? "" : detectionRes ? "检验通过" : "检验不通过"
      }}
    </div>
  </div>
</template>
