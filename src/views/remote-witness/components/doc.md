interface YwqqyscsItem {
  xskj: string; // 显示控件
  qzfs: string; // 强制方式
  display: string; // 显示
  ysmc: string; // 要素名称
  px: string; // 排序
  qy: string; // 启用
  fzysdm: string; // 父子要素代码
  qzcs: string; // 字典配置
  childys: string; // 子要素
  ycgs: string; // 隐藏规则
  scene: string; // 场景
  colspan: string; // 列跨度
  fcheck: string; // 检查
  zdcd: string; // 字段长度
  sjlx: string; // 数据类型 1:字符 2:数值 3:数组 4:分组 5:JSON数组
  mncsz: string; // 默认初始值
  id: string; // ID
  ysdm: string; // 要素代码
  xs: string; // 显示
  bzsm: string; // 备注说明
  blms: string; // 办理模式
  yslx: string; // 要素类型
  ywdm: string; // 业务代码
  fysdm: string; // 父要素代码
}

// sjlx说明:
// 1、2: 输出普通div渲染
// 3: 数组使用table组件渲染
// 4: 分组
// 5: JSON数组

// qzfs说明:
// 0: 普通
// 1: 数据字典 - 当xskj为'1'时，根据值的类型选择控件：
//    - 单值时使用radio控件渲染（disabled状态）
//    - 多值时使用checkbox控件渲染（disabled状态）
// 2: 个性字典 - 当xskj为'1'时，根据值的类型选择控件：
//    - 单值时使用radio控件渲染（disabled状态）  
//    - 多值时使用checkbox控件渲染（disabled状态）
// 3: 虚拟值
// 4: SQL值

// xskj说明:
// 0: 不显示
// 1: 显示 - 当qzfs为'1'或'2'时，根据值的类型自动选择radio或checkbox控件

// 重要说明：
// 1. 单值情况：value = "1" → 使用radio控件
// 2. 多值情况：value = "1;2;3" → 使用checkbox控件，会自动解析为 ["1", "2", "3"]
// 3. 当使用radio/checkbox控件渲染时，value保持原始值不进行字典转换，确保控件的value能正确匹配选项
// 4. radio/checkbox选项中：label为字典显示文本，value为原始值（如：{ label: "男", value: "1" }）
// 5. 传给控件的model-value为原始值或原始值数组

// ysdm和fysdm用于表示要素和父要素关系，数组类型的数据需要根据这两个字段确认关系
// ysmc表示要素名称
// 需要根据此配置结合businessData数据进行界面渲染

// 示例数据：
// 普通文本显示示例
{
    "xskj": "0",
    "qzfs": "0",
    "display": "",
    "ysmc": "证件编号",
    "px": "30",
    "qy": "",
    "fzysdm": "",
    "qzcs": "",
    "childys": "",
    "ycgs": "",
    "scene": "2;3",
    "colspan": "2",
    "fcheck": "",
    "zdcd": "",
    "sjlx": "1",
    "mncsz": "",
    "id": "902859",
    "ysdm": "zjbh",
    "xs": "1",
    "bzsm": "",
    "blms": "",
    "yslx": "",
    "ywdm": "20002",
    "fysdm": ""
}

// Radio控件显示示例（数据字典）
{
    "xskj": "1",
    "qzfs": "1", 
    "display": "",
    "ysmc": "性别",
    "px": "40",
    "qy": "",
    "fzysdm": "",
    "qzcs": "1|男;2|女", // 内联字典格式
    "childys": "",
    "ycgs": "",
    "scene": "2;3",
    "colspan": "2",
    "fcheck": "",
    "zdcd": "",
    "sjlx": "1",
    "mncsz": "",
    "id": "902860",
    "ysdm": "xb",
    "xs": "1",
    "bzsm": "",
    "blms": "",
    "yslx": "",
    "ywdm": "20002",
    "fysdm": ""
}

// Radio控件显示示例（个性字典）
{
    "xskj": "1",
    "qzfs": "2",
    "display": "",
    "ysmc": "婚姻状况",
    "px": "50",
    "qy": "",
    "fzysdm": "",
    "qzcs": "HYZK", // 字典代码，需要从字典接口获取选项
    "childys": "",
    "ycgs": "",
    "scene": "2;3",
    "colspan": "2",
    "fcheck": "",
    "zdcd": "",
    "sjlx": "1",
    "mncsz": "",
    "id": "902861",
    "ysdm": "hyzk",
    "xs": "1",
    "bzsm": "",
    "blms": "",
    "yslx": "",
    "ywdm": "20002",
    "fysdm": ""
}

// Checkbox控件显示示例（多值）
{
    "xskj": "1",
    "qzfs": "1",
    "display": "",
    "ysmc": "兴趣爱好",
    "px": "60",
    "qy": "",
    "fzysdm": "",
    "qzcs": "1|运动;2|音乐;3|阅读;4|旅游", // 内联字典格式
    "childys": "",
    "ycgs": "",
    "scene": "2;3",
    "colspan": "2",
    "fcheck": "",
    "zdcd": "",
    "sjlx": "1",
    "mncsz": "",
    "id": "902862",
    "ysdm": "xqah",
    "xs": "1",
    "bzsm": "",
    "blms": "",
    "yslx": "",
    "ywdm": "20002",
    "fysdm": ""
}

// 对应的businessData中xqah字段值为: "1;3" (表示选中了运动和阅读)
                [{
            "xskj": "0",
            "qzfs": "0",
            "display": "",
            "ysmc": "证件编号",
            "px": "30",
            "qy": "",
            "fzysdm": "",
            "qzcs": "",
            "childys": "",
            "ycgs": "",
            "scene": "2;3",
            "colspan": "2",
            "fcheck": "",
            "zdcd": "",
            "sjlx": "1",
            "mncsz": "",
            "id": "902859",
            "ysdm": "zjbh",
            "xs": "1",
            "bzsm": "",
            "blms": "",
            "yslx": "",
            "ywdm": "20002",
            "fysdm": ""
        },
        {
            "xskj": "0",
            "qzfs": "0",
            "display": "",
            "ysmc": "修改内容",
            "px": "1",
            "qy": "1",
            "fzysdm": "",
            "qzcs": "",
            "childys": "",
            "ycgs": "",
            "scene": "2;3",
            "colspan": "2",
            "fcheck": "",
            "zdcd": "",
            "sjlx": "3",
            "mncsz": "",
            "id": "64685",
            "ysdm": "xgnr",
            "xs": "1",
            "bzsm": "",
            "blms": "3",
            "yslx": "",
            "ywdm": "20002",
            "fysdm": ""
        },
        {
            "xskj": "0",
            "qzfs": "0",
            "display": "",
            "ysmc": "修改字段",
            "px": "2",
            "qy": "0",
            "fzysdm": "",
            "qzcs": "",
            "childys": "",
            "ycgs": "",
            "scene": "2;3",
            "colspan": "2",
            "fcheck": "",
            "zdcd": "",
            "sjlx": "1",
            "mncsz": "职业",
            "id": "64696",
            "ysdm": "zdmc",
            "xs": "1",
            "bzsm": "",
            "blms": "3",
            "yslx": "",
            "ywdm": "20002",
            "fysdm": "xgnr"
        },{
  "xskj": "0",
  "qzfs": "0",
  "display": "",
  "ysmc": "旧值",
  "px": "3",
  "qy": "1",
  "fzysdm": "",
  "qzcs": "",
  "childys": "",
  "ycgs": "",
  "scene": "2;3",
  "colspan": "2",
  "fcheck": "",
  "zdcd": "",
  "sjlx": "1",
  "mncsz": "其他",
  "id": "64692",
  "ysdm": "oldname",
  "xs": "1",
  "bzsm": "",
  "blms": "3",
  "yslx": "",
  "ywdm": "20002",
  "fysdm": "xgnr"
}], 对应 businessData 的数据结构为 {
            xgnr: [
                {
                    "oldname": "20231124",
                    "zdmc": "证件截止日期",
                    },
             {
                "oldname": "",
                "zdmc": "发证机关",
                }
            ],
            zjbh:'21312312312312312312321'
        }, 两个结合渲染出的 ui 内容为
        <el-row>
            <el-col :span="12">
                <div>实际受益人</div>
                <div>21312312312312312312321</div>
        </el-col>
        <el-col :span="12">
        <div>
            <div>修改内容</div>
            <el-table :data="[
                {
                    "oldname": "20231124",
                    "zdmc": "证件截止日期",
                    },
             {
                "oldname": "",
                "zdmc": "发证机关",
                }
            ]" style="width: 100%">
                <el-table-column prop="zdmc" label="修改字段" width="180" />
                <el-table-column prop="oldname" label="旧值" width="180" />
            </el-table>
        </div>
        </el-col>
        </el-row>, 每行展示2个要素，表格要素的话，独占一行，需要根据 px 字段进行排序后展示，表格内部也需要根据 px 排序
        