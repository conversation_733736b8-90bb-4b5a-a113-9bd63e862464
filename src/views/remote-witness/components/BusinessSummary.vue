<script lang="ts" setup>
import remoteWitnessApi from "@/api/remote-witness";
import { useRemoteWitnessStoreHook } from "@/store/modules/remoteWitness";
import { YwqqyscsItem } from "@/types/remote-witness";
import { fldmDictItem, getDictName } from "@/utils/dict";
import { emitter, EventTypes } from "@/utils/eventBus";
import { computed, onBeforeUnmount, onMounted, ref, watch } from "vue";

// 在文件顶部定义sjlx的enum
const Sjlx = {
  String: "1",
  Number: "2",
  Array: "3",
  JsonArray: "5",
  Group: "4",
};

// 扩展YwqqyscsItem接口，添加处理后的属性
interface ProcessedYwqqyscsItem extends YwqqyscsItem {
  value: any;
  colSpan: number;
  childItems?: ProcessedYwqqyscsItem[];
  tableData?: any[];
  hidden?: boolean; // 是否隐藏
}

interface ColumnConfig {
  prop: string;
  label: string;
  dictCode?: string;
}

interface GroupItemConfig {
  ysmc: string;
  value: any;
  colSpan: number;
  hidden: boolean;
  isHtml?: boolean; // 标记值是否包含HTML
  useRadio?: boolean;
  useCheckbox?: boolean; // 新增checkbox标识
  dictOptions?: Array<{ label: string; value: string }>;
  checkboxValues?: string[]; // 新增checkbox选中的值数组
}

// 渲染对象类型
interface RenderData {
  tabs: Array<{
    name: string;
    label: string;
    items: Array<{
      rows: Array<
        Array<{
          type: "normal" | "table" | "group";
          ysmc: string;
          value?: any;
          colSpan: number;
          hidden: boolean;
          isHtml?: boolean; // 标记值是否包含HTML
          useRadio?: boolean;
          useCheckbox?: boolean; // 新增checkbox标识
          dictOptions?: Array<{ label: string; value: string }>;
          checkboxValues?: string[]; // 新增checkbox选中的值数组
          tableData?: any[];
          columns?: ColumnConfig[];
          groupItems?: GroupItemConfig[];
        }>
      >;
    }>;
  }>;
}

const remoteWitnessStore = useRemoteWitnessStoreHook();
const ywqqyscs = computed(() => remoteWitnessStore.ywqqyscs);
const clientUserBusinessSummary = computed(
  () => remoteWitnessStore.clientUserBusinessSummary
);
const businessDataArray = computed(() => remoteWitnessStore.businessDataArray);
const dictMap = ref<Record<string, fldmDictItem[]>>({});

// 当前激活的tab
const activeTab = ref("businessInfo0");

// 弹窗相关状态
const showDetailDialog = ref(false);
const detailDialogUrl = ref("");
const detailDialogTitle = ref("详情");

// 收集所有需要的字典代码
const collectDictCodes = (): string[] => {
  const dictCodes = new Set<string>();

  ywqqyscs.value.forEach((tabItems) => {
    tabItems.forEach((item) => {
      if (item.qzcs && item.qzcs.trim() !== "") {
        const qzcs = item.qzcs.trim();

        // 检查是否为内联字典格式 (如: "1|同步中登公司;0|不同步")
        if (qzcs.includes("|") && qzcs.includes(";")) {
          // 内联字典不需要从服务端获取，直接跳过
          return;
        }

        // 如果包含HTML标签，跳过字典收集
        if (qzcs.includes("<") && qzcs.includes(">")) {
          return;
        }

        // TODO: 暂不支持SQL查询格式，如：SELECT CASE [cgzb] WHEN NULL THEN '' ELSE...
        if (qzcs.toUpperCase().startsWith("SELECT")) {
          // console.warn("暂不支持SQL查询格式的qzcs:", qzcs);
          return;
        }

        // 普通字典代码，可能是以分号分隔的多个值
        qzcs.split(";").forEach((code) => {
          if (code.trim() !== "") {
            dictCodes.add(code.trim());
          }
        });
      }
    });
  });

  return Array.from(dictCodes);
};

// 获取字典数据
const fetchDictData = async () => {
  const dictCodes = collectDictCodes();
  if (dictCodes.length > 0) {
    try {
      const res = await remoteWitnessApi.getDict({ fldm: dictCodes.join(";") });
      dictMap.value = res.dictionary || {};
      console.log("获取字典数据成功:", dictMap.value);
    } catch (error) {
      console.error("获取字典数据失败:", error);
    }
  }
};

// 解析内联字典 (格式: "1|同步中登公司;0|不同步")
const parseInlineDict = (inlineDict: string): Record<string, string> => {
  const result: Record<string, string> = {};

  try {
    // 按分号分割每个键值对
    const pairs = inlineDict.split(";");

    pairs.forEach((pair) => {
      const trimmedPair = pair.trim();
      if (trimmedPair && trimmedPair.includes("|")) {
        // 按第一个 | 分割键和值
        const pipeIndex = trimmedPair.indexOf("|");
        const key = trimmedPair.substring(0, pipeIndex).trim();
        const value = trimmedPair.substring(pipeIndex + 1).trim();

        if (key !== "" && value !== "") {
          result[key] = value;
        }
      }
    });
  } catch (error) {
    console.error("解析内联字典失败:", inlineDict, error);
  }

  return result;
};

// 检测字符串是否包含HTML标签
const containsHtml = (str: string): boolean => {
  if (!str) return false;
  // 使用正则表达式匹配HTML标签格式：<tagName> 或 </tagName> 或 <tagName attr="value">
  return /<[a-zA-Z][^>]*>/i.test(str);
};

// 替换HTML中的占位符
const replaceHtmlPlaceholders = (html: string, tabIndex: number): string => {
  if (!html || !businessDataArray.value || !businessDataArray.value[tabIndex])
    return html;

  // 使用正则表达式查找所有 [fieldName] 格式的占位符
  return html.replace(/\[([^\]]+)\]/g, (match, fieldName) => {
    const value = businessDataArray.value[tabIndex][fieldName];
    // 如果值为空或未定义，返回空字符串
    return value !== undefined && value !== null ? String(value) : "";
  });
};

// 解析并评估隐藏规则
const shouldHideByRule = (rule: string, tabIndex: number): boolean => {
  if (!rule || rule.trim() === "") return false;

  try {
    // 创建临时字段访问函数，处理特殊情况
    const getField = (fieldName: string) => {
      const businessData = businessDataArray.value[tabIndex];
      if (!businessData) return "";
      const value = businessData[fieldName];
      return value === undefined || value === null ? "" : value;
    };

    // 替换所有 [fieldName] 为函数调用
    let jsExpr = rule.replace(/\[([^\]]+)\]/g, "getField('$1')");

    // 创建函数并执行
    const evalFunc = new Function(
      "getField",
      `
      try {
        return !!(${jsExpr});
      } catch (error) {
        console.error('规则执行错误:', error);
        return false;
      }
    `
    );

    return evalFunc(getField);
  } catch (error) {
    console.error("解析隐藏规则失败:", rule, error);
    return false;
  }
};

// 根据要素代码获取业务数据，并处理字典转换
const getBusinessDataByYsdm = (
  ysdm: string,
  tabIndex: number,
  qzcs?: string,
  useRadio: boolean = false // 新增参数，标识是否用于radio控件
): { value: any; isHtml: boolean } => {
  const businessData = businessDataArray.value[tabIndex];
  if (!businessData) {
    return { value: undefined, isHtml: false };
  }

  const value = businessData[ysdm];

  // 如果是用于radio控件，直接返回原始值，不进行字典转换
  if (useRadio) {
    return {
      value: value,
      isHtml: false,
    };
  }

  // 如果有qzcs配置，先检查是否为HTML格式
  if (qzcs && qzcs.trim() !== "") {
    const trimmedQzcs = qzcs.trim();

    // 检查是否为HTML格式 - 使用更精确的检测方法
    if (containsHtml(trimmedQzcs)) {
      // 替换HTML中的占位符并返回
      const processedHtml = replaceHtmlPlaceholders(trimmedQzcs, tabIndex);
      return {
        value: processedHtml,
        isHtml: true,
      };
    }

    // 其他字典转换需要检查value是否为空
    if (value !== undefined && value !== null && value !== "") {
      // 检查是否为内联字典格式 (如: "1|同步中登公司;0|不同步")
      if (trimmedQzcs.includes("|") && trimmedQzcs.includes(";")) {
        const inlineDict = parseInlineDict(trimmedQzcs);
        const dictValue = inlineDict[String(value)] || value;
        return {
          value: dictValue,
          isHtml: containsHtml(String(dictValue)),
        };
      }

      // 普通字典代码转换
      const dictValue =
        getDictName(dictMap.value, trimmedQzcs, String(value)) || value;
      return {
        value: dictValue,
        isHtml: containsHtml(String(dictValue)),
      };
    }
  }

  return {
    value: value,
    isHtml: false,
  };
};

// 获取排序后的要素列表
const getSortedItems = (items: YwqqyscsItem[]): YwqqyscsItem[] => {
  return [...items].sort((a, b) => {
    // 判断是否为数组类型
    const aIsArray = a.sjlx === Sjlx.Array || a.sjlx === Sjlx.JsonArray;
    const bIsArray = b.sjlx === Sjlx.Array || b.sjlx === Sjlx.JsonArray;

    // 如果一个是数组类型，一个不是，数组类型排在后面
    if (aIsArray && !bIsArray) {
      return 1; // a排在b后面
    }
    if (!aIsArray && bIsArray) {
      return -1; // a排在b前面
    }

    // 如果都是相同类型（都是数组或都不是数组），则按px字段排序
    return Number(a.px || 999) - Number(b.px || 999);
  });
};

// 获取子要素列表
const getChildItems = (
  items: YwqqyscsItem[],
  parentYsdm: string
): YwqqyscsItem[] => {
  return items.filter((item) => item.fysdm === parentYsdm);
};

// 获取顶级要素列表（不是其他要素的子要素）
const getTopLevelItems = (items: YwqqyscsItem[]): YwqqyscsItem[] => {
  return items.filter((item) => !item.fysdm || item.fysdm.trim() === "");
};

// 按每行两个要素分组(表格类型除外)
function groupItemsByRow(items: YwqqyscsItem[]): YwqqyscsItem[][] {
  const result: YwqqyscsItem[][] = [];
  let currentRow: YwqqyscsItem[] = [];

  for (const item of items) {
    // 表格类型(sjlx为3或5)需要独占一行
    if (item.sjlx === Sjlx.Array || item.sjlx === Sjlx.JsonArray) {
      if (currentRow.length > 0) {
        result.push([...currentRow]);
        currentRow = [];
      }
      result.push([item]);
    } else {
      currentRow.push(item);
      if (currentRow.length === 2) {
        result.push([...currentRow]);
        currentRow = [];
      }
    }
  }

  // 处理最后一行可能不足两列的情况
  if (currentRow.length > 0) {
    result.push([...currentRow]);
  }

  return result;
}

// 格式化单元格值，用于表格
const formatCellValue = (
  row: any,
  prop: string,
  dictCode?: string
): { value: string; isHtml: boolean } => {
  if (!row || prop === undefined) return { value: "", isHtml: false };

  const value = row[prop];
  if (dictCode && value !== undefined) {
    // 检查是否为内联字典格式 (如: "1|同步中登公司;0|不同步")
    if (dictCode.includes("|") && dictCode.includes(";")) {
      const inlineDict = parseInlineDict(dictCode);
      const dictValue = inlineDict[String(value)] || value;
      return {
        value: dictValue,
        isHtml: containsHtml(String(dictValue)),
      };
    }

    // 普通字典代码转换
    const dictValue =
      getDictName(dictMap.value, dictCode, String(value)) || value;
    return {
      value: dictValue,
      isHtml: containsHtml(String(dictValue)),
    };
  }
  return { value: value || "", isHtml: false };
};

// 解析字典选项为radio选项格式
const parseRadioOptions = (
  qzcs: string
): Array<{ label: string; value: string }> => {
  const options: Array<{ label: string; value: string }> = [];

  try {
    // 检查是否为内联字典格式 (如: "1|同步中登公司;0|不同步")
    if (qzcs.includes("|") && qzcs.includes(";")) {
      const inlineDict = parseInlineDict(qzcs);
      Object.entries(inlineDict).forEach(([value, label]) => {
        options.push({ label, value });
      });
    } else {
      // 普通字典代码，从dictMap中获取选项
      const dictItems = dictMap.value[qzcs] || [];
      if (Array.isArray(dictItems)) {
        dictItems.forEach((item: any) => {
          options.push({
            label: item.note,
            value: String(item.ibm),
          });
        });
      }
    }
  } catch (error) {
    console.error("解析radio选项失败:", qzcs, error);
  }

  return options;
};

// 判断值是否为多值（包含分号分隔的多个值）
const isMultiValue = (value: any): boolean => {
  if (!value || typeof value !== "string") return false;
  return (
    value.includes(";") &&
    value.split(";").filter((v) => v.trim() !== "").length > 1
  );
};

// 解析多值字符串为数组
const parseMultiValue = (value: string): string[] => {
  if (!value || typeof value !== "string") return [];
  return value
    .split(";")
    .map((v) => v.trim())
    .filter((v) => v !== "");
};

// 判断是否应该使用radio控件渲染
const shouldUseRadio = (item: YwqqyscsItem, value: any): boolean => {
  // qzfs为'1'(数据字典)或'2'(个性字典)且xskj为'1'(显示)且为单值
  return (
    (item.qzfs === "1" || item.qzfs === "2") &&
    item.xskj === "1" &&
    !isMultiValue(value)
  );
};

// 判断是否应该使用checkbox控件渲染
const shouldUseCheckbox = (item: YwqqyscsItem, value: any): boolean => {
  // qzfs为'1'(数据字典)或'2'(个性字典)且xskj为'1'(显示)且为多值
  return (
    (item.qzfs === "1" || item.qzfs === "2") &&
    item.xskj === "1" &&
    isMultiValue(value)
  );
};

// 生成可渲染数据
const renderData = computed<RenderData>(() => {
  const tabs = ywqqyscs.value.map((tabItems, tabIndex) => {
    const sortedItems = getSortedItems(tabItems);

    // 只获取顶级要素，非子要素
    const topLevelItems = getTopLevelItems(sortedItems);

    // 过滤掉需要隐藏的项
    const visibleItems = topLevelItems.filter(
      (item) => !shouldHideByRule(item.ycgs, tabIndex)
    );
    const groupedRows = groupItemsByRow(visibleItems);

    // 处理行数据
    const rowsData = groupedRows.map((rowItems) => {
      return rowItems.map((item) => {
        // 基本数据
        const baseItem = {
          ysmc: item.ysmc,
          colSpan: item.colspan ? Number(item.colspan) * 6 : 12,
          hidden: shouldHideByRule(item.ycgs, tabIndex) || false,
        };

        // 普通字段类型
        if (item.sjlx === Sjlx.String || item.sjlx === Sjlx.Number) {
          const rawValue = businessDataArray.value[tabIndex][item.ysdm];
          const useRadio = shouldUseRadio(item, rawValue);
          const useCheckbox = shouldUseCheckbox(item, rawValue);
          const dataResult = getBusinessDataByYsdm(
            item.ysdm,
            tabIndex,
            item.qzcs,
            useRadio || useCheckbox
          );

          return {
            ...baseItem,
            type: "normal" as const,
            value: dataResult.value || "-",
            isHtml: dataResult.isHtml,
            useRadio,
            useCheckbox,
            dictOptions: useRadio
              ? parseRadioOptions(item.qzcs || "")
              : undefined,
            checkboxValues: useCheckbox
              ? parseMultiValue(String(rawValue || ""))
              : undefined,
          };
        }

        // 表格类型
        else if (item.sjlx === Sjlx.Array || item.sjlx === Sjlx.JsonArray) {
          const childItems = getSortedItems(getChildItems(tabItems, item.ysdm));
          const visibleChildItems = childItems.filter(
            (child) => !shouldHideByRule(child.ycgs, tabIndex)
          );

          const columns: ColumnConfig[] = visibleChildItems.map((child) => ({
            prop: child.ysdm,
            label: child.ysmc,
            dictCode: child.qzcs,
          }));

          return {
            ...baseItem,
            type: "table" as const,
            tableData: businessDataArray.value[tabIndex][item.ysdm] || [],
            columns,
          };
        }

        // 分组类型
        else if (item.sjlx === Sjlx.Group) {
          const childItems = getSortedItems(getChildItems(tabItems, item.ysdm));
          const visibleChildItems = childItems.filter(
            (child) => !shouldHideByRule(child.ycgs, tabIndex)
          );

          const groupItems = visibleChildItems.map((child) => {
            const rawValue = businessDataArray.value[tabIndex][child.ysdm];
            const useRadio = shouldUseRadio(child, rawValue);
            const useCheckbox = shouldUseCheckbox(child, rawValue);
            const childDataResult = getBusinessDataByYsdm(
              child.ysdm,
              tabIndex,
              child.qzcs,
              useRadio || useCheckbox
            );

            return {
              ysmc: child.ysmc,
              value: childDataResult.value || "-",
              colSpan: child.colspan ? Number(child.colspan) * 6 : 12,
              hidden: shouldHideByRule(child.ycgs, tabIndex) || false,
              isHtml: childDataResult.isHtml,
              useRadio,
              useCheckbox,
              dictOptions: useRadio
                ? parseRadioOptions(child.qzcs || "")
                : undefined,
              checkboxValues: useCheckbox
                ? parseMultiValue(String(rawValue || ""))
                : undefined,
            };
          }) as GroupItemConfig[];

          return {
            ...baseItem,
            type: "group" as const,
            groupItems,
          };
        }

        // 默认返回普通类型
        const rawValue = businessDataArray.value[tabIndex][item.ysdm];
        const useRadio = shouldUseRadio(item, rawValue);
        const useCheckbox = shouldUseCheckbox(item, rawValue);
        const defaultDataResult = getBusinessDataByYsdm(
          item.ysdm,
          tabIndex,
          item.qzcs,
          useRadio || useCheckbox
        );

        return {
          ...baseItem,
          type: "normal" as const,
          value: defaultDataResult.value || "-",
          isHtml: defaultDataResult.isHtml,
          useRadio,
          useCheckbox,
          dictOptions: useRadio
            ? parseRadioOptions(item.qzcs || "")
            : undefined,
          checkboxValues: useCheckbox
            ? parseMultiValue(String(rawValue || ""))
            : undefined,
        };
      });
    });

    // 获取当前tab对应的业务名称
    const businessSummaryItem = clientUserBusinessSummary.value[tabIndex];
    const tabLabel = businessSummaryItem?.ywmc || `业务信息${tabIndex + 1}`;

    return {
      name: `businessInfo${tabIndex}`,
      label: tabLabel,
      items: [
        {
          rows: rowsData,
        },
      ],
    };
  });

  return { tabs };
});

// 监听ywqqyscs变化，如果数据变化且当前tab不存在，则重置为第一个tab
watch(
  () => ywqqyscs.value,
  (newVal) => {
    if (
      newVal.length > 0 &&
      !renderData.value.tabs.some((tab) => tab.name === activeTab.value)
    ) {
      activeTab.value = renderData.value.tabs[0]?.name || "businessInfo0";
    }

    // 当ywqqyscs数据变化时，重新获取字典数据
    fetchDictData();
  },
  { immediate: true }
);

// 监听业务数据变化，刷新字典
watch(
  () => businessDataArray.value,
  () => {
    fetchDictData();
  },
  { deep: true }
);

// 处理tab切换事件
const handleTabChange = (tabName: string | number) => {
  const tabNameStr = String(tabName);
  activeTab.value = tabNameStr;
  // 提取tab索引
  const tabIndex = parseInt(tabNameStr.replace("businessInfo", "")) || 0;
  // 通过 mitt 发射 tab 切换事件
  emitter.emit(EventTypes.BUSINESS_TAB_CHANGE, {
    tabName: tabNameStr,
    tabIndex: tabIndex,
  });
  // 标记当前tab为已查阅
  remoteWitnessStore.markTabAsViewed(tabNameStr);
};

// 检查tab是否已查阅
const isTabViewed = (tabName: string): boolean => {
  return remoteWitnessStore.viewedTabs.has(tabName);
};

// 监听activeTab变化，自动标记第一个tab为已查阅
watch(
  () => activeTab.value,
  (newTabName) => {
    if (newTabName) {
      remoteWitnessStore.markTabAsViewed(newTabName);
    }
  },
  { immediate: true }
);

// 定义全局LBUI对象和showModalDialog方法
const setupGlobalLBUI = () => {
  if (typeof window !== "undefined") {
    // 确保LBUI对象存在
    if (!(window as any).LBUI) {
      (window as any).LBUI = {};
    }

    // 定义showModalDialog方法
    (window as any).LBUI.showModalDialog = (
      url: string,
      options?: { width?: number; height?: number; title?: string }
    ) => {
      detailDialogUrl.value = url;
      detailDialogTitle.value = options?.title || "详情";
      showDetailDialog.value = true;
    };
  }
};

// 清理全局LBUI方法
const cleanupGlobalLBUI = () => {
  if (typeof window !== "undefined" && (window as any).LBUI) {
    delete (window as any).LBUI.showModalDialog;
    // 如果LBUI对象为空，也删除它
    if (Object.keys((window as any).LBUI).length === 0) {
      delete (window as any).LBUI;
    }
  }
};

// 组件挂载时获取字典数据并设置全局方法
onMounted(() => {
  fetchDictData();
  setupGlobalLBUI();
});

// 组件卸载时清理全局方法
onBeforeUnmount(() => {
  cleanupGlobalLBUI();
});
</script>

<template>
  <div class="p-y-14 px-10 border-shadow mt-2">
    <el-tabs v-model="activeTab" class="w-full" @tab-change="handleTabChange">
      <el-tab-pane
        v-for="tab in renderData.tabs"
        :key="tab.name"
        :name="tab.name"
      >
        <template #label>
          <span>{{ tab.label }}</span>
          <span v-if="!isTabViewed(tab.name)" class="text-orange">
            （请查阅）
          </span>
        </template>
        <div v-for="(item, itemIndex) in tab.items" :key="itemIndex">
          <div v-for="(row, rowIndex) in item.rows" :key="rowIndex">
            <el-row :gutter="20">
              <template
                v-for="(element, colIndex) in row"
                :key="`${rowIndex}-${colIndex}`"
              >
                <!-- 普通要素(sjlx为1或2) -->
                <el-col
                  v-if="element.type === 'normal' && !element.hidden"
                  :span="element.colSpan"
                >
                  <div class="item-container">
                    <div class="item-label">{{ element.ysmc }}</div>

                    <!-- 使用radio控件渲染 -->
                    <div
                      v-if="element.useRadio && element.dictOptions"
                      class="item-value"
                    >
                      <el-radio-group
                        :model-value="element.value"
                        disabled
                        class="radio-group-disabled"
                      >
                        <el-radio
                          v-for="option in element.dictOptions"
                          :key="option.value"
                          :label="option.value"
                          class="radio-item"
                        >
                          {{ option.label }}
                        </el-radio>
                      </el-radio-group>
                    </div>

                    <!-- 使用checkbox控件渲染 -->
                    <div
                      v-else-if="element.useCheckbox && element.dictOptions"
                      class="item-value"
                    >
                      <el-checkbox-group
                        :model-value="element.checkboxValues"
                        disabled
                        class="checkbox-group-disabled"
                      >
                        <el-checkbox
                          v-for="option in element.dictOptions"
                          :key="option.value"
                          :label="option.value"
                          class="checkbox-item"
                        >
                          {{ option.label }}
                        </el-checkbox>
                      </el-checkbox-group>
                    </div>

                    <!-- 普通文本渲染 -->
                    <div
                      v-else-if="element.isHtml"
                      class="item-value"
                      v-html="element.value"
                    ></div>
                    <div v-else class="item-value">{{ element.value }}</div>
                  </div>
                </el-col>

                <!-- 数组类型(sjlx为3)和JSON数组(sjlx为5)使用table渲染 -->
                <el-col
                  v-else-if="element.type === 'table' && !element.hidden"
                  :span="24"
                >
                  <div class="item-container">
                    <div class="item-label">{{ element.ysmc }}</div>
                    <el-table
                      :data="element.tableData"
                      style="width: 100%"
                      border
                    >
                      <template v-if="element.columns">
                        <el-table-column
                          v-for="(column, colIdx) in element.columns"
                          :key="colIdx"
                          :prop="column.prop"
                          :label="column.label"
                          :min-width="column.label.length * 20 + 50"
                          show-overflow-tooltip
                        >
                          <template #default="scope">
                            <span
                              v-if="
                                formatCellValue(
                                  scope.row,
                                  column.prop,
                                  column.dictCode
                                ).isHtml
                              "
                              v-html="
                                formatCellValue(
                                  scope.row,
                                  column.prop,
                                  column.dictCode
                                ).value
                              "
                            ></span>
                            <span v-else>
                              {{
                                formatCellValue(
                                  scope.row,
                                  column.prop,
                                  column.dictCode
                                ).value
                              }}
                            </span>
                          </template>
                        </el-table-column>
                      </template>
                    </el-table>
                  </div>
                </el-col>

                <!-- 分组类型(sjlx为4) -->
                <el-col
                  v-else-if="element.type === 'group' && !element.hidden"
                  :span="24"
                >
                  <div class="item-container">
                    <div class="item-label">{{ element.ysmc }}</div>
                    <div class="group-container">
                      <el-row :gutter="20">
                        <template v-if="element.groupItems">
                          <template
                            v-for="(groupItem, groupIdx) in element.groupItems"
                            :key="groupIdx"
                          >
                            <el-col
                              v-if="!groupItem.hidden"
                              :span="groupItem.colSpan"
                            >
                              <div class="item-container">
                                <div class="item-label">
                                  {{ groupItem.ysmc }}
                                </div>

                                <!-- 使用radio控件渲染 -->
                                <div
                                  v-if="
                                    groupItem.useRadio && groupItem.dictOptions
                                  "
                                  class="item-value"
                                >
                                  <el-radio-group
                                    :value="groupItem.value"
                                    disabled
                                    class="radio-group-disabled"
                                  >
                                    <el-radio
                                      v-for="option in groupItem.dictOptions"
                                      :key="option.value"
                                      :label="option.value"
                                      class="radio-item"
                                    >
                                      {{ option.label }}
                                    </el-radio>
                                  </el-radio-group>
                                </div>

                                <!-- 使用checkbox控件渲染 -->
                                <div
                                  v-else-if="
                                    groupItem.useCheckbox &&
                                    groupItem.dictOptions
                                  "
                                  class="item-value"
                                >
                                  <el-checkbox-group
                                    :model-value="groupItem.checkboxValues"
                                    disabled
                                    class="checkbox-group-disabled"
                                  >
                                    <el-checkbox
                                      v-for="option in groupItem.dictOptions"
                                      :key="option.value"
                                      :label="option.value"
                                      class="checkbox-item"
                                    >
                                      {{ option.label }}
                                    </el-checkbox>
                                  </el-checkbox-group>
                                </div>

                                <!-- 普通文本渲染 -->
                                <div
                                  v-else-if="groupItem.isHtml"
                                  class="item-value"
                                  v-html="groupItem.value"
                                ></div>
                                <div v-else class="item-value">
                                  {{ groupItem.value }}
                                </div>
                              </div>
                            </el-col>
                          </template>
                        </template>
                      </el-row>
                    </div>
                  </div>
                </el-col>
              </template>
            </el-row>
          </div>
        </div>
      </el-tab-pane>
    </el-tabs>
  </div>

  <!-- 详情弹窗 -->
  <CommonDialog
    v-model="showDetailDialog"
    :title="detailDialogTitle"
    width="80%"
    @close="showDetailDialog = false"
  >
    <iframe
      v-if="detailDialogUrl"
      :src="detailDialogUrl"
      style="width: 100%; height: 65vh; border: none"
    />
  </CommonDialog>
</template>

<style scoped>
.item-container {
  margin-bottom: 16px;
}
.item-label {
  font-size: 14px;
  color: #606266;
  margin-bottom: 8px;
}
.item-value {
  font-size: 14px;
  color: #303133;
}
.group-container {
  border: 1px solid #ebeef5;
  border-radius: 4px;
  padding: 16px;
  margin-top: 8px;
}
</style>
