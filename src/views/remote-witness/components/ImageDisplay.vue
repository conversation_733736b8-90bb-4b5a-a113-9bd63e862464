<script setup lang="ts">
import { CircleCheckFilled, CircleCloseFilled } from "@element-plus/icons-vue";
import { computed } from "vue";

interface Props {
  imageUrl?: string;
  title: string;
  small?: boolean;
  status?: "success" | "error" | "none";
  statusText?: string;
}

const props = withDefaults(defineProps<Props>(), {
  small: false,
  status: "none",
  statusText: "",
});

const emit = defineEmits(["error", "load"]);

const handleImageError = (event: Event) => {
  emit("error", event);
};

const handleImageLoad = (event: Event) => {
  emit("load", event);
};

// 根据状态计算图标和颜色
const checkStatus = computed(() => {
  switch (props.status) {
    case "success":
      return {
        icon: "success",
        color: "#52C41A",
        text: props.statusText || "质检通过",
        textClass: "color-hex-52C41A",
      };
    case "error":
      return {
        icon: "error",
        color: "#F5222D",
        text: props.statusText || "检验不通过",
        textClass: "color-hex-F5222D",
      };
    default:
      return {
        icon: "none",
        color: "",
        text: "",
        textClass: "",
      };
  }
});
</script>

<template>
  <div class="relative">
    <div :class="[small ? 'image-xx-container-small' : 'image-xx-container']">
      <el-image
        v-if="imageUrl"
        :class="[small ? 'xx-size-samll' : 'xx-size']"
        fit="contain"
        :src="imageUrl"
        @error="handleImageError"
        @load="handleImageLoad"
      >
        <template #error>
          <div
            class="flex-center flex-col w-full h-full bg-[var(--el-fill-color-light)] color-[var(--el-text-color-secondary)] text-[30px]"
          >
            <el-icon>
              <Picture />
            </el-icon>
            <div class="text-sm mt-20">加载失败</div>
          </div>
        </template>
      </el-image>
      <div
        v-else
        :class="[
          small ? 'xx-size-samll' : 'xx-size',
          'bg-[var(--el-fill-color-light)]',
        ]"
      >
        <el-empty :image-size="40" style="--el-empty-padding: 20px 0" />
      </div>
      <div class="flex gap-8 items-center">
        <div class="xx-title" :class="{ 'justify-start': small }">
          {{ title }}
        </div>
        <slot name="actions"></slot>
      </div>
    </div>
    <div
      v-if="checkStatus.icon !== 'none'"
      class="flex items-center justify-center"
    >
      <el-icon :color="checkStatus.color" size="14px">
        <CircleCheckFilled v-if="checkStatus.icon === 'success'" />
        <CircleCloseFilled v-else-if="checkStatus.icon === 'error'" />
        <div v-else></div>
      </el-icon>
      <div
        class="ml-8 fw-400 text-14 font-leading-22 text-left"
        :class="checkStatus.textClass"
      >
        {{ checkStatus.text }}
      </div>
    </div>
  </div>
</template>

<style scoped lang="scss">
.image-xx-container {
  @apply m-7 bg-hex-FFFFFF border-1px border-solid border-hex-DDDDDD flex-center flex-col w-237 h-173;
}

.image-xx-container-small {
  @extend .image-xx-container; /* stylelint-disable-line */

  @apply w-142 h-174;
}

.xx-size {
  @apply w-220 h-127;
}

.xx-size-samll {
  @apply w-108 h-126;
}

.xx-title {
  @apply mt-8 fw-400 text-14 color-hex-333333 font-leading-20 text-left;
}
</style>
