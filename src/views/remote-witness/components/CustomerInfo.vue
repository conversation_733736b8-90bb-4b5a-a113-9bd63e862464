<script setup lang="ts">
import ClientAPI from "@/api/client";
import { default as remoteWitnessApi } from "@/api/remote-witness";
import { Status } from "@/config/appConfig";
import { useRemoteWitnessStore } from "@/store";
import { ElMessage, ElMessageBox } from "element-plus";
import "element-plus/es/components/message-box/style/index";
import { isEmpty } from "lodash-es";
import { computed, ref, watch } from "vue";
import ImageDisplay from "./ImageDisplay.vue";

const remoteWitnessStore = useRemoteWitnessStore();

const businessData = computed(() => remoteWitnessStore.businessData);

const customerInfo = computed(() =>
  !isEmpty(remoteWitnessStore.businessDataSupplement)
    ? remoteWitnessStore.businessDataSupplement
    : remoteWitnessStore.businessData
);

// 辅助函数，根据检测结果确定状态
const getStatusFromResult = (
  result: boolean | null
): "success" | "none" | "error" => {
  if (result === true) {
    return "success"; // 检测通过
  } else if (result === null) {
    return "none"; // 无检测结果
  } else {
    // result === false
    return "error"; // 检测失败
  }
};

// 重构：提取公共逻辑用于根据zsrwjg判断状态
const getStatusFromZsrwjgByKeyword = (
  keyword: string
): "success" | "none" | "error" => {
  const zsrwjg = remoteWitnessStore.zsrwjg;
  if (!zsrwjg || zsrwjg.length === 0) {
    return "none"; // 没有助审结果数据
  }

  const idCardEntry = zsrwjg.find((item) => item.fl === "20");
  if (
    !idCardEntry ||
    !idCardEntry.mxArray ||
    idCardEntry.mxArray.length === 0
  ) {
    return "none"; // 没有找到fl为'20'的条目或mxArray为空
  }

  const filteredItems = idCardEntry.mxArray.filter((item) =>
    item.YXLXMC?.includes(keyword)
  );

  if (filteredItems.length === 0) {
    // 如果没有匹配关键字的项，可以根据需求定义行为，这里暂时返回 'none' 或 'error'
    // 例如，如果期望必须有匹配项才算检测，则返回 'none' 或 'error'
    // 如果允许没有匹配项（例如某些情况下国徽面不是必须的），则可能需要不同逻辑
    // 当前根据用户提供的代码，是基于 every，所以如果没有项，every 会返回 true，需要注意
    // 这里我们假设如果过滤后没有项目，则表示该特定检测点的数据不存在或不适用，返回 'none'
    return "none";
  }

  const allJgzAreOne = filteredItems.every((item) => item.JGZ === "1");

  if (allJgzAreOne) {
    return "success";
  } else {
    return "error";
  }
};

// 根据zsrwjg判断身份证人像面状态
const idCardPortraitSideStatus = computed<"success" | "none" | "error">(() => {
  return getStatusFromZsrwjgByKeyword("人像面");
});

// 新增：根据zsrwjg判断身份证国徽面状态
const idCardNationalEmblemSideStatus = computed<"success" | "none" | "error">(
  () => {
    return getStatusFromZsrwjgByKeyword("国徽面");
  }
);

// 身份证照片检测状态
const idCardPhotoDetectionStatus = computed(() =>
  getStatusFromResult(remoteWitnessStore.detectionResults.idCardPhotoDetection)
);

// 现场照片检测状态
const livePhotoDetectionStatus = computed(() =>
  getStatusFromResult(remoteWitnessStore.detectionResults.livePhotoDetection)
);

const livePhotoDetectionStatusText = ref("");

// 公安校验状态
const policeVerificationStatus = computed(() =>
  getStatusFromResult(remoteWitnessStore.detectionResults.policeVerification)
);

const policeVerificationStatusText = computed(() => {
  const result = remoteWitnessStore.detectionResults.policeVerification;
  if (result === true) {
    return "检验通过"; // 校验通过
  } else if (result === null) {
    return ""; // 无校验结果
  } else {
    // result === false
    return "检验不通过"; // 校验失败
  }
});

//是否个人
const isPerson = computed(() => {
  // return true;
  return customerInfo?.value?.khlb === "0";
});

/**
 * 获取图片URL的公共方法
 * @param yxlx 图片类型代码
 * @returns 完整的图片URL
 */
const getImageUrl = (yxlx: string) => {
  try {
    if (!businessData?.value?.image) return "";
    const filepath =
      businessData?.value?.image?.find((item) => item.yxlx === yxlx)
        ?.filepath || "";
    if (!filepath) return "";
    return ClientAPI.downloadEsb + filepath;
  } catch (error) {
    console.error("🚀 ~ getImageUrl ~ error:", error);
    return "";
  }
};

//TODO: 经办人证件图片 编码是多少？
const agentImg = computed(() => getImageUrl("11"));

// 身份证人像面图片
const idCardImg = computed(() => getImageUrl("275"));

watch(idCardImg, (val) => {
  if (!val) return;
  const filepath =
    businessData?.value?.image?.find((item) => item.yxlx === "275")?.filepath ||
    "";
  if (filepath) {
    remoteWitnessStore.submitIdCardFilepath(filepath); //保存到全局 store，方便人脸对比使用
  }
});

// 身份证国徽面图片
const idCardBgImg = computed(() => getImageUrl("276"));

// 现场图
const photoOfTheScene = computed(() => getImageUrl("9"));

// 公安图相关变量
let gongAnTimer: any = null;
let gongAnTimerNum = 0;
// const gongAnFilepath = ref<string>();

const checkLoading = ref(false);
// 校验公安图
const onGongAnValidte = async () => {
  checkLoading.value = true;
  gongAnTimer = setInterval(async () => {
    const data = await remoteWitnessApi.mpsVerificationApplication({
      sfzh: customerInfo.value?.zjbh,
      xm: customerInfo.value?.khmc,
      cxlx: "12",
      cfbz: "1",
      zjlb: customerInfo.value?.zjlb,
    });
    if (gongAnTimerNum === 3) {
      clearGongAnTimer();
      checkLoading.value = false;
      return;
    }
    gongAnTimerNum++;
    if (data.cljg == "1") {
      clearGongAnTimer();

      try {
        const res = await remoteWitnessApi.mpsVerificationResult({
          id: data.id,
          sfzh: customerInfo.value?.zjbh,
          xm: customerInfo.value?.khmc,
        });
        checkLoading.value = false;
        const result = res[0];
        console.log("🚀 ~ gongAnTimer=setInterval ~ result:", result);
        if (parseInt(result.cljg) > 0) {
          // 一致
          remoteWitnessStore.submitGongAnFilepath(result.filepath); //保存到全局 store，方便人脸对比使用
          remoteWitnessStore.submitDetectionResult({
            policeVerification: true,
          });
        } else {
          // 不一致
          remoteWitnessStore.submitDetectionResult({
            policeVerification: false,
          });
        }
      } catch (error) {
        console.error("🚀 ~ gongAnTimer=setInterval ~ error:", error);
        clearGongAnTimer();
        checkLoading.value = false;
      }
    }
  }, 1000);
};

// 清除公安图校验定时器
function clearGongAnTimer() {
  clearInterval(gongAnTimer);
  gongAnTimer = null;
  gongAnTimerNum = 0;
}

// 公安图图片
const gongAnImg = computed(() => {
  if (!remoteWitnessStore.gongAnFilepath) return "";
  return ClientAPI.downloadEsb + remoteWitnessStore.gongAnFilepath;
});

// 截图相关功能
const captureLoading = ref(false);

/**
 * 调用anychat拍照功能
 */
const handleCapture = async () => {
  // 如果录制已结束，需要确认是否重新拍照
  if (remoteWitnessStore.callStatus === Status.RecordEnd) {
    try {
      await ElMessageBox.confirm(
        "录制已结束，重新拍照需要重新开始录制流程。是否确认重新拍照？",
        "确认拍照",
        {
          confirmButtonText: "确认拍照",
          cancelButtonText: "取消",
          type: "warning",
        }
      );
    } catch {
      // 用户取消，直接返回
      return;
    }
  }

  captureLoading.value = true;

  try {
    // 通过AgentApp的iframe发送拍照指令
    const agentAppFrame = document.querySelector(
      "#agentApp"
    ) as HTMLIFrameElement;
    if (agentAppFrame && agentAppFrame.contentWindow) {
      agentAppFrame.contentWindow.postMessage(
        {
          cmd: "CMDAnyChatBusinessGetUserPhoto",
        },
        "*"
      );
      console.log("已发送拍照指令");
    } else {
      console.error("找不到AgentApp iframe");
      ElMessage.error("拍照功能不可用");
    }
  } catch (error) {
    console.error("拍照失败:", error);
    ElMessage.error("拍照失败");
  } finally {
    captureLoading.value = false;
  }
};
</script>

<template>
  <div>
    <div class="p-y-14 px-10 border-shadow">
      <tag-with-indicator title="客户信息" />
    </div>
    <div class="flex flex-row mr-8 flex-wrap">
      <div class="bg-[#FCFDFE] my-8 flex flex-row">
        <template v-if="isPerson">
          <image-display
            :image-url="idCardImg"
            title="身份证人像面"
            :status="idCardPortraitSideStatus"
          />
          <image-display
            :image-url="idCardBgImg"
            title="身份证国徽面"
            :status="idCardNationalEmblemSideStatus"
          />
        </template>
        <image-display
          v-else
          :image-url="agentImg"
          title="经办人证件"
          :status="idCardPhotoDetectionStatus"
          @error="
            remoteWitnessStore.submitDetectionResult({
              idCardPhotoDetection: false,
            })
          "
          @load="
            remoteWitnessStore.submitDetectionResult({
              idCardPhotoDetection: true,
            })
          "
        />
      </div>
      <div class="bg-[#FCFDFE] my-8 w-auto flex flex-row">
        <image-display
          :image-url="photoOfTheScene"
          title="现场图"
          :status="livePhotoDetectionStatus"
          :status-text="livePhotoDetectionStatusText"
        >
          <template #actions>
            <el-tooltip
              :content="
                remoteWitnessStore.callStatus === Status.Recording
                  ? '录制过程中不允许拍照'
                  : remoteWitnessStore.callStatus === Status.RecordEnd
                    ? '录制已结束，重新拍照需要重新录制'
                    : '点击拍照'
              "
              placement="top"
            >
              <el-button
                class="mt-8"
                size="small"
                :loading="captureLoading"
                :disabled="remoteWitnessStore.callStatus === Status.Recording"
                @click="handleCapture"
              >
                拍照
              </el-button>
            </el-tooltip>
          </template>
        </image-display>
        <image-display
          :image-url="gongAnImg"
          title="公安图"
          :status="policeVerificationStatus"
          :status-text="policeVerificationStatusText"
        >
          <template #actions>
            <el-button
              class="mt-8"
              size="small"
              :loading="checkLoading"
              @click="onGongAnValidte"
            >
              校验
            </el-button>
          </template>
        </image-display>
      </div>
    </div>
  </div>
</template>

<style scoped lang="scss">
.border-shadow {
  border-bottom: 1px solid #ddd; /* 下边框颜色 */
  box-shadow: 0 1px 2px rgb(0 0 0 / 10%); /* 阴影效果 */
}
</style>
