<script lang="ts" setup>
import Client<PERSON><PERSON> from "@/api/client";
import RemoteWitnessAPI from "@/api/remote-witness";
import { Status } from "@/config/appConfig";
import { DEVICE_INFO_KEY } from "@/enums/CacheEnum";
import { usePwaitStore, useUserStore } from "@/store";
import useRemoteWitnessStore from "@/store/modules/remoteWitness";
import {
  convertToAnyChatTtsInfo,
  replaceScriptVariablesInSpeeches,
} from "@/utils/speechFormatConverter";

const remoteWitnessStore = useRemoteWitnessStore();
const pwaitStore = usePwaitStore();
const userStore = useUserStore();

let agentApp = ref<HTMLIFrameElement | null>(null);
let agentAppFrame: Window | null = null;

function sendMessage(cmd: string, data: any) {
  agentAppFrame?.postMessage(
    {
      cmd: cmd,
      msg: data,
    },
    "*"
  );
}

function agentAppInit() {
  if (!remoteWitnessStore.finalSpeeches.length) {
    ElMessage.warning("请先配置话术");
    return;
  }

  // 话术数据，进行变量转换后再转换为ttsInfo格式
  const convertedSpeeches = replaceScriptVariablesInSpeeches(
    remoteWitnessStore.finalSpeeches,
    remoteWitnessStore.businessDataSupplement,
    remoteWitnessStore.businessData,
    userStore.user.name || ""
  );

  // 转换为anychat的ttsInfo格式
  const ttsInfo = convertToAnyChatTtsInfo(
    convertedSpeeches,
    "见证话术",
    "WITNESS"
  );

  console.log("使用转换后的话术数据:", ttsInfo);

  // 读取设备信息
  let deviceInfo = null;
  const deviceInfoStr = sessionStorage.getItem(DEVICE_INFO_KEY);

  if (deviceInfoStr) {
    try {
      const parsedDeviceInfo = JSON.parse(deviceInfoStr);
      // 检查所有设备 index 是否都 >= 0
      const isValidDevice = (device: any) => device && device.index >= 0;

      if (
        isValidDevice(parsedDeviceInfo.camera) &&
        isValidDevice(parsedDeviceInfo.microphone) &&
        isValidDevice(parsedDeviceInfo.speaker)
      ) {
        deviceInfo = {
          camera: {
            index: parsedDeviceInfo.camera.index,
            name: parsedDeviceInfo.camera.name || "摄像头",
          },
          microphone: {
            index: parsedDeviceInfo.microphone.index,
            name: parsedDeviceInfo.microphone.name || "麦克风",
          },
          speaker: {
            index: parsedDeviceInfo.speaker.index,
            name: parsedDeviceInfo.speaker.name || "扬声器",
          },
          ttsData: {
            speed: parsedDeviceInfo.ttsData.speed || 1,
            tts_type: parsedDeviceInfo.ttsData.tts_type || 1,
          },
        };
      }
    } catch (error) {
      console.warn("解析设备信息失败:", error);
    }
  }

  // 通过 postMessage 传输参数
  const finalPresetData: any = {
    config: {
      appId: "95E61F1D-854C-8E41-8249-74BFE2E76D16",
      serverIp: "dev.bairuitech.cn",
      serverPort: "14603",
      serverAuthPass: "",
    },
    agentInfo: {
      nickName: userStore.user.name,
      strUserId: userStore.user.id,
      agentInfoMessage: remoteWitnessStore.agentInfoMessage,
      tipMessage: remoteWitnessStore.revealInfo,
    },
    queueInfo: {
      roomId: pwaitStore.witnessRequestId,
      hasFlow: false,
    },
    business: {
      [pwaitStore.bizCode]: {
        ttsInfo,
      },
      qualityItems: {
        checkNum: 1,
        aiCheckInterval: 10000,
        aiCompareInterval: 8000,

        asrWaitingTimeLimit: 5000, // asr回答等待时间限制
        asrErrorTimesLimit: 3, // asr回答错误次数限制
        asrBlackListTimesLimit: 3, // asr回答黑名单次数限制

        QA: {
          itemName: "一问一答",
          itemScore: 30,
          itemDeduct: 5,
          itemSecondDeduct: 15,
        },
        CF: {
          itemName: "客户人脸比对",
          itemBenchmark: 60, // 及格分
          itemScore: 40, // 满分
          itemDeduct: 5, // 每次扣分
        },
        IB: {
          itemName: "在框检测",
          itemScore: 30,
          itemDeduct: 5,
        },
        PG: {
          itemBenchmark: 60,
        },
      },
      aiAbility: {
        aiCheck: true,
        aiCompare: true,
        aiASR: true,
        aiTTS: true,
      },
    },
    recordParams: {
      fileType: 1,
      fileName: "[timestamp]",
      category: "video",
      // localFilePath: "D:\\anychat",
      mode: 1,
      videoPathPrefix: "",
      RQIAvailable: false,
      recordScreen: false,
      resolution: "640x480",
      codeRate: "400000",
    },
    videoParams: {},
    snapshotParams: {
      mode: 1,
      fileName: "[timestamp]_[userName]_[agentName]",
      category: "picture",
      localFilePath: "D:\\anychat",
    },
    // 设备信息
    // deviceInfo: {
    //   camera: {
    //     index: 0,
    //     name: "摄像头",
    //   },
    //   microphone: {
    //     index: 0,
    //     name: "麦克风",
    //   },
    //   ttsData: {
    //     speed: 1,
    //     tts_type: 1,
    //   },
    // },
    playbackType: "AUTO", //播报模式参数 AUTO:智能播报模式\MP3:MP3 播报模式\MANMADE:人工口播模式
    others: {
      pluginDownload:
        "/witness-agent/AnyChatChromeProxySetup_V9.5.0.R8.231019.exe",
    },
  };

  // 如果有有效的设备信息，添加到 finalPresetData 中
  if (deviceInfo) {
    finalPresetData.deviceInfo = deviceInfo;
  }

  sendMessage("CMDAnyChatAgentInit", {
    presetData: finalPresetData,
  });
}

onMounted(() => {
  if (agentApp.value) {
    agentAppFrame = agentApp.value.contentWindow;

    // 监听消息事件
    window.addEventListener("message", async function (event) {
      console.log("🚀 ~ event:", event);
      let cmd = event.data.cmd; // 指令名
      let msg = event.data.msg; // 消息数据

      switch (cmd) {
        // 组件准备好，可以开始初始化
        case "CMDAnyChatAgentReady":
          console.log("🚀 ~ event:", msg);
          setTimeout(() => {
            agentAppInit();
          }, 100);
          // agentAppInit();
          break;
        case "CMDAnyChatAgentInitDone":
          console.log("🚀 ~ event:", msg);
          // 初始化完成，改成connected状态
          remoteWitnessStore.$patch({ callStatus: Status.Connected });
          break;
        case "CMDAnyChatAgentRecordStart":
          console.log("🚀 ~ event:", msg);
          remoteWitnessStore.$patch({ callStatus: Status.Recording });
          break;
        case "CMDAnyChatGetUserPhoto":
          console.log("🚀 ~ CMDAnyChatGetUserPhoto:", msg);
          // 检查是否有现场图，有的话发送给anychat
          if (remoteWitnessStore.photoOfTheScene) {
            sendMessage("CMDAnyChatSendUserPhoto", {
              headShot: remoteWitnessStore.photoOfTheScene,
            });
          } else {
            console.log("现场图为空，需要先拍摄现场图");
            ElMessageBox({
              title: "提示",
              message: "现场图为空，需要先拍摄现场图",
              type: "warning",
            });
          }
          break;
        // 拍照回调
        case "CMDAnyChatReceiveSnapshotFromClient":
          console.log("🚀 ~ CMDAnyChatReceiveSnapshotFromClient:", msg);
          // 处理从客户端接收到的拍照结果
          if (msg.base64) {
            // 保存现场图base64数据到store
            remoteWitnessStore.submitPhotoOfTheScene(msg.base64);
            console.log("已保存现场图base64数据");

            // 将base64转换为blob并上传
            try {
              const byteCharacters = atob(msg.base64);
              const byteNumbers = new Array(byteCharacters.length);
              for (let i = 0; i < byteCharacters.length; i++) {
                byteNumbers[i] = byteCharacters.charCodeAt(i);
              }
              const byteArray = new Uint8Array(byteNumbers);
              const blob = new Blob([byteArray], { type: "image/jpeg" });

              const res = await ClientAPI.uploadImage({ file: blob });
              console.log("拍照图片上传成功:", res);

              // 查找是否存在yxlx===9的图片
              const existingImage =
                remoteWitnessStore.businessData?.image?.find(
                  (i) => i.yxlx === "9"
                );

              // 构建新的图片对象
              const newImage = existingImage
                ? { ...existingImage, filepath: res.filepath }
                : {
                    thyy: "",
                    yxlx: "9",
                    filepath: res.filepath,
                    thyyUpdate: "",
                    bx: "1",
                    yxlxmc: "头部正面照",
                    yxshbs: "0",
                  };

              // 执行人脸比对，使用身份证照片或公安图作为参考图片
              if (
                remoteWitnessStore.idCardFilepath ||
                remoteWitnessStore.gongAnFilepath
              ) {
                try {
                  // 获取参考图片的完整URL (公安图优先，其次是身份证照片)
                  const referenceImagePath =
                    remoteWitnessStore.gongAnFilepath ||
                    remoteWitnessStore.idCardFilepath;
                  const referenceImageUrl =
                    ClientAPI.downloadEsb + referenceImagePath;

                  // 获取参考图片并转为base64
                  const response = await fetch(referenceImageUrl);
                  const imageBlob = await response.blob();
                  const reader = new FileReader();
                  const referenceImageBase64 = await new Promise<string>(
                    (resolve) => {
                      reader.onloadend = async () => {
                        // 移除data:image/jpeg;base64,前缀
                        const base64String = reader.result as string;
                        resolve(base64String.split(",")[1]);
                      };
                      reader.readAsDataURL(imageBlob);
                    }
                  );

                  // 使用comparePhoto接口进行人脸比对
                  const compareResult = await RemoteWitnessAPI.comparePhoto({
                    base641: referenceImageBase64,
                    base642: msg.base64,
                  });

                  console.log("人脸比对结果:", compareResult);

                  // code 0 同一个人 2不是同一个人 小于0表示比对失败
                  const compareCode = compareResult.code || -1;
                  const isPassed = compareCode === 0;

                  console.log(
                    "人脸比对结果:",
                    compareCode,
                    "是否通过:",
                    isPassed
                  );

                  // 更新检测结果
                  remoteWitnessStore.submitDetectionResult({
                    livePhotoDetection: isPassed,
                  });
                } catch (error) {
                  console.error("人脸比对失败:", error);
                  // 比对失败时设置为false
                  remoteWitnessStore.submitDetectionResult({
                    livePhotoDetection: false,
                  });
                }
              } else {
                console.warn("没有找到参考图片（身份证照片或公安图）");
              }

              // 处理多个业务请求ID的情况
              const bizRequestIdStr = String(pwaitStore.bizRequestId);
              if (bizRequestIdStr.includes(";")) {
                // 多个ID的情况，需要为每个ID都调用接口
                const ids = bizRequestIdStr
                  .split(";")
                  .filter((id) => id.trim() !== "");
                await Promise.all(
                  ids.map(async (id) =>
                    RemoteWitnessAPI.modifyAcceptantApi({
                      ywqqid: id.trim(),
                      data: {
                        image: [
                          ...(remoteWitnessStore.businessData?.image?.filter(
                            (i) => i.yxlx !== "9"
                          ) || []),
                          newImage,
                        ],
                      },
                    })
                  )
                );
              } else {
                // 单个ID的情况
                await RemoteWitnessAPI.modifyAcceptantApi({
                  ywqqid: bizRequestIdStr,
                  data: {
                    image: [
                      ...(remoteWitnessStore.businessData?.image?.filter(
                        (i) => i.yxlx !== "9"
                      ) || []),
                      newImage,
                    ],
                  },
                });
              }

              // 通知store刷新业务数据
              remoteWitnessStore.fetchBusinessInfo(pwaitStore.bizRequestId);
              ElMessage.success("拍照成功");
            } catch (error) {
              console.error("拍照处理失败:", error);
              ElMessage.error("拍照处理失败");
            }
          }
          break;
        // 录制结束回调
        case "CMDAnyChatAgentRecordDone":
          console.log("🚀 ~ event:", msg);
          remoteWitnessStore.$patch({ callStatus: Status.RecordEnd });
          // TODO: 保存视频信息
          break;
        case "指令其他":
          // functionother(msg);
          break;
        // 更多其他指令请查看对接文档。.
      }
    });
  }
});
</script>

<template>
  <iframe
    id="agentApp"
    ref="agentApp"
    class="w-740"
    src="/witness-agent/anychatAgent/index.html"
  />
</template>
