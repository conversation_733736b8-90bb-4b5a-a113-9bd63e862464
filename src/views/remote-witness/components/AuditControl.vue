<script setup lang="ts">
import { Status } from "@/config/appConfig";
import { usePwaitStore } from "@/store";
import useRemoteWitnessStore from "@/store/modules/remoteWitness";
import {
  addFailedUpload,
  addPendingUpload,
  deletePendingUploadByRoomAndFile,
  updatePendingUploadStatusByRoomAndFile,
} from "@/utils/indexedDB";
import { ElMessage } from "element-plus";
import { computed, ref } from "vue";

// 初始化 store
const pwaitStore = usePwaitStore();
const remoteWitnessStore = useRemoteWitnessStore();

// 处理意见
const reason = ref<string>("");

// 弹窗相关
const dialogVisible = ref(false);
const dialogReason = ref("");
const isPass = computed(() => {
  // 检查detectionResults中所有字段是否都为true
  const {
    idCardPhotoDetection,
    livePhotoDetection,
    policeVerification,
    videoDetection,
    customerInfoDetection,
    customerName,
    idCardNumber,
    idCardExpiry,
  } = remoteWitnessStore.detectionResults;

  return !!(
    idCardPhotoDetection &&
    livePhotoDetection &&
    policeVerification &&
    videoDetection &&
    customerInfoDetection &&
    customerName &&
    idCardNumber &&
    idCardExpiry
  );
});

/**
 * 检查视频录制是否完成
 */
const checkVideoRecordingCompleted = async () => {
  // TODO: 对接视频录制完成判断
  // const res = await ClientApi.getWitness(Number(remoteWitnessStore.roomId));

  // const markerData = res.witnessRequest.markerData
  //   ? JSON.parse(res.witnessRequest.markerData)
  //   : {};

  // // 录制视频完成后会落地数据，这样用是否存在这个落地数据来判断是否完成录制
  // if (Object.keys(markerData).length === 0) {
  //   ElMessage.warning("录制视频完成后才能通过审核，请先完成录制");
  //   return;
  // }

  // 异步上传视频文件，不阻塞后续逻辑
  if (remoteWitnessStore.recordFileInfoList.length > 0) {
    uploadVideoFiles();
  }

  // 设置弹窗状态
  dialogReason.value = reason.value;
  dialogVisible.value = true;
};

/**
 * 从文件路径中提取文件名
 * @param path - 文件路径 (e.g., "D:\anychat\video\2021-10-23\13-11-15-603_1u1_57.mp4")
 * @returns 文件名 (e.g., "13-11-15-603_1u1_57.mp4")
 */
const getFileNameFromPath = (path: string): string => {
  // 替换所有反斜杠为正斜杠，然后按正斜杠分割
  let splitPath = path.replace(/\\/g, "/");
  if (splitPath.endsWith("/")) {
    splitPath = splitPath.slice(0, -1);
  }
  console.log("分割后的路径:", splitPath);
  const parts = splitPath.split("/");
  const fileName = parts[parts.length - 1];
  console.log("文件名:", fileName);
  return fileName;
};

/**
 * 异步上传视频文件
 */
const uploadVideoFiles = async () => {
  // TODO: 后续完善视频上传逻辑
  // 这里使用伪代码实现异步上传，不阻塞后续逻辑

  try {
    // 先将所有待上传文件保存到IndexedDB
    for (const fileInfo of remoteWitnessStore.recordFileInfoList) {
      try {
        await addPendingUpload({
          roomId: remoteWitnessStore.roomId,
          fileName: getFileNameFromPath(fileInfo.filePath.toString()),
          filePath: fileInfo.filePath.toString(),
          fileSize: fileInfo.filelength || 0,
          timestamp: new Date().toISOString(),
        });
        console.log(`已保存待上传文件到IndexedDB: ${fileInfo.filePath}`);
      } catch (error) {
        console.error(
          `保存待上传文件到IndexedDB失败: ${fileInfo.filePath}`,
          error
        );
      }
    }

    // 异步上传
    for (const fileInfo of remoteWitnessStore.recordFileInfoList) {
      try {
        // 更新状态为上传中
        await updatePendingUploadStatusByRoomAndFile(
          remoteWitnessStore.roomId,
          getFileNameFromPath(fileInfo.filePath.toString()),
          "uploading"
        );

        // TODO: 实际的上传逻辑
        console.log(
          `开始上传视频文件: ${fileInfo.filePath}, 房间号: ${remoteWitnessStore.roomId}`
        );

        // 模拟上传延迟
        await new Promise((resolve) => setTimeout(resolve, 1000));

        // 模拟随机失败（10%概率）
        if (Math.random() < 0.1) {
          throw new Error("网络连接失败");
        }

        // 上传成功，从IndexedDB中删除待上传记录
        await deletePendingUploadByRoomAndFile(
          remoteWitnessStore.roomId,
          getFileNameFromPath(fileInfo.filePath.toString())
        );

        console.log(`视频文件上传成功: ${fileInfo.filePath}`);
      } catch (error) {
        console.error(`视频文件上传失败: ${fileInfo.filePath}`, error);

        // 更新状态为失败
        await updatePendingUploadStatusByRoomAndFile(
          remoteWitnessStore.roomId,
          getFileNameFromPath(fileInfo.filePath.toString()),
          "failed"
        );

        // 存储上传失败的信息到IndexedDB
        await saveFailedUpload({
          roomId: remoteWitnessStore.roomId,
          fileName: getFileNameFromPath(fileInfo.filePath.toString()),
          failureReason: error instanceof Error ? error.message : "未知错误",
          timestamp: new Date().toISOString(),
        });

        // 提示坐席视频上传失败
        ElMessage.error(
          `视频 ${getFileNameFromPath(fileInfo.filePath)} 上传失败: ${
            error instanceof Error ? error.message : "未知错误"
          }`
        );
      }
    }
  } catch (error) {
    console.error("批量上传过程中发生错误:", error);
  }
};

/**
 * 保存上传失败信息到IndexedDB
 */
const saveFailedUpload = async (failedUpload: {
  roomId: string;
  fileName: string;
  failureReason: string;
  timestamp: string;
}) => {
  console.log("保存上传失败信息:", failedUpload);

  try {
    await addFailedUpload(failedUpload);
    console.log("上传失败信息已保存到IndexedDB");
  } catch (error) {
    console.error("保存上传失败信息失败:", error);
  }
};

/**
 * 显示确认弹窗
 */
const showConfirmDialog = (pass: boolean) => {
  // 如果是通过，检查视频是否已录制完成
  if (pass) {
    checkVideoRecordingCompleted();
  } else {
    // 设置弹窗状态
    dialogReason.value = reason.value;
    dialogVisible.value = true;
  }
};

/**
 * 通过审核处理
 */
const onPassClick = async () => {
  // 检查是否所有tab都已查阅
  const allTabNames = remoteWitnessStore.clientUserBusinessSummary.map(
    (_, index) => `businessInfo${index}`
  );
  if (
    allTabNames.length > 0 &&
    !remoteWitnessStore.areAllTabsViewed(allTabNames)
  ) {
    const unviewedCount = remoteWitnessStore.getUnviewedTabCount(allTabNames);
    ElMessage.warning(
      `请先查阅所有业务信息页签，还有${unviewedCount}个页签未查阅`
    );
    return;
  }

  if (isPass.value) {
    //如果所有检查都是通过，点通过就直接通过了
    remoteWitnessStore.callStatus = Status.Idle;
    pwaitStore.pass(dialogReason.value || "通过");
    reason.value = "";
    dialogReason.value = "";
    dialogVisible.value = false;
  } else {
    //否则需要弹窗确认
    showConfirmDialog(true);
  }
};

/**
 * 不通过审核处理
 */
const onNotPassClick = () => {
  // 检查是否所有tab都已查阅
  const allTabNames = remoteWitnessStore.clientUserBusinessSummary.map(
    (_, index) => `businessInfo${index}`
  );
  if (
    allTabNames.length > 0 &&
    !remoteWitnessStore.areAllTabsViewed(allTabNames)
  ) {
    const unviewedCount = remoteWitnessStore.getUnviewedTabCount(allTabNames);
    ElMessage.warning(
      `请先查阅所有业务信息页签，还有${unviewedCount}个页签未查阅`
    );
    return;
  }

  if (!reason.value) {
    //不写原因就弹窗
    showConfirmDialog(false);
    return;
  }

  remoteWitnessStore.callStatus = Status.Idle;
  pwaitStore.unpass(reason.value);
  reason.value = "";
};

/**
 * 确认操作
 */
const modalConfirmAction = (pass: boolean) => {
  if (pass) {
    // 通过审核
    remoteWitnessStore.callStatus = Status.Idle;
    pwaitStore.pass(dialogReason.value || "通过");
  } else {
    // 不通过审核
    if (!dialogReason.value) {
      return ElMessage.warning("请输入原因");
    }
    remoteWitnessStore.callStatus = Status.Idle;
    pwaitStore.unpass(dialogReason.value);
  }

  // 重置状态
  reason.value = "";
  dialogReason.value = "";
  dialogVisible.value = false;
};

const popoverVisible = ref(false);
const handleCancel = () => {
  reason.value = "";
  dialogReason.value = "";
  popoverVisible.value = false;
};

const handleConfirm = () => {
  popoverVisible.value = false;
};
</script>

<template>
  <div v-if="remoteWitnessStore.callStatus !== Status.Idle">
    <div class="h-50 w-full bg-white p-9 rounded-8 flex-x-center">
      <el-popover
        v-model:visible="popoverVisible"
        trigger="click"
        placement="top-end"
        width="510px"
      >
        <template #reference>
          <span class="flex-center">
            填写意见
            <div
              class="border-1 border-solid border-gray-200 rounded-4 py-4 px-8 cursor-pointer ml-10"
            >
              <el-icon><EditPen /></el-icon>
            </div>
          </span>
        </template>

        <el-row :gutter="16">
          <el-col :span="24">
            <el-input
              v-model="reason"
              :autosize="{ minRows: 4, maxRows: 5 }"
              type="textarea"
              placeholder="请填写处理意见"
            />
          </el-col>
          <el-col :span="24" class="mt-10">
            <el-select v-model="reason" placeholder="选择常用意见">
              <el-option label="缺少现场图" value="缺少现场图" />
              <el-option label="公安图比对不通过" value="公安图比对不通过" />
            </el-select>
          </el-col>
        </el-row>
        <div class="flex justify-end mt-10">
          <el-button @click="handleCancel">取消</el-button>
          <el-button type="primary" @click="handleConfirm">确定</el-button>
        </div>
      </el-popover>
      <el-button type="primary" class="w-112 mr-30 ml-41" @click="onPassClick">
        通过
      </el-button>
      <el-button class="w-112" @click="onNotPassClick">不通过</el-button>
    </div>
  </div>

  <!-- 提交确认弹窗 -->
  <el-dialog
    v-model="dialogVisible"
    width="660px"
    :close-on-click-modal="false"
    :show-close="true"
  >
    <template #header>
      <TagWithIndicator title="提交确认" />
    </template>
    <div class="max-h-60vh overflow-y-auto">
      <!-- 智能检测结果 -->
      <div class="m-20">
        <h3 class="text-16 font-bold mb-10 text-gray-600">智能检测结果</h3>

        <div
          class="detection-item flex-y-center justify-between py-15 border-b border-gray-200"
        >
          <span>客户证件照片检测</span>
          <el-icon
            :color="
              remoteWitnessStore.detectionResults.idCardPhotoDetection
                ? '#4EC32F'
                : '#FF4D4F'
            "
            :size="20"
          >
            <component
              :is="
                remoteWitnessStore.detectionResults.idCardPhotoDetection
                  ? 'CircleCheckFilled'
                  : 'CircleCloseFilled'
              "
            />
          </el-icon>
        </div>

        <div
          class="detection-item flex-y-center justify-between py-15 border-b border-gray-200"
        >
          <span>客户现场照片检测</span>
          <el-icon
            :color="
              remoteWitnessStore.detectionResults.livePhotoDetection
                ? '#4EC32F'
                : '#FF4D4F'
            "
            :size="20"
          >
            <component
              :is="
                remoteWitnessStore.detectionResults.livePhotoDetection
                  ? 'CircleCheckFilled'
                  : 'CircleCloseFilled'
              "
            />
          </el-icon>
        </div>

        <div
          class="detection-item flex-y-center justify-between py-15 border-b border-gray-200"
        >
          <span>公安核查</span>
          <el-icon
            :color="
              remoteWitnessStore.detectionResults.policeVerification
                ? '#4EC32F'
                : '#FF4D4F'
            "
            :size="20"
          >
            <component
              :is="
                remoteWitnessStore.detectionResults.policeVerification
                  ? 'CircleCheckFilled'
                  : 'CircleCloseFilled'
              "
            />
          </el-icon>
        </div>

        <div
          class="detection-item flex-y-center justify-between py-15 border-b border-gray-200"
        >
          <span>视频检测</span>
          <el-icon
            :color="
              remoteWitnessStore.detectionResults.videoDetection
                ? '#4EC32F'
                : '#FF4D4F'
            "
            :size="20"
          >
            <component
              :is="
                remoteWitnessStore.detectionResults.videoDetection
                  ? 'CircleCheckFilled'
                  : 'CircleCloseFilled'
              "
            />
          </el-icon>
        </div>

        <div
          class="detection-item flex-y-center justify-between py-15 border-b border-gray-200"
        >
          <span>客户信息检测</span>
          <el-icon
            :color="
              remoteWitnessStore.detectionResults.customerInfoDetection
                ? '#4EC32F'
                : '#FF4D4F'
            "
            :size="20"
          >
            <component
              :is="
                remoteWitnessStore.detectionResults.customerInfoDetection
                  ? 'CircleCheckFilled'
                  : 'CircleCloseFilled'
              "
            />
          </el-icon>
        </div>

        <!-- 证件信息 -->
        <div class="id-info flex mt-15 gap-10">
          <div
            class="id-info-item w-104 px-15 py-4 bg-white rounded-4 text-center border-1 border-solid border-[#eee]"
            :class="{
              'bg-red-50 text-red-500':
                !remoteWitnessStore.detectionResults.customerName,
            }"
          >
            <span>客户名称</span>
          </div>
          <div
            class="id-info-item w-104 px-15 py-4 bg-white rounded-4 text-center border-1 border-solid border-[#eee]"
            :class="{
              'bg-red-50 text-red-500':
                !remoteWitnessStore.detectionResults.idCardNumber,
            }"
          >
            <span>证件号码</span>
          </div>
          <div
            class="id-info-item w-104 px-15 py-4 bg-white rounded-4 text-center border-1 border-solid border-[#eee]"
            :class="{
              'bg-red-50 text-red-500':
                !remoteWitnessStore.detectionResults.idCardExpiry,
            }"
          >
            <span>证件有效期</span>
          </div>
        </div>
      </div>

      <!-- 处理意见 -->
      <div class="m-20">
        <h3 class="text-16 font-bold mb-10 text-gray-600">处理意见</h3>
        <el-input
          v-model="dialogReason"
          type="textarea"
          :rows="4"
          placeholder="处理意见"
        />
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button
          type="primary"
          :class="{ 'el-button--danger': true }"
          @click="() => modalConfirmAction(false)"
        >
          不通过
        </el-button>
        <el-button type="primary" @click="() => modalConfirmAction(true)">
          通过
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<style scoped>
.detection-item {
  transition: all 0.3s;
}
</style>
