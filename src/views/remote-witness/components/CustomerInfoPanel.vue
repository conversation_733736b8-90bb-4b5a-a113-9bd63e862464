<script setup lang="ts">
import BusinessSummary from "./BusinessSummary.vue";
import CustomerInfo from "./CustomerInfo.vue";
import CustomerInfoConfirm from "./CustomerInfoConfirm.vue";
</script>

<template>
  <div
    class="flex flex-col rounded-8 wh-full flex-6 bg-white rounded-8 overflow-y-auto overflow-x-hidden"
  >
    <!-- 客户信息 -->
    <CustomerInfo />

    <!-- 客户信息确认 -->
    <CustomerInfoConfirm />

    <!-- 业务信息 -->
    <BusinessSummary />
  </div>
</template>

<style scoped lang="scss">
.border-shadow {
  border-bottom: 1px solid #ddd; /* 下边框颜色 */
  box-shadow: 0 1px 2px rgb(0 0 0 / 10%); /* 阴影效果 */
}
</style>
