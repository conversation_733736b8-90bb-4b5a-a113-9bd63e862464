<script lang="ts" setup>
import ClientAPI from "@/api/client";
import { useDict } from "@/composables/useDict";
import { useRemoteWitnessStore } from "@/store";
import { getDictName } from "@/utils/dict";
import { dayjs } from "element-plus";
import { isEmpty } from "lodash-es";
import SimpleDetectionResult from "./SimpleDetectionResult.vue";

const remoteWitnessStore = useRemoteWitnessStore();

// 使用 useDict hooks 获取字典数据
const { dictMap } = useDict("GT_ZJLB;GT_KHZT");

const businessData = computed(() => remoteWitnessStore.businessData);
const businessDataSupplement = computed(
  () => remoteWitnessStore.businessDataSupplement
);

//businessDataSupplement不是空对象， 则表示是二次业务
const isSecondBusiness = computed(() => {
  return !isEmpty(businessDataSupplement.value);
});

const customerInfo = computed(() => {
  if (!isEmpty(businessDataSupplement.value)) {
    return businessDataSupplement.value;
  }
  return businessData.value;
});

const yxrwmx = computed(() => remoteWitnessStore.yxrwmx);

// 辅助函数：在 mxArray 中查找元素并获取其 FilePath
const getFilePathFromMxArray = (
  mxArray: any[] | undefined,
  ysdm: string
): string => {
  if (!mxArray) return "";
  const filePath = mxArray.find(
    (predicate) => predicate.YSDM === ysdm
  )?.FilePath;
  return filePath ? ClientAPI.downloadEsb + filePath : "";
};

// 辅助函数：在 mxArray 中查找元素并获取其 BDJG
const getBdJgFromMxArray = (
  mxArray: any[] | undefined,
  ysdm: string
): string | undefined => {
  if (!mxArray) return undefined;
  return mxArray.find((predicate) => predicate.YSDM === ysdm)?.BDJG;
};

// --- 根据 YXLX 计算不同部分的 yxrwmx 属性 ---
// 来自身份证正面的数据 (YXLX '275')
const idCardFrontData = computed(() =>
  yxrwmx.value?.find((item) => item.YXLX === "275")
);
// 来自身份证背面的数据 (YXLX '276')
const idCardBackData = computed(() =>
  yxrwmx.value?.find((item) => item.YXLX === "276")
);

// 旧版 '911' 场景的数据。
// 如果未找到 idCardFrontData 和 idCardBackData，则使用此数据。
const legacyPrimaryOcrData = computed(() => {
  if (
    !idCardFrontData.value &&
    !idCardBackData.value &&
    yxrwmx.value &&
    yxrwmx.value.length > 0 &&
    yxrwmx.value[0]?.YXLX === "911"
  ) {
    return yxrwmx.value[0];
  }
  return null;
});

const khmcImg = computed(() => {
  if (idCardFrontData.value) {
    // 新结构：YXLX '275'，YSDM 'khmc' 用于图像
    return getFilePathFromMxArray(idCardFrontData.value.mxArray, "khmc");
  }
  if (legacyPrimaryOcrData.value) {
    // 旧版：YXLX '911'，YSDM 'khxm' 用于图像
    return getFilePathFromMxArray(legacyPrimaryOcrData.value.mxArray, "khxm");
  }
  return "";
});

/**
 * 证件编号
 */
const zjbhImg = computed(() => {
  if (idCardFrontData.value) {
    // 新结构：YXLX '275'，YSDM 'zjbh'
    return getFilePathFromMxArray(idCardFrontData.value.mxArray, "zjbh");
  }
  if (legacyPrimaryOcrData.value) {
    // 旧版：YXLX '911'，YSDM 'zjbh'
    return getFilePathFromMxArray(legacyPrimaryOcrData.value.mxArray, "zjbh");
  }
  return "";
});

/**
 * 证件编号比对结果， 0-不一致 1-一致
 */
const zjbhCompareResult = computed(() => {
  let bdJg;
  if (idCardFrontData.value) {
    // 新结构：YXLX '275'，YSDM 'zjbh'
    bdJg = getBdJgFromMxArray(idCardFrontData.value.mxArray, "zjbh");
  } else if (legacyPrimaryOcrData.value) {
    // 旧版：YXLX '911'，YSDM 'zjbh'
    bdJg = getBdJgFromMxArray(legacyPrimaryOcrData.value.mxArray, "zjbh");
  }
  return bdJg === "1";
});

/**
 * 证件有效期图片 (例如身份证背面，包含起止日期)
 * 原备注: 证件开始日期，他的图片包含截止日期
 */
const zjqsrqImg = computed(() => {
  if (idCardBackData.value) {
    // 新结构：YXLX '276'，从 mxArray 中获取
    return getFilePathFromMxArray(idCardBackData.value.mxArray, "zjqsrq");
  }
  if (legacyPrimaryOcrData.value) {
    // 旧版：YXLX '911'，YSDM 'zjqsrq' (根据原逻辑和注释)
    return getFilePathFromMxArray(legacyPrimaryOcrData.value.mxArray, "zjqsrq");
  }
  return "";
});

/**
 * 证件开始日期和证件结束日期比对结果， 0-不一致 1-一致
 */
const zjyxqCompareResult = computed(() => {
  let bdJg1, bdJg2;
  if (idCardBackData.value) {
    // 新结构：YXLX '276' 用于日期字段
    bdJg1 = getBdJgFromMxArray(idCardBackData.value.mxArray, "zjqsrq");
    bdJg2 = getBdJgFromMxArray(idCardBackData.value.mxArray, "zjjzrq");
  } else if (legacyPrimaryOcrData.value) {
    // 旧版：YXLX '911'
    bdJg1 = getBdJgFromMxArray(legacyPrimaryOcrData.value.mxArray, "zjqsrq");
    bdJg2 = getBdJgFromMxArray(legacyPrimaryOcrData.value.mxArray, "zjjzrq");
  }

  if (!bdJg1 || !bdJg2) return false;
  return bdJg1 === "1" && bdJg2 === "1";
});

/**
 * 客户名称比对结果， 0-不一致 1-一致
 */
const khmcCompareResult = computed(() => {
  let bdJg;
  if (idCardFrontData.value) {
    // New structure: YXLX '275', YSDM 'khmc'
    bdJg = getBdJgFromMxArray(idCardFrontData.value.mxArray, "khmc");
  } else if (legacyPrimaryOcrData.value) {
    // Legacy: YXLX '911', YSDM 'khmc' for comparison
    bdJg = getBdJgFromMxArray(legacyPrimaryOcrData.value.mxArray, "khmc");
  }
  return bdJg === "1";
});

/**
 * 证件有效期
 */
const zjyxq = computed(() => {
  return (
    dayjs(customerInfo.value.zjqsrq).format("YYYY-MM-DD") +
    " - " +
    dayjs(customerInfo.value.zjjzrq).format("YYYY-MM-DD")
  );
});

/**
 * 格式化日期
 */
const formatDate = (
  date: string | number | Date | null | undefined
): string => {
  if (!date) return "";
  return dayjs(date).format("YYYY-MM-DD");
};

//校验客户信息
watchEffect(() => {
  remoteWitnessStore.submitDetectionResult({
    customerName: khmcCompareResult.value,
  });

  remoteWitnessStore.submitDetectionResult({
    idCardNumber: zjbhCompareResult.value,
  });

  if (
    customerInfo.value.zjjzrq && // 确保 zjjzrq 存在
    dayjs(customerInfo.value.zjjzrq).isAfter(dayjs()) &&
    zjyxqCompareResult.value
  ) {
    remoteWitnessStore.submitDetectionResult({
      idCardExpiry: true,
    });
  } else {
    remoteWitnessStore.submitDetectionResult({
      idCardExpiry: false,
    });
  }

  if (
    remoteWitnessStore.detectionResults.customerName &&
    remoteWitnessStore.detectionResults.idCardNumber &&
    remoteWitnessStore.detectionResults.idCardExpiry
  ) {
    remoteWitnessStore.submitDetectionResult({
      customerInfoDetection: true,
    });
  }
});
</script>

<template>
  <div>
    <div class="p-y-14 px-10 border-shadow mt-7">
      <tag-with-indicator title="客户信息确认" />
    </div>
    <div v-if="customerInfo" class="mt-20 mx-10 khxx">
      <!-- 第一行：客户号、营业部 -->
      <el-row :gutter="16">
        <el-col :span="4" text-right>
          <div class="khxx-title">客户号:</div>
        </el-col>
        <el-col :span="8">
          <div class="khxx-nr">{{ customerInfo.khh }}</div>
        </el-col>
        <el-col :span="4" text-right>
          <div class="khxx-title">营业部:</div>
        </el-col>
        <el-col :span="8">
          <div class="khxx-nr">{{ customerInfo.yybmc }}</div>
        </el-col>
      </el-row>

      <!-- 第二行：客户名称、证件类别 -->
      <el-row :gutter="16">
        <el-col :span="4" text-right>
          <div class="khxx-title">客户名称:</div>
        </el-col>
        <el-col :span="8">
          <div class="flex items-center justify-between">
            <div class="khxx-nr">
              {{ customerInfo.khmc + " " + businessData.khpymc }}
            </div>
            <SimpleDetectionResult :detectionRes="khmcCompareResult" />
          </div>
          <div class="flex items-center w-1/2">
            <img v-if="khmcImg" :src="khmcImg" class="w-full" />
          </div>
        </el-col>
        <el-col :span="4" text-right>
          <div class="khxx-title">证件类别:</div>
        </el-col>
        <el-col :span="8">
          <div class="khxx-nr">
            {{ getDictName(dictMap, "GT_ZJLB", customerInfo.zjlb) }}
          </div>
        </el-col>
      </el-row>

      <!-- 第三行：证件编号（独占一行） -->
      <el-row :gutter="16">
        <el-col :span="4" text-right>
          <div class="khxx-title">证件编号:</div>
        </el-col>
        <el-col :span="20">
          <div class="flex items-center justify-between">
            <div class="khxx-nr">{{ customerInfo.zjbh }}</div>
            <SimpleDetectionResult :detectionRes="zjbhCompareResult" />
          </div>
          <div class="flex items-center justify-between">
            <img v-if="zjbhImg" :src="zjbhImg" class="w-full" />
          </div>
        </el-col>
      </el-row>

      <!-- 第四行：证件有效期（独占一行） -->
      <el-row :gutter="16">
        <el-col :span="4" text-right>
          <div class="khxx-title">证件有效期:</div>
        </el-col>
        <el-col :span="20">
          <div class="flex items-center justify-between">
            <div class="khxx-nr">{{ zjyxq }}</div>
            <SimpleDetectionResult :detectionRes="zjyxqCompareResult" />
          </div>
          <div class="flex items-center justify-between">
            <img v-if="zjqsrqImg" :src="zjqsrqImg" class="w-full" />
          </div>
        </el-col>
      </el-row>

      <!-- 第五行：开户日期、手机 -->
      <el-row v-if="isSecondBusiness" :gutter="16">
        <el-col :span="4" text-right>
          <div class="khxx-title">开户日期:</div>
        </el-col>
        <el-col :span="8">
          <div class="khxx-nr">
            {{ formatDate(customerInfo.khrq) }}
          </div>
        </el-col>
        <el-col :span="4" text-right>
          <div class="khxx-title">手机:</div>
        </el-col>
        <el-col :span="8">
          <div class="khxx-nr">{{ customerInfo.sj }}</div>
        </el-col>
      </el-row>

      <!-- 第六行：客户状态、客户星级 -->
      <el-row :gutter="16">
        <el-col :span="4" text-right>
          <div class="khxx-title">客户状态:</div>
        </el-col>
        <el-col :span="8">
          <div class="khxx-nr">
            {{ getDictName(dictMap, "GT_KHZT", customerInfo.khzt) }}
          </div>
        </el-col>
        <el-col :span="4" text-right>
          <div class="khxx-title">客户星级:</div>
        </el-col>
        <el-col :span="8">
          <div class="khxx-nr">{{ customerInfo.khxj }}</div>
        </el-col>
      </el-row>

      <!-- 第七行：联系地址、单位 -->
      <el-row :gutter="16">
        <el-col :span="4" text-right>
          <div class="khxx-title">联系地址:</div>
        </el-col>
        <el-col :span="8">
          <div class="khxx-nr">{{ customerInfo.dz }}</div>
        </el-col>
        <el-col :span="4" text-right>
          <div class="khxx-title">单位:</div>
        </el-col>
        <el-col :span="8">
          <div class="khxx-nr">{{ customerInfo.gzdw }}</div>
        </el-col>
      </el-row>

      <!-- 第八行：职业、职务 -->
      <el-row :gutter="16">
        <el-col :span="4" text-right>
          <div class="khxx-title">职业:</div>
        </el-col>
        <el-col :span="8">
          <div class="khxx-nr">{{ customerInfo.zy }}</div>
        </el-col>
        <el-col :span="4" text-right>
          <div class="khxx-title">职务:</div>
        </el-col>
        <el-col :span="8">
          <div class="khxx-nr">{{ customerInfo.zw }}</div>
        </el-col>
      </el-row>

      <!-- 第九行：发起人、备注 -->
      <el-row :gutter="16">
        <el-col :span="4" text-right>
          <div class="khxx-title">发起人:</div>
        </el-col>
        <el-col :span="8">
          <div class="khxx-nr">{{ customerInfo.fqr }}</div>
        </el-col>
        <el-col :span="4" text-right>
          <div class="khxx-title">备注:</div>
        </el-col>
        <el-col :span="8">
          <div class="khxx-nr">{{ customerInfo.bz }}</div>
        </el-col>
      </el-row>
    </div>
  </div>
</template>

<style scoped>
.khxx-nr {
  @apply fw-400 text-14 color-hex-20212B font-leading-22 text-left;
}

.khxx-title {
  @apply fw-400 text-14 color-hex-666666 font-leading-22;
}

.khxx {
  .el-row {
    @apply mb-20;
  }
}
</style>
