<script setup lang="ts">
import { TOKEN_KEY } from "@/enums/CacheEnum";
import { useUserStore } from "@/store";
import useRemoteWitnessStore from "@/store/modules/remoteWitness";
import { ElMessage } from "element-plus";
// import { copyText } from "vue3-clipboard";
// import WebSocketSingleton from "@/utils/WebSocketSingleton";

const router = useRouter();
const store = useRemoteWitnessStore();
store.getHsnr();

const handleInto = async () => {
  // 检查设备是否都正常
  const hasCamera = store.cameraList.length > 0;
  const hasMicrophone = store.microphoneList.length > 0;
  const hasSpeaker = store.speakerList.length > 0;

  // 如果有设备未检测到，则提示用户并阻止签入
  if (!hasCamera || !hasMicrophone || !hasSpeaker) {
    let errorMsg = "无法签入坐席，";
    if (!hasCamera) {
      errorMsg += "未检测到摄像头";
    }
    if (!hasMicrophone) {
      errorMsg += (!hasCamera ? "、" : "") + "未检测到麦克风";
    }
    if (!hasSpeaker) {
      errorMsg += (!hasCamera || !hasMicrophone ? "、" : "") + "未检测到扬声器";
    }
    errorMsg += "，请检查设备连接后重试。";

    ElMessage({
      message: errorMsg,
      type: "error",
      duration: 5000,
    });
    return;
  }

  try {
    // 建立连接
    await router.push("/remote-witness/user");
  } catch (error) {
    console.error("WebSocket connection error:", error);
  }
};

const userStore = useUserStore();

onMounted(async () => {
  // 如果有token，尝试获取用户信息
  if (localStorage.getItem(TOKEN_KEY)) {
    try {
      await userStore.getUserInfo();
    } catch (error) {
      console.warn("获取用户信息失败:", error);
    }
  }

  // 确保设备列表已更新
  if (
    store.cameraList.length === 0 ||
    store.microphoneList.length === 0 ||
    store.speakerList.length === 0
  ) {
    // 尝试从本地存储获取设备ID
    store.getDeviceFromStorage();
  }
});

// function copy() {
//   copyText(inviteLink, undefined, async (error: any) => {
//     if (error) {
//       ElMessage({ message: "Copy failed!", type: "error" });
//     } else {
//       ElMessage({ message: "Copied!", type: "success" });
//     }
//   });
// }
</script>

<template>
  <remote-witness-container>
    <div class="bg-image overflow-auto">
      <div class="flex items-center w-full h-760">
        <div class="flex-1 flex-center">
          <webrtc-check />
        </div>
        <div
          class="w-0.25rem h-74.75rem border-0.25rem border-solid border-hex-D0D0D0"
        ></div>
        <div class="flex-1 flex-center">
          <div class="flex-center flex-col">
            <div class="image-qr"></div>
            <div
              class="w-65rem h-7rem fw-400 text-5rem color-hex-20212B font-leading-7rem text-right mb-7.5rem"
            >
              签入坐席前，请确保设备正常
            </div>
            <el-button type="primary" size="large" @click="handleInto">
              签入坐席
            </el-button>
          </div>
        </div>
      </div>
    </div>
  </remote-witness-container>
</template>

<style lang="scss" scoped>
.bg-image {
  @apply wh-full;

  background: url("@/assets/pic_bj.png") 100% no-repeat;
  background-size: 100vw 113.5rem;
}

.image-qr {
  width: 120rem;
  height: 65rem;
  background: url("@/assets/pic_kxtb.png") 100% no-repeat;
  //background-size: 120rem 65rem;
}
</style>
