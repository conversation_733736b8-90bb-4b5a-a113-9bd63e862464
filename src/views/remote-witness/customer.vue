<script lang="ts" setup>
import { WebSocketAction } from "@/enums/WebSocketActionEnum";
import useRemoteWitnessStore from "@/store/modules/remoteWitness";
import { getParamKey } from "@/utils/utils";
import { setupWebSocketConnection } from "@/utils/websocket";
import { Client } from "@stomp/stompjs";
import TRTC from "trtc-sdk-v5";
const loading = ref(false);
const route = useRoute();
const store = useRemoteWitnessStore();
const ywqqid: string = route.query.ywqqid as string;
const token: string = route.query.customerToken as string;
const SDKAppId: string = route.query.SDKAppId as string;
const host = window.location.hostname; // 获取主机名，例如 'localhost'
const port = 8090;
const socket = ref<Client | null>(null); // 建立和 pad 端的连接
const disConnectSocket = () => {}; // 建立和 pad 端的连接
const trtc = TRTC.create();
const sdkAppId = parseInt(getParamKey("sdkAppId"), 10);
const myUserId = getParamKey("userId");
const userSig = getParamKey("userSig");
const roomId = parseInt(getParamKey("roomId"), 10);
console.log(roomId, "roomId");
const initSocket = () => {
  // 使用您的认证 token
  const { client, disconnect } = setupWebSocketConnection(token);
  socket.value = client;
  disConnectSocket.value = disconnect;
};
onMounted(() => {
  initSocket();
});
onUnmounted(() => {
  disConnectSocket.value && disConnectSocket.value();
});
async function sendMessage() {
  try {
    loading.value = true;
    await handleEnter();

    socket.value?.publish({
      destination: "/message",
      body: JSON.stringify({
        recipient: "sl001",
        content: JSON.stringify({
          action: WebSocketAction.ENTER_ROOM,
          roomId: roomId,
        }),
      }),
    });
    loading.value = false;
  } catch (e) {
    loading.value = false;
  }
}

async function handleEnter() {
  try {
    await trtc.enterRoom({
      roomId,
      sdkAppId,
      userId: myUserId,
      userSig,
    });

    trtc.on(TRTC.EVENT.REMOTE_VIDEO_AVAILABLE, handleRemoteVideoAvailable);
    trtc.on(TRTC.EVENT.REMOTE_VIDEO_UNAVAILABLE, handleRemoteVideoUnavailable);

    await trtc.startLocalAudio();
    await trtc.startLocalVideo({
      view: "local",
      option: {
        profile: "1080p",
      },
    });
  } catch (error: any) {
    ElMessage({
      message: error.message,
      type: "error",
    });
  }
}

async function handleRemoteVideoAvailable(event: any) {
  console.log("[demo] Video Available", event);
  const { userId, streamType } = event;
  try {
    if (streamType === TRTC.TYPE.STREAM_TYPE_MAIN) {
      store.invitedRemoteUsers.push(`${userId}_main`);
      await nextTick();
      await trtc.startRemoteVideo({
        userId,
        streamType,
        view: `remote`,
      });
    } else {
      store.invitedRemoteUsers.push(`${userId}_screen`);
      await nextTick();
      trtc.startRemoteVideo({ userId, streamType, view: `${userId}_screen` });
    }
    console.log(`startRemoteVideo success: [${userId}]`);
  } catch (error: any) {
    console.log(
      `startRemoteVideo failed: [${userId}], error: ${error.message}`
    );
  }
}

async function handleRemoteVideoUnavailable(event: any) {
  console.log("[demo] Video Unavailable", event);
  const { streamType } = event;
  trtc.stopRemoteVideo({ userId: event.userId, streamType });
  if (streamType === TRTC.TYPE.STREAM_TYPE_MAIN) {
    store.invitedRemoteUsers = store.invitedRemoteUsers.filter(
      (userId: string) => userId !== `${event.userId}_main`
    );
  } else {
    store.invitedRemoteUsers = store.invitedRemoteUsers.filter(
      (userId: string) => userId !== `${event.userId}_screen`
    );
  }
}

async function handleExit() {
  try {
    trtc.off(TRTC.EVENT.REMOTE_VIDEO_AVAILABLE, handleRemoteVideoAvailable);
    trtc.off(TRTC.EVENT.REMOTE_VIDEO_UNAVAILABLE, handleRemoteVideoUnavailable);
    await trtc.exitRoom();
    await trtc.stopLocalVideo();
    await trtc.stopLocalAudio();
  } catch (error: any) {
    ElMessage({
      message: `Exit room failed. Error: ${error.message}`,
      type: "error",
    });
  }
}
</script>
<template>
  <div>
    <div class="flex">
      <div id="local" class="w-200 h-200 bg-blue mr-20"></div>
      <div id="remote" class="w-200 h-200 bg-red"></div>
    </div>

    <el-button :loading="loading" type="primary" @click="sendMessage">
      发起排队请求
    </el-button>
    <el-button :loading="loading" type="primary" @click="handleExit">
      离开
    </el-button>
  </div>
</template>
<style lang="stylus" scoped></style>
