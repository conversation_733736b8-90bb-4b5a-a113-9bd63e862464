export type ClientUserInfoType = Record<string, any> & {
  image: {
    thyy: string;
    yxlx: string;
    filepath: string;
    thyyUpdate: string;
    bx: string;
    yxlxmc: string;
    yxshbs: string;
  }[];
};

// 预设数据类型定义
export type PresetData = {
  /** 配置信息 - AnyChat 音视频能力平台连接配置 */
  config: Config;
  /** 坐席信息 */
  agentInfo: AgentInfo;
  /** 队列信息配置 */
  queueInfo: QueueInfo;
  /** 业务功能数据 */
  business?: Business;
  /** 音视频参数 */
  videoParams?: VideoParams;
  /** 录像参数 */
  recordParams?: RecordParams;
  /** 设备参数 */
  deviceSelected?: DeviceSelected;
};

/** Config配置 */
export interface Config {
  /** 应用ID */
  appId: string;
  /** 服务器IP */
  serverIp: string;
  /** 服务器端口 */
  serverPort: string;
  /** 服务器认证密码 */
  serverAuthPass?: string;
}

/** 坐席登录信息 */
export interface AgentInfo {
  /** 登录昵称 */
  nickName: string;
  /** 登录账户(唯一标识) */
  strUserId: string;
  /** 登录密码 */
  password?: string;
  /** 签名字符串 */
  sign?: string;
  /** 签名时间戳 */
  timeStamp?: number;
  /** 启用全局坐席 */
  isGlobalAgent?: boolean;
}

/** 进房间所需配置 */
export interface QueueInfo {
  /** roomID */
  roomId: string;
  /** 是否需要后管 */
  hasFlow?: boolean;
}
/** 业务功能数据 */
export interface Business {
  /** 某项业务编码的业务数据 */
  [businessCode: string]: BusinessData;
}
/** 业务功能数据 */
export interface BusinessData {
  /** 话术提示列表 */
  speechCraftInfo?: SpeechCraftInfo[];
  /** 智能播报信息 */
  ttsInfo?: TTSInfoItem[];
  /** AI 能力开关 */
  aiAbility?: AiAbility;
  /** 质检规则 */
  qualityItems?: QualityItems;
}

export interface SpeechCraftInfo {
  [key: string]: any;
}

/** 视频参数对象类型 */
export interface VideoParams {
  /** 图片水印 */
  picWatermark?: PicWatermark;
  /** 文字水印 */
  textWatermark?: TextWatermark;
}

interface PicWatermark {
  /** 图片透明度 */
  alpha?: number;
  /** 图片水印在x轴方向上的起始位置（百分比，范围0~100） */
  posx: number;
  /** 图片水印在y轴方向上的起始位置（百分比，范围0~100） */
  posy: number;
  /** 图片的宽度，可传0，表示应用图片的原始宽度，传其他值时建议按高宽比例来设置 */
  overlayingwidth: number;
  /** 图片的高度，可传0，表示应用图片的原始高度，传其他值时建议按高宽比例来设置 */
  overlayimgheight: number;
  /** 图片的路径，传入图片的本地路径（绝对路径） */
  imagepath: string;
}

interface TextWatermark {
  /** 图片透明度 */
  alpha?: number;
  /** 图片水印在x轴方向上的起始位置（百分比，范围0~100） */
  posx: number;
  /** 图片水印在y轴方向上的起始位置（百分比，范围0~100） */
  posy: number;
  /** 文字颜色，颜色值采用十六进制rgb格式 */
  fontcolor: string;
  /** 文字大小 */
  fontsize?: number;
  /** 文字内容，若加上[timestamp]，则表示增加时间戳 */
  text: string;
}

/** 录像参数对象类型 */
export interface RecordParams {
  /** 录像文件名 */
  fileName?: string;
  /** 存放目录 */
  category?: string;
  /** 本地录制文件地址 */
  localFilePath?: string;
  /** 录制模式 */
  mode?: number;
  /** 录像分辨率 */
  resolution?: string;
  /** 码率 */
  codeRate?: string;
  /** 图片水印 */
  picWatermark?: PicWatermark;
  /** 文字水印 */
  textWatermark?: TextWatermark;
}

/** 设备选择对象类型 */
export type DeviceSelected = Record<string, any>;

// TTSInfoItem 定义
export interface TTSInfoItem {
  /** 组名 */
  groupName: string;
  /** 组唯一标志 */
  groupKey: string;
  /** 话术组列表 */
  groupList: GroupItem[];
}

// GroupItem 定义
export interface GroupItem {
  /** 播报名称 */
  ruleName: string;
  /**
   * 播报类型
   * 0 => 一段话
   * 1 => 一问一答
   * 2 => 朗读声明
   * 3 => mp4
   * 4 => mp3
   */
  type: number;
  /** 播报内容 */
  broadcast: string;
  /** 一问一答问题 */
  checkQuestion: string;
  /** 期望答案（逗号分割） */
  expectAnswer: string;
  /** 非期望答案（逗号分割） */
  unExpectAnswer: string;
  /** 朗读声明文本 */
  customerAnswer?: string;
}

export interface AiAbility {
  /** 控制人脸在框能力开关 */
  aiCheck?: boolean;
  /** 控制人脸对比能力开关 */
  aiCompare?: boolean;
  /** 控制语音识别能力开关 */
  aiASR?: boolean;
  /** 控制语音播报能力开关 */
  aiTTS?: boolean;
}

export interface QualityItems {
  /** 在框检测数目 */
  checkNum: number;
  /** 在框检测间隔(ms) */
  aiCheckInterval: number;
  /** 人脸比对检测间隔(ms) */
  aiComparelnterval: number;
  /** 一问一答规则对象 */
  QA: {
    /** 规则名 */
    itemName: string;
    /** 总分 */
    itemScore: number;
    /** 错误扣分 */
    itemDeduct: number;
  };
  /** 客户人脸比对规则对象 */
  CF: {
    /** 规则名 */
    itemName: string;
    /** 及格分 */
    itemBenchmark: number;
    /** 错误扣分 */
    itemDeduct: number;
    /** 总分 */
    itemScore: number;
  };
  /** 在框检测规则对象 */
  IB: {
    /** 规则名 */
    itemName: string;
    /** 总分 */
    itemScore: number;
    /** 错误扣分 */
    itemDeduct: number;
  };
  /** 总分规则对象 */
  PG: {
    /** 及格分 */
    itemBenchmark: number;
  };
}

export interface DeviceInfo {
  /** 摄像头信息 */
  camera?: Camera;
  /** 麦克风信息 */
  microphone?: Microphone;
  /** 智能播报参数 */
  ttsData?: TtsData;
}

export interface Camera {
  /** 摄像头索引 */
  index: number;
  /** 摄像头名称 */
  name: string;
}

export interface Microphone {
  /** 麦克风索引 */
  index: number;
  /** 麦克风名称 */
  name: string;
}

export interface TtsData {
  /** 语速 */
  speed: number;
  /** 音色类型：1为男声，2为女声 */
  tts_type: number;
}
