<script setup lang="ts">
import ClientApi from "@/api/client";
import { CommonQueryTableProps } from "@/components/CommonQueryTable/type";
import { ControlTypeEnum } from "@/enums/ControlTypeEnum";
import { useQuery } from "@tanstack/vue-query";
import { computed, reactive } from "vue";

// 查询参数
const queryParams = reactive({
  dicCode: "",
  pageNumber: 1,
  pageSize: 10,
});

// 使用 vue-query 获取字典列表数据
const {
  data: dictListData,
  isFetching,
  refetch,
} = useQuery({
  queryKey: ["witnessDictList", queryParams],
  queryFn: () => {
    return ClientApi.getWitnessDictList({
      dicCode: queryParams.dicCode,
      pageNumber: queryParams.pageNumber,
      pageSize: queryParams.pageSize,
    });
  },
});

// 使用 computed 属性替代 reactive 对象和 watch
const config = computed<CommonQueryTableProps>(() => ({
  formItem: [
    {
      controlType: ControlTypeEnum.INPUT,
      prop: "dicCode",
      label: "分类代码",
    },
  ],
  // buttons: [
  //   {
  //     label: "新增",
  //     type: "primary",
  //     onClick: () => {},
  //   },
  //   {
  //     label: "修改",
  //     type: "",
  //     onClick: () => {},
  //   },
  //   {
  //     label: "删除",
  //     type: "",
  //     onClick: () => {},
  //   },
  // ],
  columns: [
    {
      prop: "dicCode",
      label: "分类代码",
      type: "show",
      showOverflowTooltip: true,
    },
    {
      prop: "dicName",
      label: "分类名称",
      type: "show",
    },
    {
      prop: "ibm",
      label: "数字编码",
      type: "show",
    },
    {
      prop: "cbm",
      label: "字符编码",
      type: "show",
    },
    {
      prop: "note",
      label: "编码说明",
      type: "show",
      width: 350,
    },
  ],
  // 直接从查询结果中获取数据
  tableData: dictListData.value?.records || [],
  pagination: {
    pageSize: queryParams.pageSize,
    currentPage: queryParams.pageNumber,
    total: dictListData.value?.total || 0,
  },
}));

// 处理分页变化
const handlePaginationChange = (pagination: {
  currentPage: number;
  pageSize: number;
}) => {
  // 更新分页参数
  queryParams.pageNumber = pagination.currentPage;
  queryParams.pageSize = pagination.pageSize;
};

// 处理表单状态变化
const handleFormStateChange = ({ dicCode: tmp }: Record<string, any>) => {
  // 更新查询参数
  queryParams.dicCode = tmp;
  queryParams.pageNumber = 1;
  queryParams.pageSize = 10;
  // 触发查询
  refetch();
};
</script>

<template>
  <div>
    <CommonQueryTable
      :config="config"
      :loading="isFetching"
      @pagination-change="handlePaginationChange"
      @form-state-change="handleFormStateChange"
    />
  </div>
</template>
