<script setup lang="ts">
import ClientApi from "@/api/client";
import { CommonQueryTableProps } from "@/components/CommonQueryTable/type";
import { ControlTypeEnum } from "@/enums/ControlTypeEnum";
import { useQuery } from "@tanstack/vue-query";
import { ElMessage, ElMessageBox } from "element-plus";
import { computed, reactive, ref } from "vue";

// 查询参数
const queryParams = ref({});

// 表格数据查询
const { data, isFetching, refetch } = useQuery({
  queryKey: ["listProperties", queryParams],
  queryFn: () => ClientApi.listProperties(queryParams.value),
  select: (data) => data.records,
});

// 表单状态变更
const handleFormStateChange = (state: any) => {
  queryParams.value = state;
  refetch();
};

// 对话框相关状态
const dialogVisible = ref(false);
const dialogTitle = ref("新增系统参数");
const isEdit = ref(false);

// 表单数据和规则
const formData = reactive({
  code: "",
  name: "",
  value: "",
  description: "",
});

const formRules = {
  code: [
    { required: true, message: "请输入参数代码", trigger: "blur" },
    { max: 50, message: "长度不能超过50个字符", trigger: "blur" },
  ],
  name: [
    { required: true, message: "请输入参数名称", trigger: "blur" },
    { max: 50, message: "长度不能超过50个字符", trigger: "blur" },
  ],
  value: [{ required: true, message: "请输入参数值", trigger: "blur" }],
  description: [
    { max: 200, message: "长度不能超过200个字符", trigger: "blur" },
  ],
};

const formRef = ref();

// 打开新增对话框
const handleAdd = () => {
  isEdit.value = false;
  dialogTitle.value = "新增系统参数";
  resetForm();
  dialogVisible.value = true;
};

// 打开修改对话框
const handleEdit = (row: any, selectedRows: any[]) => {
  if (!row) {
    ElMessage.warning("请选择要修改的数据");
    return;
  }
  if (selectedRows.length > 1) {
    ElMessage.warning("只能选择一条数据进行修改");
    return;
  }
  isEdit.value = true;
  dialogTitle.value = "修改系统参数";
  resetForm();
  // 填充表单数据
  Object.assign(formData, selectedRows[0]);
  dialogVisible.value = true;
};

// 重置表单
const resetForm = () => {
  if (formRef.value) {
    formRef.value.resetFields();
  }
  Object.assign(formData, {
    code: "",
    name: "",
    value: "",
    description: "",
  });
};

// 提交表单
const submitForm = async () => {
  if (!formRef.value) return;

  await formRef.value.validate(async (valid: boolean) => {
    if (valid) {
      try {
        if (isEdit.value) {
          // 修改
          await ClientApi.updateProperty({
            code: formData.code,
            name: formData.name,
            value: formData.value,
            description: formData.description,
          });
          ElMessage.success("修改成功");
        } else {
          // 新增
          await ClientApi.addProperty({
            code: formData.code,
            name: formData.name,
            value: formData.value,
            description: formData.description,
          });
          ElMessage.success("新增成功");
        }
        dialogVisible.value = false;
        // 刷新表格数据
        refetch();
      } catch (error) {
        console.error("操作失败", error);
        ElMessage.error("操作失败");
      }
    }
  });
};

// 删除系统参数
const handleDelete = (row: any, selectedRows: any[]) => {
  if (!row) {
    ElMessage.warning("请选择要删除的数据");
    return;
  }

  const delName = selectedRows.length > 1 ? "选中的数据" : selectedRows[0].name;

  ElMessageBox.confirm(`确定要删除参数 ${delName} 吗？`, "删除确认", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  })
    .then(async () => {
      try {
        await ClientApi.deleteProperty({
          code: selectedRows.map((item) => item.code),
        });
        ElMessage.success("删除成功");
        refetch();
      } catch (error) {
        console.error("删除失败", error);
        ElMessage.error("删除失败");
      }
    })
    .catch(() => {
      // 取消删除操作
    });
};

// 表格配置
const config = computed<CommonQueryTableProps>(() => {
  return {
    formItem: [
      {
        controlType: ControlTypeEnum.INPUT,
        prop: "code",
        label: "参数代码",
      },
      {
        controlType: ControlTypeEnum.INPUT,
        prop: "name",
        label: "参数名称",
      },
    ],
    buttons: [
      {
        label: "新增",
        type: "primary",
        onClick: handleAdd,
      },
      {
        label: "修改",
        type: "",
        onClick: (selection: any, selectedRows: any[]) =>
          handleEdit(selection, selectedRows),
      },
      {
        label: "删除",
        type: "",
        onClick: (selection: any, selectedRows: any[]) =>
          handleDelete(selection, selectedRows),
      },
    ],
    columns: [
      {
        prop: "code",
        label: "参数代码",
        type: "show",
      },
      {
        prop: "name",
        label: "参数名称",
        type: "show",
      },
      {
        prop: "value",
        label: "参数值",
        type: "show",
      },
      {
        prop: "description",
        label: "参数说明",
        type: "show",
        width: 400,
      },
    ],
    tableData: data.value || [],
  };
});
</script>

<template>
  <div>
    <!-- 表格组件 -->
    <CommonQueryTable
      row-key="code"
      :config="config"
      :loading="isFetching"
      @form-state-change="handleFormStateChange"
    />

    <!-- 新增/修改对话框 -->
    <CommonDialog
      v-model="dialogVisible"
      :title="dialogTitle"
      width="500px"
      @confirm="submitForm"
    >
      <el-form
        ref="formRef"
        :model="formData"
        :rules="formRules"
        label-width="100px"
        class="mt-4"
      >
        <el-form-item label="参数代码" prop="code">
          <el-input
            v-model="formData.code"
            placeholder="请输入参数代码"
            :disabled="isEdit"
          />
        </el-form-item>
        <el-form-item label="参数名称" prop="name">
          <el-input v-model="formData.name" placeholder="请输入参数名称" />
        </el-form-item>
        <el-form-item label="参数值" prop="value">
          <el-input v-model="formData.value" placeholder="请输入参数值" />
        </el-form-item>
        <el-form-item label="参数说明" prop="description">
          <el-input
            v-model="formData.description"
            type="textarea"
            :rows="3"
            placeholder="请输入参数说明"
          />
        </el-form-item>
      </el-form>
    </CommonDialog>
  </div>
</template>

<style scoped>
.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 8px;
}
</style>
