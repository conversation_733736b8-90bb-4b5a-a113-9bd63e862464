<script lang="ts" setup>
import { WitnessAI, type QualityItem } from "@/api/witness-ai";
import { useQuery } from "@tanstack/vue-query";
import type { FormInstance, FormRules } from "element-plus";
import { ElMessage } from "element-plus";
import { computed, reactive, ref, watch } from "vue";

// 表单引用
const formRef = ref<FormInstance>();

// 直接使用API返回的数据结构，无需复杂映射
const formData = reactive<Record<string, QualityItem>>({});

// 获取质量检查数据
const { data: qualityCheckData, refetch } = useQuery({
  queryKey: ["queryAllQualityCheck"],
  queryFn: () => WitnessAI.queryAllQualityCheck(),
  select: (res) => res.records,
});

// 当API数据变化时更新表单数据
watch(
  () => qualityCheckData.value,
  (newData) => {
    if (newData && newData.length > 0) {
      // 清空现有数据
      Object.keys(formData).forEach((key) => delete formData[key]);

      // 将API数据添加到表单数据中，使用code作为键
      newData.forEach((item) => {
        formData[item.code] = { ...item };
      });
    }
  },
  { immediate: true }
);

// 获取客户回答检测项
const getCustomerAnswerItem = computed(() => formData["KHHDJC"] || null);

// 获取人脸质量检测项
const getFaceQualityItem = computed(() => formData["RLZLJC"] || null);

// 获取人脸对比检测项
const getFaceCompareItem = computed(() => formData["RLBD"] || null);

// 获取在框人数检测项
const getFrameCountItem = computed(() => formData["ZKRSJC"] || null);

// 计算所有项目总分之和
const totalProjectScore = computed(() => {
  return Object.values(formData).reduce(
    (sum, item) => sum + (item.totalScore || 0),
    0
  );
});

// 计算通过分数总和
const totalPassScore = computed(() => {
  return Object.values(formData).reduce(
    (sum, item) => sum + (item.passScore || 0),
    0
  );
});

// 验证扣分不超过总分
const validateDeduction = (
  rule: any,
  value: number,
  callback: any,
  item: QualityItem,
  fieldName: string
) => {
  if (value > item.totalScore) {
    callback(new Error(`${fieldName}不能超过项目总分(${item.totalScore})`));
  } else {
    callback();
  }
};

// 验证项目总分之和为100
const validateTotalScore = (rule: any, value: number, callback: any) => {
  if (totalProjectScore.value !== 100) {
    callback(
      new Error(
        `三个类别项目总分之和必须为100分，当前为${totalProjectScore.value}分`
      )
    );
  } else {
    callback();
  }
};

// 表单验证规则
const rules = reactive<FormRules>({
  // 客户回答检测
  "KHHDJC.totalScore": [
    { required: true, message: "请输入项目总分", trigger: "change" },
    { validator: validateTotalScore, trigger: "change" },
  ],
  "KHHDJC.onceDeductedScore": [
    { required: true, message: "请输入单次答错扣分", trigger: "change" },
    {
      validator: (rule, value, callback) =>
        validateDeduction(
          rule,
          value,
          callback,
          formData["KHHDJC"],
          "单次答错扣分"
        ),
      trigger: "change",
    },
  ],
  "KHHDJC.twiceDeductedScore": [
    { required: true, message: "请输入二次答错扣分", trigger: "change" },
    {
      validator: (rule, value, callback) =>
        validateDeduction(
          rule,
          value,
          callback,
          formData["KHHDJC"],
          "二次答错扣分"
        ),
      trigger: "change",
    },
  ],
  "KHHDJC.passScore": [
    { required: true, message: "请输入项目通过分", trigger: "change" },
    {
      validator: (rule, value, callback) =>
        validateDeduction(
          rule,
          value,
          callback,
          formData["KHHDJC"],
          "项目通过分"
        ),
      trigger: "change",
    },
  ],

  // 人脸质量检测
  "RLZLJC.totalScore": [
    { required: true, message: "请输入项目总分", trigger: "change" },
    { validator: validateTotalScore, trigger: "change" },
  ],
  "RLZLJC.value": [
    { required: true, message: "请输入人脸质量检测通过分", trigger: "change" },
  ],
  "RLZLJC.onceDeductedScore": [
    { required: true, message: "请输入单次不通过扣分", trigger: "change" },
    {
      validator: (rule, value, callback) =>
        validateDeduction(
          rule,
          value,
          callback,
          formData["RLZLJC"],
          "单次不通过扣分"
        ),
      trigger: "change",
    },
  ],
  "RLZLJC.passScore": [
    { required: true, message: "请输入项目通过分", trigger: "change" },
    {
      validator: (rule, value, callback) =>
        validateDeduction(
          rule,
          value,
          callback,
          formData["RLZLJC"],
          "项目通过分"
        ),
      trigger: "change",
    },
  ],

  // 人脸对比检测
  "RLBD.totalScore": [
    { required: true, message: "请输入项目总分", trigger: "change" },
    { validator: validateTotalScore, trigger: "change" },
  ],
  "RLBD.value": [
    { required: true, message: "请输入人脸比对检测通过分", trigger: "change" },
  ],
  "RLBD.onceDeductedScore": [
    { required: true, message: "请输入单次不通过扣分", trigger: "change" },
    {
      validator: (rule, value, callback) =>
        validateDeduction(
          rule,
          value,
          callback,
          formData["RLBD"],
          "单次不通过扣分"
        ),
      trigger: "change",
    },
  ],
  "RLBD.passScore": [
    { required: true, message: "请输入项目通过分", trigger: "change" },
    {
      validator: (rule, value, callback) =>
        validateDeduction(
          rule,
          value,
          callback,
          formData["RLBD"],
          "项目通过分"
        ),
      trigger: "change",
    },
  ],

  // 在框人数检测
  "ZKRSJC.totalScore": [
    { required: true, message: "请输入项目总分", trigger: "change" },
    { validator: validateTotalScore, trigger: "change" },
  ],
  "ZKRSJC.value": [
    { required: true, message: "请输入在框人数", trigger: "change" },
  ],
  "ZKRSJC.onceDeductedScore": [
    { required: true, message: "请输入单次不通过扣分", trigger: "change" },
    {
      validator: (rule, value, callback) =>
        validateDeduction(
          rule,
          value,
          callback,
          formData["ZKRSJC"],
          "单次不通过扣分"
        ),
      trigger: "change",
    },
  ],
  "ZKRSJC.passScore": [
    { required: true, message: "请输入项目通过分", trigger: "change" },
    {
      validator: (rule, value, callback) =>
        validateDeduction(
          rule,
          value,
          callback,
          formData["ZKRSJC"],
          "项目通过分"
        ),
      trigger: "change",
    },
  ],
});

// 当项目总分变化时，重新验证所有项目总分字段
watch(
  () => [
    formData["KHHDJC"]?.totalScore,
    formData["RLZLJC"]?.totalScore,
    formData["ZKRSJC"]?.totalScore,
    formData["RLBD"]?.totalScore,
  ],
  () => {
    if (formRef.value) {
      formRef.value.validateField([
        "KHHDJC.totalScore",
        "RLZLJC.totalScore",
        "ZKRSJC.totalScore",
        "RLBD.totalScore",
      ]);
    }
  }
);

// 保存表单
const submitForm = async () => {
  if (!formRef.value) return;

  try {
    await formRef.value.validate();

    // 准备提交到后端的数据 - 直接使用formData中的数据
    const updateData = Object.values(formData);

    // 提交到后端
    await WitnessAI.updateQualityCheck({ values: updateData });

    // 刷新数据
    refetch();

    // 提交成功
    ElMessage.success("保存成功");
  } catch (error) {
    console.error("表单验证或提交失败:", error);
    ElMessage.error("表单验证或提交失败，请检查输入");
  }
};
</script>

<template>
  <div class="p-16">
    <h2 class="text-xl font-bold mb-24 flex items-center justify-between">
      <div>量化参数（总分100）</div>
      <div class="flex items-center gap-4">
        <span v-if="totalProjectScore !== 100" class="text-red-500 text-sm">
          三个类别项目总分之和必须为100分，当前为{{ totalProjectScore }}分
        </span>
        <ElButton type="primary" @click="submitForm">保存</ElButton>
      </div>
    </h2>

    <ElForm
      ref="formRef"
      :model="formData"
      :rules="rules"
      label-width="150px"
      label-position="left"
    >
      <div class="flex flex-col xxl:flex-row gap-24">
        <!-- 客户回答检测 -->
        <ElCard
          v-if="getCustomerAnswerItem"
          shadow="hover"
          class="flex-1 mb-16"
        >
          <template #header>
            <div class="font-bold">{{ getCustomerAnswerItem.name }}</div>
          </template>
          <ElFormItem label="项目总分" prop="KHHDJC.totalScore">
            <el-input-number
              v-model="getCustomerAnswerItem.totalScore"
              :min="0"
              :max="100"
              :precision="0"
            />
          </ElFormItem>
          <ElFormItem label="单次答错扣分" prop="KHHDJC.onceDeductedScore">
            <el-input-number
              v-model="getCustomerAnswerItem.onceDeductedScore"
              :min="0"
              :max="getCustomerAnswerItem.totalScore"
              :precision="0"
            />
          </ElFormItem>
          <ElFormItem label="二次答错扣分" prop="KHHDJC.twiceDeductedScore">
            <el-input-number
              v-model="getCustomerAnswerItem.twiceDeductedScore"
              :min="0"
              :max="getCustomerAnswerItem.totalScore"
              :precision="0"
            />
          </ElFormItem>
          <ElFormItem label="项目通过分" prop="KHHDJC.passScore">
            <el-input-number
              v-model="getCustomerAnswerItem.passScore"
              :min="0"
              :max="getCustomerAnswerItem.totalScore"
              :precision="0"
            />
          </ElFormItem>
        </ElCard>

        <!-- 人脸质量检测 -->
        <ElCard v-if="getFaceQualityItem" shadow="hover" class="flex-1 mb-16">
          <template #header>
            <div class="font-bold">{{ getFaceQualityItem.name }}</div>
          </template>
          <ElFormItem label="项目总分" prop="RLZLJC.totalScore">
            <el-input-number
              v-model="getFaceQualityItem.totalScore"
              :min="0"
              :max="100"
              :precision="0"
            />
          </ElFormItem>
          <!-- <ElFormItem label="人脸质量检测通过分" prop="RLZLJC.value">
            <el-input-number
              v-model.number="getFaceCompareItem.value"
              :min="0"
              :max="100"
              :precision="0"
            />
          </ElFormItem> -->
          <ElFormItem label="单次不通过扣分" prop="RLZLJC.onceDeductedScore">
            <el-input-number
              v-model="getFaceQualityItem.onceDeductedScore"
              :min="0"
              :max="getFaceQualityItem.totalScore"
              :precision="0"
            />
          </ElFormItem>
          <ElFormItem label="项目通过分" prop="RLZLJC.passScore">
            <el-input-number
              v-model="getFaceQualityItem.passScore"
              :min="0"
              :max="getFaceQualityItem.totalScore"
              :precision="0"
            />
          </ElFormItem>
        </ElCard>

        <!-- 人脸比对检测 -->
        <ElCard v-if="getFaceCompareItem" shadow="hover" class="flex-1 mb-16">
          <template #header>
            <div class="font-bold">{{ getFaceCompareItem.name }}</div>
          </template>
          <ElFormItem label="项目总分" prop="RLBD.totalScore">
            <el-input-number
              v-model="getFaceCompareItem.totalScore"
              :min="0"
              :max="100"
              :precision="0"
            />
          </ElFormItem>
          <ElFormItem label="人脸比对通过分" prop="RLBD.value">
            <el-input-number
              v-model.number="getFaceCompareItem.value"
              :min="0"
              :max="100"
              :precision="0"
            />
          </ElFormItem>
          <ElFormItem label="单次不通过扣分" prop="RLBD.onceDeductedScore">
            <el-input-number
              v-model="getFaceCompareItem.onceDeductedScore"
              :min="0"
              :max="getFaceCompareItem.totalScore"
              :precision="0"
            />
          </ElFormItem>
          <ElFormItem label="项目通过分" prop="RLBD.passScore">
            <el-input-number
              v-model="getFaceCompareItem.passScore"
              :min="0"
              :max="getFaceCompareItem.totalScore"
              :precision="0"
            />
          </ElFormItem>
        </ElCard>

        <!-- 在框人数检测 -->
        <ElCard v-if="getFrameCountItem" shadow="hover" class="flex-1 mb-16">
          <template #header>
            <div class="font-bold">{{ getFrameCountItem.name }}</div>
          </template>
          <ElFormItem label="项目总分" prop="ZKRSJC.totalScore">
            <el-input-number
              v-model="getFrameCountItem.totalScore"
              :min="0"
              :max="100"
              :precision="0"
            />
          </ElFormItem>
          <ElFormItem label="在框人数" prop="ZKRSJC.value">
            <el-input-number
              v-model.number="getFrameCountItem.value"
              :min="0"
              :precision="0"
            />
          </ElFormItem>
          <ElFormItem label="单次不通过扣分" prop="ZKRSJC.onceDeductedScore">
            <el-input-number
              v-model="getFrameCountItem.onceDeductedScore"
              :min="0"
              :max="getFrameCountItem.totalScore"
              :precision="0"
            />
          </ElFormItem>
          <ElFormItem label="项目通过分" prop="ZKRSJC.passScore">
            <el-input-number
              v-model="getFrameCountItem.passScore"
              :min="0"
              :max="getFrameCountItem.totalScore"
              :precision="0"
            />
          </ElFormItem>
        </ElCard>
      </div>

      <div class="mt-16 text-gray-500 text-sm">
        系统判断双录之间通过分数为：{{ totalPassScore }}
        分（回答检测通过分＋人脸质量检测通过分＋在框人数检测通过分）
      </div>
    </ElForm>
  </div>
</template>
