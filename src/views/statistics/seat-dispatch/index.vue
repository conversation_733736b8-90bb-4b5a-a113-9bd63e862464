<script lang="ts" setup>
import ClientApi, { DictItem } from "@/api/client";
import RemoteWitnessAPI from "@/api/remote-witness";
import WitnessQueryAPI from "@/api/witness-query";
import { CommonQueryTableProps } from "@/components/CommonQueryTable/type";
import { useBusinessCode } from "@/composables/useBusinessCode";
import { useOrg } from "@/composables/useOrg";
import { ControlTypeEnum } from "@/enums/ControlTypeEnum";
import { getBizInfoByCode } from "@/utils/dict";
import { useQuery } from "@tanstack/vue-query";
import { dayjs } from "element-plus";
const queryParams = ref({});

const { org } = useOrg();

// 统一使用useQuery获取所有字典数据
const { data: dictMap } = useQuery({
  queryKey: ["/dictionary/all"],
  queryFn: async () => {
    // 获取YYB、YWDJ、KHXJ字典
    const dictRes = await RemoteWitnessAPI.getDict({ fldm: "YYB;YWDJ;KHXJ" });
    // 获取ZXJNZ字典
    const witnessDict = await ClientApi.getWitnessDict({ dicCode: "ZXJNZ" });

    // 合并所有字典数据
    return {
      ...dictRes.dictionary,
      ZXJNZ: witnessDict,
    };
  },
});

// 提取YYB字典，用于表格显示
const yybDict = computed(() => {
  return dictMap.value?.YYB || [];
});

const { data: khxjDict } = useQuery({
  queryKey: ["/witness/client/getWitnessDict"],
  queryFn: () =>
    ClientApi.getWitnessDict<{ dict: DictItem[] }>({
      dicCode: "KHXJ",
    }),
});

const { data, isFetching, refetch } = useQuery({
  queryKey: ["/witness/query/queue", queryParams],
  queryFn: async () => {
    const res = await WitnessQueryAPI.queryQueue(queryParams.value);
    return res.records.map((item) => ({
      ...item,
      witnessRequest: undefined,
      ...item.witnessRequest,
    }));
  },
});

const bizCodeList = useBusinessCode();

const seatQueryConfig = computed<CommonQueryTableProps>(() => {
  return {
    formItem: [
      {
        controlType: ControlTypeEnum.INPUT,
        prop: "id",
        label: "用户 ID",
      },
      {
        controlType: ControlTypeEnum.INPUT,
        prop: "customerName",
        label: "客户姓名",
      },
      {
        prop: "org",
        label: "客户营业部",
        controlType: ControlTypeEnum.SELECT2,
        fieldProps: {
          clearable: true,
          style: "width: 240px",
          filterable: true,
          placeholder: "请选择或搜索营业部",
        },
        options: org.value ?? [],
      },
    ],
    tableData: data.value || [],
    showSelection: false,
    columns: [
      {
        prop: "clientId",
        label: "用户ID",
        fixed: "left",
        width: "",
        type: "show",
      },
      {
        prop: "clientName",
        label: "客户姓名",
        width: "120",
        type: "show",
      },
      {
        prop: "org",
        label: "客户营业部",
        width: "240",
        type: "show",
        dict: yybDict.value,
      },
      {
        prop: "cusLevel",
        label: "客户等级",
        width: "",
        type: "show",
        dict: khxjDict.value,
      },
      {
        prop: "bizCode",
        label: "业务名称",
        type: "show",
        width: 100,
        formatter(row, column, cellValue) {
          return getBizInfoByCode(bizCodeList.value, cellValue, "bizName");
        },
      },
      {
        prop: "",
        label: "业务等级",
        type: "show",
        width: 120,
        formatter(row) {
          return getBizInfoByCode(bizCodeList.value, row.bizCode, "bizLevel");
        },
      },
      {
        prop: "joinTime",
        label: "发起时间",
        width: "160",
        type: "show",
        formatter: (row, column, cellValue) => {
          return dayjs(cellValue).format("YYYY-MM-DD HH:mm:ss");
        },
      },
      {
        prop: "",
        label: "等待时间",
        width: "",
        type: "show",
        formatter(row) {
          if (row.serveTime && row.joinTime) {
            const joinTimeDate = dayjs(row.joinTime);
            const now = row.serverTime ? dayjs(row.serverTime) : dayjs();
            const diffSeconds = now.diff(joinTimeDate, "second");
            return `${diffSeconds}秒`;
          } else {
            return "--";
          }
        },
      },
      {
        prop: "priority",
        label: "优先级规则",
        width: "120",
        type: "show",
      },
      {
        prop: "currentQueueInfo",
        label: "当前队列位置",
        width: "120",
        type: "show",
        formatter(row, column, cellValue, index) {
          return index + 1;
        },
      },
      {
        prop: "operate",
        label: "操作",
        width: "120",
        fixed: "right",
        type: "operate",
        buttons: [
          {
            label: "置顶",
            type: "text",
            size: "small",
            onClick: ({ row }) => {
              WitnessQueryAPI.topRequest({ requestId: row.requestId }).then(
                () => {
                  ElMessage.success("置顶成功");
                  refetch();
                }
              );
            },
          },
        ],
      },
    ],
  };
});

const handleFormStateChange = (state: any) => {
  console.log("🚀 ~ handleFormStateChange ~ state:", state);
  queryParams.value = state;
  refetch();
};

// 移除onMounted中的字典请求，已统一使用useQuery获取
</script>

<template>
  <div>
    <CommonQueryTable
      :config="seatQueryConfig"
      :dictMap="dictMap"
      :loading="isFetching"
      @form-state-change="handleFormStateChange"
    />
  </div>
</template>
