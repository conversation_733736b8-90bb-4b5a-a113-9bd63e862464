<script setup lang="tsx">
import <PERSON><PERSON><PERSON>, { AgentItem } from "@/api/agent";
import ClientA<PERSON>, { DictItem } from "@/api/client";
import RemoteWitnessAPI from "@/api/remote-witness";
import User<PERSON><PERSON> from "@/api/user";
import Witness<PERSON><PERSON>y<PERSON>I from "@/api/witness-query";
import CommonQueryTable from "@/components/CommonQueryTable/index.vue";
import {
  CommonQueryPagination,
  CommonQueryTableProps,
  DictMapType,
} from "@/components/CommonQueryTable/type";
import { useBusinessCode } from "@/composables/useBusinessCode";
import { useOrg } from "@/composables/useOrg";
import { ControlTypeEnum } from "@/enums/ControlTypeEnum";
import { HSNRItem } from "@/types/type";
import { getBizInfoByCode, getDictName } from "@/utils/dict";
import { keepPreviousData, useQuery } from "@tanstack/vue-query";
import { dayjs, ElButton, ElMessage } from "element-plus";
import { reactive } from "vue";

// 字典数据
const dictMap = reactive<DictMapType>({
  clientType: [
    { ibm: "白金卡客户", note: "白金卡客户" },
    { ibm: "贵宾客户", note: "贵宾客户" },
  ],
  businessType: [
    { ibm: "普通类", note: "普通类" },
    { ibm: "高级类", note: "高级类" },
  ],
  businessSubType: [{ ibm: "见证户", note: "见证户" }],
});

// 查询参数
const queryParams = reactive({
  clientId: "",
  clientName: "",
  clientType: "",
  businessId: "",
  startTime: "",
  endTime: "",
  currentPage: 1,
  pageSize: 10,
});

const { data: dict } = useQuery({
  queryKey: ["/test/queryDictionary"],
  queryFn: () => RemoteWitnessAPI.getDict({ fldm: "YYB" }),
  select: (res) => {
    return res.dictionary;
  },
});

const { data: khxjDict } = useQuery({
  queryKey: ["/witness/client/getWitnessDict"],
  queryFn: () =>
    ClientApi.getWitnessDict<{ dict: DictItem[] }>({
      dicCode: "KHXJ",
    }),
});

const bizCodeList = useBusinessCode();

const { data: allAgent } = useQuery({
  queryKey: ["/witness/agent/query"],
  queryFn: () =>
    AgentApi.query<AgentItem[]>({
      org: "-1",
      pageNumber: 1,
      pageSize: -1,
      techName: "",
    }),
});

const { data, isFetching, refetch } = useQuery({
  queryKey: ["taskQuery", queryParams],
  queryFn: () =>
    WitnessQueryAPI.queryFinishedWitness({
      ...queryParams,
      pageNumber: queryParams.currentPage,
    }),
  select: (response) => ({
    total: response.total || 0,
    records: response.records || [],
  }),
  placeholderData: keepPreviousData,
});

const { org } = useOrg();

const total = computed(() => data.value?.total ?? 0);
const tableData = computed(() => data.value?.records ?? []);

const showVideoPreview = ref(false);
const hsnr = ref<HSNRItem[]>([]);
const mediaUrl = ref("");
const coverUrl = ref("");
async function handlePreview(row: any) {
  const res2 = await ClientApi.getWitness(Number(row.id));
  const markerData = res2.witnessRequest.markerData
    ? JSON.parse(res2.witnessRequest.markerData)
    : {};

  hsnr.value = markerData.hsnr || [];
  // 定义轮询函数
  const pollMedia = async () => {
    const res = await UserAPI.searchMedia(row.id);
    console.log("🚀 ~ searchMedia ~ res:", res);

    // 更新当前值
    mediaUrl.value = res.mediaUrl || "";
    coverUrl.value = res.coverUrl || "";

    // 检查是否 mediaUrl 和 coverUrl 都有值
    if (res.mediaUrl && res.coverUrl) {
      // 如果都有值，结束轮询，更新 loading 状态

      // 只有在获取成功后才显示视频预览弹窗
      showVideoPreview.value = true;
      return true;
    }
    return false;
  };

  // 首次尝试获取
  let isComplete = await pollMedia();

  // 如果首次获取未成功，开始轮询
  if (!isComplete) {
    // 显示轮询开始的提示信息
    ElMessage({
      message: "视频合成中，请稍候...",
      type: "info",
      duration: 3000,
    });
    // 最多轮询 30 次，每次间隔 2 秒，总共最多等待 60 秒
    let attempts = 0;
    const maxAttempts = 30;
    const interval = 2000; // 2 秒

    const polling = setInterval(async () => {
      attempts++;

      try {
        isComplete = await pollMedia();

        // 如果获取成功或达到最大尝试次数，清除轮询
        if (isComplete || attempts >= maxAttempts) {
          clearInterval(polling);

          // 如果达到最大尝试次数但仍未获取到，也结束 loading 状态
          if (!isComplete && attempts >= maxAttempts) {
            coverUrl.value = "";
            console.warn("视频预览链接获取超时");
            ElMessage({
              message: "视频预览链接获取超时，请稍后再试",
              type: "warning",
              duration: 3000,
            });
          }
        }
      } catch (error) {
        console.error("轮询视频预览链接出错:", error);
        clearInterval(polling);
        coverUrl.value = "";
        ElMessage({
          message: "获取视频预览出错，请稍后再试",
          type: "error",
          duration: 3000,
        });
      }
    }, interval);
  }
}
// 查询配置
const taskQueryConfig = computed<CommonQueryTableProps>(() => ({
  // 表单项配置
  formItem: [
    {
      label: "用户ID",
      prop: "clientId",
      controlType: ControlTypeEnum.INPUT,
      fieldProps: {
        placeholder: "请输入用户ID",
        clearable: true,
      },
    },
    {
      label: "客户姓名",
      prop: "clientName",
      controlType: ControlTypeEnum.INPUT,
      fieldProps: {
        placeholder: "请输入客户姓名",
        clearable: true,
      },
    },
    {
      label: "客户营业部",
      prop: "org",
      controlType: ControlTypeEnum.SELECT2,
      fieldProps: {
        clearable: true,
        style: "width: 240px",
        filterable: true,
        placeholder: "请选择或搜索营业部",
      },
      options: org.value ?? [],
    },
    {
      label: "坐席ID",
      prop: "agentId",
      controlType: ControlTypeEnum.INPUT,
      fieldProps: {
        placeholder: "请输入坐席ID",
        clearable: true,
      },
    },
    {
      label: "坐席姓名",
      prop: "agentName",
      controlType: ControlTypeEnum.INPUT,
      fieldProps: {
        placeholder: "请输入坐席姓名",
        clearable: true,
      },
    },
  ],
  // 表格列配置
  columns: [
    {
      prop: "clientName",
      label: "客户姓名",
      type: "show",
      width: 260,
      showOverflowTooltip: true,
      fixed: "left",
    },
    {
      prop: "clientId",
      label: "用户ID",
      type: "show",
      width: 160,
      showOverflowTooltip: true,
    },
    {
      prop: "org",
      label: "客户营业部",
      type: "show",
      width: 180,
      showOverflowTooltip: true,
      dict: dict.value,
      // formatter(row, column, cellValue, index) {
      //   return getDictName(dict.value, "YYB", cellValue);
      // },
    },
    {
      prop: "cusLevel",
      label: "客户等级",
      type: "show",
      width: 120,
      dict: khxjDict.value,
    },
    {
      prop: "bizCode",
      label: "业务名称",
      type: "show",
      width: 100,
      formatter(row, column, cellValue) {
        return getBizInfoByCode(bizCodeList.value, cellValue, "bizName");
      },
    },
    {
      prop: "",
      label: "业务等级",
      type: "show",
      width: 120,
      formatter(row) {
        return getBizInfoByCode(bizCodeList.value, row.bizCode, "bizLevel");
      },
    },
    {
      prop: "joinTime",
      label: "呼叫时间",
      type: "show",
      width: 180,
      formatter(row, column, cellValue) {
        return dayjs(cellValue).format("YYYY-MM-DD HH:mm:ss");
      },
    },
    {
      prop: "serveAgentId",
      label: "坐席ID",
      type: "show",
      width: 120,
    },
    {
      prop: "",
      label: "坐席人员姓名",
      type: "show",
      width: 150,
      formatter(row) {
        console.log(allAgent.value, "###");
        return (
          allAgent.value?.find((item) => item?.agentId === row.serveAgentId)
            ?.agentName || "--"
        );
      },
    },
    {
      prop: "",
      label: "坐席营业部",
      type: "show",
      width: 180,
      showOverflowTooltip: true,
      formatter(row) {
        const org = allAgent.value?.find(
          (item) => item?.agentId === row.serveAgentId
        )?.org;
        return getDictName(dict.value, "YYB", org?.toString() ?? "");
      },
    },
    {
      prop: "serveTime",
      label: "见证时间",
      type: "show",
      width: 340,
      formatter(row, column, cellValue, index) {
        return (
          dayjs(cellValue).format("YYYY-MM-DD HH:mm:ss") +
          " - " +
          dayjs(row.serveEndTime).format("YYYY-MM-DD HH:mm:ss")
        );
      },
    },
    {
      prop: "result",
      label: "见证结果",
      type: "show",
      width: 120,
      fixed: "right",
      formatter(row, column, cellValue, index) {
        return cellValue === "true" ? "通过" : "不通过";
      },
    },
    {
      prop: "operate",
      label: "操作",
      type: "operate",
      fixed: "right",
      width: 120,
      buttons: [
        {
          component: ({ row }) => {
            if (row?.result === "true") {
              return (
                <ElButton
                  type="text"
                  class="text-blue-500 cursor-pointer"
                  onClick={() => handlePreview(row)}
                >
                  视频预览
                </ElButton>
              );
            } else {
              return (
                <ElButton
                  type="text"
                  class="cursor-pointer"
                  disabled={true}
                  onClick={() => {
                    ElMessage({
                      message: "见证不通过，暂无预览链接",
                      type: "warning",
                      duration: 2000,
                    });
                  }}
                >
                  视频预览
                </ElButton>
              );
            }
          },
        },
      ],
    },
  ],
  // 表格数据
  tableData: tableData.value,
  // 分页配置
  pagination: {
    currentPage: queryParams.currentPage, // 确保这里用的是 queryParams
    pageSize: queryParams.pageSize, // 确保这里用的是 queryParams
    total: total.value,
    layout: "total, sizes, prev, pager, next",
  },
}));

// 监听数据变化，更新表格数据和分页信息
// watch(data, (newData) => {
//   if (newData) {
//     taskQueryConfig.tableData = newData.records;
//     if (taskQueryConfig.pagination) {
//       taskQueryConfig.pagination.total = newData.total;
//     }
//   }
// });

// 表单状态变更处理
const handleFormStateChange = (formState: Record<string, any>) => {
  refetch();
  // 更新查询参数
  Object.assign(queryParams, formState);
  // 重置分页
  queryParams.currentPage = 1;
};

// 分页变更处理
const handlePaginationChange = ({
  currentPage,
  pageSize,
}: CommonQueryPagination) => {
  queryParams.currentPage = currentPage;
  queryParams.pageSize = pageSize;
  // 更新本地分页状态
  // if (taskQueryConfig.pagination) {
  //   taskQueryConfig.pagination.currentPage = currentPage;
  //   taskQueryConfig.pagination.pageSize = pageSize;
  // }
};
</script>
<template>
  <div>
    <CommonQueryTable
      :config="taskQueryConfig"
      :dictMap="dictMap"
      :loading="isFetching"
      @form-state-change="handleFormStateChange"
      @pagination-change="handlePaginationChange"
    />
    <VideoPreviewDialog
      v-model:visible="showVideoPreview"
      :video-url="mediaUrl"
      title="视频预览"
      :hsnr="hsnr"
    />
  </div>
</template>
