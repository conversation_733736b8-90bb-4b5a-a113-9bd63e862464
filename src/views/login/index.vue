<script setup lang="ts">
// 外部库和依赖
import { LocationQuery, useRoute } from "vue-router";

// 内部依赖
import { type LoginData } from "@/api/auth";
import { ThemeEnum } from "@/enums/ThemeEnum";
import router from "@/router";
import { useSettingsStore, useUserStore } from "@/store";

// 类型定义
import type { FormInstance } from "element-plus";

// 导入 login.scss 文件
import "@/styles/login.scss";

// 使用导入的依赖和库
const userStore = useUserStore();
const settingsStore = useSettingsStore();
const route = useRoute();
// 窗口高度
const { height } = useWindowSize();
// 国际化 Internationalization
const { t } = useI18n();

// 是否暗黑模式
const isDark = ref(settingsStore.theme === ThemeEnum.DARK);
// 是否显示 ICP 备案信息
const icpVisible = ref(true);
// 按钮 loading 状态
const loading = ref(false);
// 是否大写锁定
const isCapslock = ref(false);
// 验证码图片Base64字符串
const captchaBase64 = ref();
// 登录表单ref
const loginFormRef = ref<FormInstance>();

const loginData = ref<LoginData>({
  username: import.meta.env.DEV ? "admin" : "",
  password: import.meta.env.DEV ? "abc**123" : "",
  // captchaKey: "",
  // captchaCode: "",
});

const loginRules = computed(() => {
  return {
    username: [
      {
        required: true,
        trigger: "blur",
        message: t("login.message.username.required"),
      },
    ],
    password: [
      {
        required: true,
        trigger: "blur",
        message: t("login.message.password.required"),
      },
      {
        min: 6,
        message: t("login.message.password.min"),
        trigger: "blur",
      },
    ],
    // captchaCode: [
    //   {
    //     required: true,
    //     trigger: "blur",
    //     message: t("login.message.captchaCode.required"),
    //   },
    // ],
  };
});

// /** 获取验证码 */
// function getCaptcha() {
//   AuthAPI.getCaptcha().then((data) => {
//     loginData.value.captchaKey = data.captchaKey;
//     captchaBase64.value = data.captchaBase64;
//   });
// }

/** 登录表单提交 */
function handleLoginSubmit() {
  loginFormRef.value?.validate((valid: boolean) => {
    if (valid) {
      loading.value = true;
      userStore
        .login(loginData.value)
        .then(() => {
          const { path, queryParams } = parseRedirect();
          router.push({ path: path, query: queryParams });
        })
        .catch(() => {
          // getCaptcha();
        })
        .finally(() => {
          loading.value = false;
        });
    }
  });
}

/** 解析 redirect 字符串 为 path 和  queryParams */
function parseRedirect(): {
  path: string;
  queryParams: Record<string, string>;
} {
  const query: LocationQuery = route.query;
  const redirect = (query.redirect as string) ?? "/";

  const url = new URL(redirect, window.location.origin);
  const path = url.pathname;
  const queryParams: Record<string, string> = {};

  url.searchParams.forEach((value, key) => {
    queryParams[key] = value;
  });

  return { path, queryParams };
}

/** 主题切换 */
const toggleTheme = () => {
  const newTheme =
    settingsStore.theme === ThemeEnum.DARK ? ThemeEnum.LIGHT : ThemeEnum.DARK;
  settingsStore.changeTheme(newTheme);
};

/** 根据屏幕宽度切换设备模式 */
watchEffect(() => {
  if (height.value < 600) {
    icpVisible.value = false;
  } else {
    icpVisible.value = true;
  }
});

/** 检查输入大小写 */
function checkCapslock(event: KeyboardEvent) {
  // 防止浏览器密码自动填充时报错
  if (event instanceof KeyboardEvent) {
    isCapslock.value = event.getModifierState("CapsLock");
  }
}

onMounted(() => {
  // getCaptcha();
});
</script>

<template>
  <div class="login-container">
    <!-- 顶部工具栏 -->
    <!--    <div class="top-bar">-->
    <!--      <el-switch-->
    <!--        v-model="isDark"-->
    <!--        inline-prompt-->
    <!--        active-icon="Moon"-->
    <!--        inactive-icon="Sunny"-->
    <!--        @change="toggleTheme"-->
    <!--      />-->
    <!--      <lang-select class="ml-2 cursor-pointer" />-->
    <!--    </div>-->
    <div class="pos-fixed top-80 left-63 flex">
      <img
        src="@/assets/images/login-logo.png"
        width="198"
        height="55"
        alt="logo"
      />
      <div class="text-#fff text-30px pl-10 lh-75px">统一见证服务系统</div>
    </div>
    <div class="pos-fixed right-0 h-full w-40% bg-[#F0F4FA]">
      <!-- 登录表单 -->
      <el-card
        class="login-card pos-absolute top-1/2 left-1/2 transform -translate-y-1/2 -translate-x-1/2"
        shadow="never"
      >
        <!--        <div class="text-center relative">-->
        <!--          <h2>{{ defaultSettings.title }}</h2>-->
        <!--          <el-tag class="ml-2 absolute-rt">-->
        <!--            {{ defaultSettings.version }}-->
        <!--          </el-tag>-->
        <!--        </div>-->
        <div class="pos-relative h-80px">
          <div class="text-24px fw-400">欢迎登录</div>
          <span
            class="pos-absolute bottom-40 inline-block w-50px h-4px bg-blue-500 rounded-[2px]"
          ></span>
        </div>
        <el-form
          ref="loginFormRef"
          :model="loginData"
          :rules="loginRules"
          class="login-form"
        >
          <!-- 用户名 -->
          <el-form-item prop="username" style="margin-bottom: 30px">
            <div class="input-wrapper bg-white rounded-[2px]">
              <!--              <i-ep-user class="mx-2" />-->
              <svg-icon icon-class="login-user" size="30" class="ml-10 mr-10" />
              <el-input
                ref="username"
                v-model="loginData.username"
                :placeholder="`请输入${$t('login.username')}`"
                name="username"
                size="large"
                class="h-[48px]"
              />
            </div>
          </el-form-item>

          <!-- 密码 -->
          <el-tooltip
            :visible="isCapslock"
            :content="$t('login.capsLock')"
            placement="right"
          >
            <el-form-item prop="password" style="margin-bottom: 50px">
              <div
                class="input-wrapper bg-white rounded-[2px] border-blue overflow-hidden"
              >
                <!--                <i-ep-lock class="mx-2" />-->
                <svg-icon
                  icon-class="login-pwd"
                  size="30"
                  class="ml-10 mr-10"
                />
                <el-input
                  v-model="loginData.password"
                  :placeholder="`请输入${$t('login.password')}`"
                  type="password"
                  name="password"
                  size="large"
                  class="h-[48px] pr-10"
                  show-password
                  @keyup="checkCapslock"
                  @keyup.enter="handleLoginSubmit"
                />
              </div>
            </el-form-item>
          </el-tooltip>

          <!-- 验证码 -->
          <!--        <el-form-item prop="captchaCode">-->
          <!--          <div class="input-wrapper">-->
          <!--            <svg-icon icon-class="captcha" class="mx-2" />-->
          <!--            <el-input-->
          <!--              v-model="loginData.captchaCode"-->
          <!--              auto-complete="off"-->
          <!--              size="large"-->
          <!--              class="flex-1"-->
          <!--              :placeholder="$t('login.captchaCode')"-->
          <!--              @keyup.enter="handleLoginSubmit"-->
          <!--            />-->

          <!--            <el-image-->
          <!--              @click="getCaptcha"-->
          <!--              :src="captchaBase64"-->
          <!--              class="captcha-image"-->
          <!--            />-->
          <!--          </div>-->
          <!--        </el-form-item>-->

          <!-- 登录按钮 -->
          <el-button
            :loading="loading"
            type="primary"
            size="large"
            class="w-full bg-[#3A89FA]"
            @click.prevent="handleLoginSubmit"
          >
            {{ $t("login.login") }}
          </el-button>

          <!--        &lt;!&ndash; 账号密码提示 &ndash;&gt;-->
          <!--        <div class="mt-10 text-sm">-->
          <!--          <span>{{ $t("login.username") }}: admin</span>-->
          <!--          <span class="ml-4">{{ $t("login.password") }}: 123456</span>-->
          <!--        </div>-->
        </el-form>
      </el-card>
    </div>

    <!--    &lt;!&ndash; ICP备案 &ndash;&gt;-->
    <!--    <div class="icp-info" v-show="icpVisible">-->
    <!--      <p>-->
    <!--        Copyright © 2021 - 2024 youlai.tech All Rights Reserved. 有来技术-->
    <!--        版权所有-->
    <!--      </p>-->
    <!--      <p>皖ICP备20006496号-3</p>-->
    <!--    </div>-->
  </div>
</template>

<style lang="scss" scoped></style>
