<script setup lang="ts">
import { useSettingsStore } from "@/store";
import variables from "@/styles/variables.module.scss";

const router = useRouter();
type EntrancesType = {
  title: string;
  describe: string;
  icon: any;
  background: string;
  link: string;
};

const entrance = ref<EntrancesType[]>([
  {
    title: "智能见证",
    describe:
      "远程见证时采集过程中，通过智能化手段提升身份验证的准确性和见证视频的录制质量",
    icon: "cameras",
    background: "linear-gradient(-30deg,#8B4CFE, #BE92FF, #D9ADFF)",
    link: "/remote-witness/wait",
  },
  {
    title: "坐席监控大屏",
    describe:
      "实时展示坐席人员工作状态和关键业务数据的可视化工具，用于提高运营效率和管理决策",
    icon: "monitor-screen",
    background: "linear-gradient(-30deg, #368AFD, #80B9FF, #A4E2FF)",
    link: "",
  },
  // {
  //   title: "单点登录",
  //   describe: "允许用户登录多个相关但独立的系统应用",
  //   icon: "sso",
  //   background: "linear-gradient(-30deg, #36DAFD, #80FFF8, #A4F1FF)",
  //   link: "",
  // },
]);

const modules = ref<
  {
    title: string;
    items: {
      title: string;
      link: string;
    }[];
  }[]
>([
  {
    title: "见证管理",
    items: [
      { title: "坐席技能组", link: "/witness-config/skill-group" },
      { title: "见证人员管理", link: "/witness-config/staff-manage" },
      { title: "坐席派单规则", link: "/witness-config/seat-rules" },
      { title: "见证话术配置", link: "/witness-config/witness-script" },
      { title: "揭示信息", link: "/witness-config/reveal-info" },
    ],
  },
  {
    title: "查询统计",
    items: [
      { title: "坐席调度管理", link: "/statistics/seat-dispatch" },
      { title: "坐席任务查询", link: "/statistics/task-query" },
    ],
  },
  {
    title: "系统管理",
    items: [
      { title: "数据字典管理", link: "/system/dict-manage" },
      { title: "系统参数管理", link: "/system/system-param" },
      // { title: "操作日志管理", link: "/system/log-manage" },
    ],
  },
]);

const minHeight = computed(() => {
  if (useSettingsStore().tagsView) {
    return `calc(100vh - ${variables["navbar-height"]} - ${variables["tags-view-height"]} - 24px)`;
  } else {
    return `calc(100vh - ${variables["navbar-height"]} - 24px)`;
  }
});

const handleEntranceClick = (item: EntrancesType) => {
  if (item.link) {
    // router.push(item.link);
    window.open(`${import.meta.env.BASE_URL}#${item.link}`, "_blank");
  } else {
    ElMessage({
      message: `功能暂不开放`,
      type: "warning",
    });
  }
};

const handleModuleClick = (item: any) => {
  // if (!item.link) {
  //   ElMessage({
  //     message: `功能暂不开放`,
  //     type: "warning",
  //   });
  //   return;
  // }
  router.push(item.link);
};
</script>

<template>
  <div class="flex flex-col" :style="{ 'min-height': minHeight }">
    <div class="flex items-center gap-20">
      <div
        v-for="item in entrance"
        :key="item.title"
        class="bg-white rounded-10 shadow-sm px-22 py-25 box-border hover:shadow-lg transition-shadow duration-300 cursor-pointer w-430 h-210"
        @click="handleEntranceClick(item)"
      >
        <div class="flex items-start mb-16">
          <div
            class="rounded-full w-40 h-40 flex-center"
            :style="{
              background: item.background,
            }"
          >
            <svg-icon :icon-class="item.icon" size="24" color="white" />
          </div>
        </div>
        <h3 class="text-lg font-medium text-gray-900">{{ item.title }}</h3>
        <p class="text-gray-600 text-sm leading-relaxed">{{ item.describe }}</p>
      </div>
    </div>

    <div class="bg-white mt-26 rounded-8 p-22 flex flex-1 flex-col shadow-sm">
      <div
        v-for="module in modules"
        :key="module.title"
        class="mt-26 rounded-2 first:mt-0"
      >
        <div class="flex items-center gap-8 mb-18">
          <span class="text-14 text-#888">{{ module.title }}</span>
        </div>
        <div class="flex items-center gap-12">
          <div
            v-for="item in module.items"
            :key="item.title"
            class="p-12 rounded-8 bg-#F5F8FF hover:opacity-60 cursor-pointer transition-colors duration-300 min-w-230"
            @click="handleModuleClick(item)"
          >
            <div class="text-14 color-#333333">{{ item.title }}</div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
