<script setup lang="ts">
import CommonDialog from "@/components/CommonDialog/index.vue";

interface Props {
  modelValue: boolean;
  title: string;
}

interface Emits {
  (_e: "update:modelValue", _value: boolean): void;
}

// 话术变量数据结构
interface ScriptVariable {
  code: string; // 变量代码
  name: string; // 变量名称
}

defineProps<Props>();

const emit = defineEmits<Emits>();

// 话术变量数据
const scriptVariables: ScriptVariable[] = [
  { code: "{KHMC}", name: "客户名称" },
  { code: "{KHH}", name: "客户号" },
  { code: "{JZRYMC}", name: "见证人员名称" },
  { code: "{ZJBH}", name: "证件编号" },
  { code: "{CPDM}", name: "产品代码" },
  { code: "{ZJZRQ}", name: "证件截止日期" },
  { code: "{FQRXM}", name: "发起人姓名" },
  { code: "{FQR}", name: "发起人" },
  { code: "{FXCSNLMC}", name: "风险承受能力" },
  { code: "{YYBMC}", name: "营业部名称" },
  { code: "{CPTZQXMC}", name: "产品投资期限" },
  { code: "{KHTZPZMC}", name: "客户投资品种" },
  { code: "{ZJLBMC}", name: "证件类别" },
  { code: "{SLYWLBMC}", name: "双录业务类别" },
  { code: "{ZJQSRQ}", name: "证件起始日期" },
  { code: "{KHTZQXMC}", name: "客户投资期限" },
  { code: "{CPMC}", name: "产品名称" },
  { code: "{CPTZPZMC}", name: "产品投资品种" },
  { code: "{FSYYB}", name: "发生营业部代码" },
  { code: "{CPFXDJMC}", name: "产品风险等级" },
];

const handleClose = () => {
  emit("update:modelValue", false);
};
</script>

<template>
  <CommonDialog
    :model-value="modelValue"
    :title="title"
    width="800px"
    :hide-footer="true"
    @close="handleClose"
    @update:model-value="
      (value: boolean | undefined) => $emit('update:modelValue', value ?? false)
    "
  >
    <div class="p-20 max-h-400 overflow-y-auto">
      <div class="grid grid-cols-2 gap-x-40 gap-y-12">
        <div
          v-for="variable in scriptVariables"
          :key="variable.code"
          class="flex items-center text-base"
        >
          <span class="text-[#409eff] font-500 min-w-120">
            {{ variable.code }}
          </span>
          <span class="mx-10 text-[#909399]">-</span>
          <span class="text-[#303133]">{{ variable.name }}</span>
        </div>
      </div>
    </div>
  </CommonDialog>
</template>
