<script setup lang="ts">
import ClientApi from "@/api/client";
import CommonDialog from "@/components/CommonDialog/index.vue";

interface Props {
  modelValue: boolean;
  title: string;
  bizTypeOptions?: Array<{ label: string; value: number }>;
  // streamOptions?: Array<{ label: string; value: number }>;
  // streamFpsOptions?: Array<{ label: string; value: number }>;
  // streamResolutionOptions?: Array<{ label: string; value: number }>;
  initialData?: {
    bizCode: string;
    bizType: number | undefined;
    bizTypeName: string;
    // stream: number;
    // streamFps: number;
    // streamResolution: number;
    state: number;
  };
}

interface Emits {
  (_e: "update:modelValue", _value: boolean): void;
  (_e: "success"): void;
}

const props = withDefaults(defineProps<Props>(), {
  bizTypeOptions: () => [],
  // streamOptions: () => [],
  // streamFpsOptions: () => [],
  // streamResolutionOptions: () => [],
  initialData: () => ({
    bizCode: "",
    bizType: undefined,
    bizTypeName: "",
    // stream: 1,
    // streamFps: 1,
    // streamResolution: 1,
    state: 1,
  }),
});

const emit = defineEmits<Emits>();

const formRef = ref();
const formData = ref({
  bizCode: "",
  bizType: undefined as number | undefined,
  bizTypeName: "",
  // stream: 1,
  // streamFps: 1,
  // streamResolution: 1,
  state: 1,
});

const formRules = {
  bizCode: [{ required: true, message: "请输入业务编码", trigger: "change" }],
  bizType: [{ required: true, message: "请选择业务类型", trigger: "change" }],
  // streamFps: [{ required: true, message: "请选择帧率", trigger: "change" }],
  // streamResolution: [
  //   { required: true, message: "请选择分辨率", trigger: "change" },
  // ],
  state: [{ required: true, message: "请选择状态", trigger: "change" }],
};

// 监听对话框显示状态，重置表单数据
watch(
  () => props.modelValue,
  (newValue) => {
    if (newValue) {
      // 对话框打开时，初始化表单数据
      formData.value = { ...props.initialData };
    }
  }
);

const resetForm = () => {
  formRef.value?.resetFields();
  formData.value = { ...props.initialData };
};

const submitForm = async () => {
  // 表单校验
  const valid = await formRef.value.validate();
  if (valid) {
    try {
      // 表单校验通过，执行提交逻辑
      console.log("表单校验通过，提交数据：", formData.value);
      if (props.title === "新增") {
        await ClientApi.addScriptConfig({
          ...formData.value,
          bizType: formData.value.bizType as number,
          scriptConfig: "[]",
        });
      } else {
        await ClientApi.updateScriptConfig({
          ...formData.value,
          bizType: formData.value.bizType as number,
        });
      }
      // 关闭对话框
      emit("update:modelValue", false);
      // 通知父组件刷新数据
      emit("success");
    } catch (error) {
      console.error("提交失败", error);
    }
  }
};

const handleClose = () => {
  resetForm();
  emit("update:modelValue", false);
};

const handleBizTypeChange = (value: number) => {
  formData.value.bizTypeName =
    props.bizTypeOptions?.find((item) => item.value === value)?.label || "";
};
</script>

<template>
  <CommonDialog
    :model-value="modelValue"
    :title="title"
    width="500px"
    @close="handleClose"
    @confirm="submitForm"
    @update:model-value="
      (value: boolean | undefined) => $emit('update:modelValue', value ?? false)
    "
  >
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="120px"
      class="mt-4"
    >
      <template v-if="title === '新增'">
        <el-form-item label="业务代码" prop="bizCode">
          <el-input v-model="formData.bizCode" placeholder="请输入业务代码" />
        </el-form-item>
      </template>
      <template v-else>
        <el-form-item label="业务代码" prop="bizCode">
          <el-input
            v-model="formData.bizCode"
            placeholder="请输入业务代码"
            disabled
          />
        </el-form-item>
      </template>
      <el-form-item label="业务类别" prop="bizType">
        <el-select
          v-model="formData.bizType"
          placeholder="请输入业务类别"
          :disabled="title === '修改'"
          @change="handleBizTypeChange"
        >
          <el-option
            v-for="item in bizTypeOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item v-show="false" label="业务类别名称" prop="bizTypeName">
        <el-input v-model="formData.bizTypeName" placeholder="请输入业务别名" />
      </el-form-item>
      <!-- <el-form-item label="流配置" prop="stream">
        <el-select v-model="formData.stream" placeholder="请选择流配置">
          <el-option
            v-for="item in streamOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="合流帧率" prop="streamFps">
        <el-select v-model="formData.streamFps" placeholder="请选择合流帧率">
          <el-option
            v-for="item in streamFpsOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="合流分辨率" prop="streamResolution">
        <el-select
          v-model="formData.streamResolution"
          placeholder="请选择合流分辨率"
        >
          <el-option
            v-for="item in streamResolutionOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item> -->
      <el-form-item label="状态" prop="state">
        <el-radio-group v-model="formData.state">
          <el-radio :value="1">启用</el-radio>
          <el-radio :value="0">停用</el-radio>
        </el-radio-group>
      </el-form-item>
    </el-form>
  </CommonDialog>
</template>

<style scoped>
/* 组件特定样式 */
</style>
