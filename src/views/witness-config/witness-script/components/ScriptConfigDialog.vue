<script setup lang="ts">
import ClientApi from "@/api/client";
import { ArrowLeftBold } from "@element-plus/icons-vue";
import { ElMessage } from "element-plus";
import { computed, ref, watch } from "vue";

interface ScriptItem {
  id: string;
  nr: string; // 话术内容
  needAnswer: boolean; // 是否需要客户回答
  khhd: string; // 客户回答内容
  waitTime: number; // 等待客户回答时间（秒）
  playMode: "auto" | "real"; // 播放方式：自动播放/实时播放
  maleAudioUrl?: string; // 男声音频地址
  femaleAudioUrl?: string; // 女声音频地址
  isSpecial?: boolean; // 是否特殊话术
}

interface Props {
  modelValue: boolean;
  currentRow?: any;
  previewMode?: boolean;
}

interface Emits {
  (_e: "update:modelValue", _value: boolean): void;
  (_e: "success"): void;
}

const props = withDefaults(defineProps<Props>(), {
  previewMode: false,
});
const emit = defineEmits<Emits>();

// 话术列表
const scriptList = ref<ScriptItem[]>([]);
const specialScriptList = ref<ScriptItem[]>([]);

// 当前选中的话术
const selectedScript = ref<ScriptItem | null>(null);
const selectedScriptType = ref<"normal" | "special">("normal");

// 语音相关状态
const synthesizing = ref(false);
const selectedVoice = ref<"male" | "female">("male");
const showPreview = ref(false);

// 生成唯一ID
const generateId = () => Date.now() + Math.random();

// 创建新话术
const createNewScript = (): ScriptItem => ({
  id: generateId().toString(),
  nr: "",
  needAnswer: false,
  khhd: "无需",
  waitTime: 30,
  playMode: "auto",
});

// 加载话术数据
const loadScriptData = (scriptConfig: string) => {
  try {
    const result = JSON.parse(scriptConfig || "[]");
    if (Array.isArray(result)) {
      scriptList.value = result
        .filter((item) => !item.isSpecial)
        .map((item) => ({
          id: item.id || generateId().toString(),
          nr: item.nr || "",
          needAnswer: item.khhd !== "无需",
          khhd: item.khhd || "无需",
          waitTime: item.waitTime || 30,
          playMode: item.playMode || "auto",
          maleAudioUrl: item.maleAudioUrl,
          femaleAudioUrl: item.femaleAudioUrl,
        }));

      specialScriptList.value = result
        .filter((item) => item.isSpecial)
        .map((item) => ({
          id: item.id || generateId().toString(),
          nr: item.nr || "",
          needAnswer: item.khhd !== "无需",
          khhd: item.khhd || "无需",
          waitTime: item.waitTime || 30,
          playMode: item.playMode || "auto",
          maleAudioUrl: item.maleAudioUrl,
          femaleAudioUrl: item.femaleAudioUrl,
          isSpecial: true,
        }));
    }
  } catch (error) {
    console.error("解析数据失败", error);
    ElMessage.error("解析数据失败");
    scriptList.value = [];
    specialScriptList.value = [];
  }
};

// 监听弹窗打开，加载数据
watch(
  () => props.modelValue,
  (val) => {
    if (val && props.currentRow?.scriptConfig) {
      loadScriptData(props.currentRow.scriptConfig);
      // 默认选中第一个话术
      if (scriptList.value.length > 0) {
        selectScript(scriptList.value[0], "normal");
      } else if (specialScriptList.value.length > 0) {
        selectScript(specialScriptList.value[0], "special");
      }
    }
  }
);

// 选中话术
const selectScript = (script: ScriptItem, type: "normal" | "special") => {
  selectedScript.value = { ...script };
  selectedScriptType.value = type;
};

// 添加话术
const addScript = (isSpecial = false) => {
  const newScript = createNewScript();
  if (isSpecial) {
    newScript.isSpecial = true;
    specialScriptList.value.push(newScript);
    selectScript(newScript, "special");
  } else {
    scriptList.value.push(newScript);
    selectScript(newScript, "normal");
  }
};

// 删除话术
const deleteScript = (script: ScriptItem, type: "normal" | "special") => {
  const list = type === "normal" ? scriptList.value : specialScriptList.value;
  const index = list.findIndex((item) => item.id === script.id);
  if (index > -1) {
    list.splice(index, 1);

    // 如果删除的是当前选中的话术，选中下一个
    if (selectedScript.value?.id === script.id) {
      if (list.length > 0) {
        selectScript(list[Math.min(index, list.length - 1)], type);
      } else {
        // 如果当前类型没有话术了，尝试选中另一个类型的第一个
        const otherList =
          type === "normal" ? specialScriptList.value : scriptList.value;
        const otherType = type === "normal" ? "special" : "normal";
        if (otherList.length > 0) {
          selectScript(otherList[0], otherType);
        } else {
          selectedScript.value = null;
        }
      }
    }
  }
};

// 更新当前选中的话术
const updateSelectedScript = () => {
  if (!selectedScript.value) return;

  const list =
    selectedScriptType.value === "normal"
      ? scriptList.value
      : specialScriptList.value;
  const index = list.findIndex((item) => item.id === selectedScript.value!.id);
  if (index > -1) {
    list[index] = { ...selectedScript.value };
  }
};

// 监听选中话术的变化，同步更新到列表
watch(selectedScript, updateSelectedScript, { deep: true });

// 客户回答选项变化
const handleAnswerTypeChange = (val: string | number | boolean | undefined) => {
  const needAnswer = Boolean(val);
  if (selectedScript.value) {
    selectedScript.value.needAnswer = needAnswer;
    if (!needAnswer) {
      selectedScript.value.khhd = "无需";
    } else {
      selectedScript.value.khhd = "";
    }
  }
};

// 合成语音
const synthesizeAudio = async () => {
  if (!selectedScript.value?.nr.trim()) {
    ElMessage.warning("请先输入话术内容");
    return;
  }

  synthesizing.value = true;
  try {
    // TODO: 调用实际的语音合成接口
    // const response = await ClientApi.synthesizeAudio({
    //   text: selectedScript.value.nr,
    //   voiceType: 'both' // 同时合成男声和女声
    // });

    // 模拟接口调用
    await new Promise((resolve) => setTimeout(resolve, 2000));

    // 模拟返回的音频地址
    if (selectedScript.value) {
      selectedScript.value.maleAudioUrl =
        "http://music.163.com/song/media/outer/url?id=447925558.mp3";
      selectedScript.value.femaleAudioUrl =
        "https://www.cambridgeenglish.org/images/153149-movers-sample-listening-test-vol2.mp3";
    }

    ElMessage.success("语音合成成功");
  } catch (error) {
    console.error("语音合成失败", error);
    ElMessage.error("语音合成失败");
  } finally {
    synthesizing.value = false;
  }
};

// 下载音频
const downloadAudio = () => {
  if (!selectedScript.value) return;

  const audioUrl =
    selectedVoice.value === "male"
      ? selectedScript.value.maleAudioUrl
      : selectedScript.value.femaleAudioUrl;

  if (!audioUrl) {
    ElMessage.warning("请先合成语音");
    return;
  }

  // 创建隐藏的下载链接
  const link = document.createElement("a");
  link.href = audioUrl;
  link.download = `${selectedScript.value.nr.slice(0, 10)}_${selectedVoice.value === "male" ? "男声" : "女声"}.mp3`;
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
};

// 保存配置
const saveConfig = async () => {
  // 验证话术内容
  const allScripts = [...scriptList.value, ...specialScriptList.value];
  const isValid = allScripts.every((item) => item.nr.trim() !== "");
  if (!isValid) {
    ElMessage.warning("话术内容不能为空");
    return;
  }

  const hasEmptyAnswer = allScripts.some(
    (item) => item.needAnswer && !item.khhd.trim()
  );
  if (hasEmptyAnswer) {
    ElMessage.warning("需要客户回答的内容不能为空");
    return;
  }

  try {
    if (!props.currentRow?.bizType) {
      ElMessage.error("业务类型不能为空");
      return;
    }

    // 转换数据格式以兼容后端
    const formattedScripts = allScripts.map((item) => ({
      id: item.id,
      nr: item.nr,
      pagetime: item.waitTime, // 兼容老字段
      khhd: item.needAnswer ? item.khhd : "无需",
      waitTime: item.waitTime,
      needAnswer: item.needAnswer,
      playMode: item.playMode,
      maleAudioUrl: item.maleAudioUrl,
      femaleAudioUrl: item.femaleAudioUrl,
      isSpecial: item.isSpecial || false,
    }));

    await ClientApi.updateScriptConfig({
      bizType: props.currentRow.bizType,
      scriptConfig: JSON.stringify(formattedScripts),
    });

    ElMessage.success("保存成功");
    emit("update:modelValue", false);
    emit("success");
  } catch (error) {
    console.error("保存话术失败", error);
    ElMessage.error("保存话术失败");
  }
};

// 关闭弹窗
const closeDialog = () => {
  emit("update:modelValue", false);
  selectedScript.value = null;
  scriptList.value = [];
  specialScriptList.value = [];
};

// 预览功能
const togglePreview = () => {
  showPreview.value = !showPreview.value;
};

// 监听预览模式属性变化
watch(
  () => props.previewMode,
  (val) => {
    showPreview.value = val;
  }
);

// 计算属性
const allScriptsList = computed(() => [
  ...scriptList.value.map((item, index) => ({
    ...item,
    title: `话术${index + 1}`,
    type: "normal" as const,
  })),
  ...specialScriptList.value.map((item, index) => ({
    ...item,
    title: `特殊话术${index + 1}`,
    type: "special" as const,
  })),
]);

// 当前选中的音频URL
const currentAudioUrl = computed(() => {
  if (!selectedScript.value) return "";
  return selectedVoice.value === "male"
    ? selectedScript.value.maleAudioUrl || ""
    : selectedScript.value.femaleAudioUrl || "";
});

const hasAudioFiles = computed(() => {
  return (
    selectedScript.value?.maleAudioUrl && selectedScript.value?.femaleAudioUrl
  );
});

// 用于预览的所有话术列表
const allPreviewScripts = computed(() => [
  ...scriptList.value.map((s) => ({ ...s, isSpecial: false })),
  ...specialScriptList.value,
]);
</script>

<template>
  <CommonDialog
    :model-value="modelValue"
    :title="showPreview ? '话术预览' : '话术配置'"
    width="1200px"
    :hide-body-padding="true"
    :hide-footer="showPreview"
    @close="closeDialog"
    @confirm="saveConfig"
  >
    <div class="flex items-center justify-between px-16">
      <div class="flex items-center">
        <h3 class="text-lg font-medium">话术列表</h3>
        <div class="flex gap-8 ml-10">
          <el-button size="small" @click="addScript(false)">新增话术</el-button>
          <el-button size="small" @click="addScript(true)">
            新增特殊话术
          </el-button>
          <el-button size="small" @click="togglePreview">预览</el-button>
        </div>
      </div>
      <div class="flex items-center gap-10 text-xl">
        <el-icon><Setting /></el-icon>
        <el-icon><ChatLineSquare /></el-icon>
      </div>
    </div>
    <div v-if="!showPreview" class="flex h-600px border-t border-gray-200">
      <!-- 左栏：话术列表 -->
      <div class="w-200px border-r border-gray-200 p-16">
        <div class="space-y-8 max-h-500px overflow-y-auto">
          <div
            v-for="script in allScriptsList"
            :key="script.id"
            class="flex items-center justify-between px-12 rounded-4 py-4 rounded cursor-pointer hover:bg-gray-50"
            :class="{
              'bg-blue-50 text-primary ': selectedScript?.id === script.id,
            }"
            @click="selectScript(script, script.type)"
          >
            <span
              class="flex-1 truncate"
              :class="{ 'text-red-600': script.type === 'special' }"
            >
              {{ script.title }}
            </span>
            <div
              v-if="selectedScript?.id === script.id"
              class="cursor-pointer text-gray-400"
              @click="deleteScript(script, script.type)"
            >
              <svg-icon icon-class="delete" size="16px" />
            </div>
          </div>

          <div
            v-if="allScriptsList.length === 0"
            class="text-center py-8 text-gray-500"
          >
            暂无话术，请点击"新增话术"按钮添加
          </div>
        </div>
      </div>

      <!-- 右栏：话术详细配置 -->
      <div class="flex-1 p-16">
        <div v-if="selectedScript">
          <!-- 话术内容 -->
          <el-input
            v-model="selectedScript.nr"
            type="textarea"
            :rows="16"
            placeholder="请输入话术内容"
          />

          <!-- 客户回答选项 -->
          <div class="mt-6">
            <div
              class="flex items-center divide-x-1 divide-[#D6D6D6] bg-[#F8FAFB] h-32 border-1 border-[#d9d9d9] rounded-4"
            >
              <div
                class="flex-1 flex-center h-full cursor-pointer"
                :class="{
                  'bg-primary text-white': selectedScript.needAnswer === false,
                }"
                @click="
                  (selectedScript.needAnswer = false) &&
                  handleAnswerTypeChange(false)
                "
              >
                不需要客户回答
              </div>
              <div
                class="flex-1 flex-center h-full cursor-pointer"
                :class="{
                  'bg-primary text-white': selectedScript.needAnswer === true,
                }"
                @click="
                  (selectedScript.needAnswer = true) &&
                  handleAnswerTypeChange(true)
                "
              >
                需要客户回答
              </div>
            </div>

            <div
              v-if="selectedScript.needAnswer"
              class="flex items-center mt-8"
            >
              <el-input
                v-model="selectedScript.khhd"
                placeholder="请输入回答内容"
                class="flex-1"
              >
                <template #append>
                  <div class="flex-center h-full">
                    <input
                      v-model.number="selectedScript.waitTime"
                      type="number"
                      :min="5"
                      :max="60"
                      class="w-40 text-center bg-transparent border-none outline-none"
                    />
                    <span class="text-gray-700">等待客户回答时间（秒）</span>
                  </div>
                </template>
              </el-input>
            </div>
          </div>

          <!-- 播放方式选项 -->
          <div class="flex items-center mt-12">
            <div class="font-medium mr-8 flex items-end">
              <span class="text-red-500 text-base mr-4">*</span>
              <span>播放方式</span>
            </div>
            <div class="flex items-center gap-4">
              <el-select v-model="selectedScript.playMode" style="width: 250px">
                <el-option label="自动播放" value="auto" />
                <el-option label="实时播放" value="real" />
              </el-select>

              <el-button
                v-if="selectedScript.playMode === 'auto'"
                :loading="synthesizing"
                @click="synthesizeAudio"
              >
                {{ synthesizing ? "合成中..." : "合成" }}
              </el-button>
            </div>
          </div>

          <!-- 语音播放区域 -->
          <div v-if="selectedScript.playMode === 'auto' && hasAudioFiles">
            <div class="mt-12 flex items-center">
              <label class="block text-sm font-medium mr-8">语音播放</label>
              <div class="flex flex-1 items-center gap-12">
                <el-radio-group v-model="selectedVoice">
                  <el-radio value="male">男声</el-radio>
                  <el-radio value="female">女声</el-radio>
                </el-radio-group>

                <!-- 原生音频控件 -->
                <audio
                  v-if="currentAudioUrl"
                  :src="currentAudioUrl"
                  controls
                  class="flex-1"
                  preload="metadata"
                >
                  您的浏览器不支持音频播放
                </audio>

                <!-- 下载按钮 -->
                <el-button size="small" type="primary" @click="downloadAudio">
                  下载音频
                </el-button>
              </div>
            </div>
          </div>
        </div>

        <div
          v-else
          class="flex items-center justify-center h-full text-gray-500"
        >
          请在左侧选择话术进行配置
        </div>
      </div>
    </div>

    <!-- 预览模式 -->
    <div v-else class="bg-[#f5f5f5] p-5 h-600px">
      <div class="flex items-center cursor-pointer mb-4" @click="togglePreview">
        <el-icon><ArrowLeftBold /></el-icon>
        <span class="ml-2">返回配置</span>
      </div>

      <div class="bg-white rounded-lg p-6 h-full overflow-auto">
        <h3 class="text-lg font-medium mb-4">
          {{ currentRow?.bizTypeName }} - 话术预览
        </h3>

        <div class="space-y-4">
          <template
            v-for="(script, index) in allPreviewScripts"
            :key="script.id"
          >
            <!-- 客服话术 -->
            <div class="flex items-start gap-3">
              <div
                class="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center text-sm"
              >
                客服
              </div>
              <div class="flex-1">
                <div class="bg-blue-50 rounded-lg p-3">
                  <div
                    v-if="script.isSpecial"
                    class="text-red-500 text-sm font-medium mb-1"
                  >
                    [特殊话术{{
                      specialScriptList.findIndex((s) => s.id === script.id) +
                      1
                    }}]
                  </div>
                  <div v-else class="text-gray-500 text-sm mb-1">
                    话术{{
                      scriptList.findIndex((s) => s.id === script.id) + 1
                    }}
                  </div>
                  <p class="text-gray-800">{{ script.nr }}</p>

                  <!-- 播放控制 -->
                  <div class="mt-2 flex items-center gap-2">
                    <span class="text-sm text-gray-500">
                      播放方式：{{
                        script.playMode === "auto" ? "自动播放" : "实时播放"
                      }}
                    </span>
                    <el-button
                      v-if="script.playMode === 'auto'"
                      size="small"
                      type="primary"
                    >
                      播放语音
                    </el-button>
                  </div>
                </div>
              </div>
            </div>

            <!-- 客户回答 -->
            <div v-if="script.needAnswer" class="flex items-start gap-3 ml-10">
              <div
                class="w-10 h-10 bg-green-100 rounded-full flex items-center justify-center text-sm"
              >
                客户
              </div>
              <div class="flex-1">
                <div class="bg-green-50 rounded-lg p-3">
                  <p class="text-gray-800">{{ script.khhd }}</p>
                  <p class="text-sm text-gray-500 mt-1">
                    等待时间：{{ script.waitTime }}秒
                  </p>
                </div>
              </div>
            </div>
          </template>

          <div
            v-if="allPreviewScripts.length === 0"
            class="text-center py-8 text-gray-500"
          >
            暂无话术内容，请先添加话术
          </div>
        </div>
      </div>
    </div>
  </CommonDialog>
</template>

<style scoped>
.flex-center {
  @apply flex items-center justify-center;
}

.flex-y-center {
  @apply flex items-center;
}
</style>
