<script setup lang="ts">
import ClientApi, { DictItem, IWitnessScript } from "@/api/client";
import playIcon from "@/assets/play.png";
import { CommonQueryTableProps } from "@/components/CommonQueryTable/type";
import { ControlTypeEnum } from "@/enums/ControlTypeEnum";
import { getDictConvertOptionsByCode } from "@/utils/dict";
import { keepPreviousData, useQuery } from "@tanstack/vue-query";
import { dayjs, ElMessage } from "element-plus";

defineOptions({
  name: "WitnessScript",
});

const queryParams = ref<{
  state: number | "";
  currentPage: number;
  pageSize: number;
}>({
  state: "",
  currentPage: 1,
  pageSize: 10,
});
const { data, isFetching, refetch } = useQuery({
  queryKey: ["scriptConfig", queryParams],
  queryFn: async () => {
    const res = await ClientApi.getScriptConfigList({
      state: queryParams.value.state,
      pageNumber: queryParams.value.currentPage,
      pageSize: queryParams.value.pageSize,
    });
    return res;
  },
  select: (response) => ({
    total: response.total || 0,
    records: response.records || [],
  }),

  placeholderData: keepPreviousData,
});

const formRef = ref();
const showEditDialog = ref(false);
const dialogTitle = ref("修改");
const formData = ref<{
  bizCode: string;
  bizType: number | undefined;
  bizTypeName: string;
  // stream: number;
  // streamFps: number;
  // streamResolution: number;
  state: number;
}>({
  bizCode: "",
  bizType: undefined,
  bizTypeName: "",
  // stream: 1,
  // streamFps: 1,
  // streamResolution: 1,
  state: 1,
});

// 话术配置相关
const showScriptDialog = ref(false);
const showPreview = ref(false);
const currentRow = ref<IWitnessScript>();
const scriptList = ref<
  Array<{
    nr: string;
    pagetime: number;
    khhd: string;
  }>
>([]);

// 特殊话术配置相关
const specialScriptList = ref<
  Array<{
    nr: string;
    pagetime: number;
    khhd: string;
    isSpecial: true;
  }>
>([]);

// 话术变量对话框相关
const showScriptVarDialog = ref(false);

// 打开话术变量对话框
const openScriptVarDialog = () => {
  showScriptVarDialog.value = true;
};

// 加载话术数据
const loadScriptData = async (scriptConfig: string) => {
  try {
    const result = JSON.parse(scriptConfig);
    if (result && Array.isArray(result)) {
      scriptList.value = result.filter((item) => !item.isSpecial);
      specialScriptList.value = result.filter(
        (item) => item.isSpecial === true
      );
    } else {
      scriptList.value = [];
      specialScriptList.value = [];
    }
  } catch (error) {
    console.error("解析数据失败", error);
    ElMessage.error("解析数据失败");
    scriptList.value = [];
    specialScriptList.value = [];
  }
};

// 添加新话术
const addScript = () => {
  scriptList.value.push({
    nr: "",
    pagetime: 15,
    khhd: "无需",
  });
};

// 添加特殊话术
const addSpecialScript = () => {
  specialScriptList.value.push({
    nr: "",
    pagetime: 15,
    khhd: "无需",
    isSpecial: true,
  });
};

const previewScript = () => {
  showPreview.value = true;
};

// 删除话术
const deleteScript = (index: number) => {
  scriptList.value.splice(index, 1);
};

// 删除特殊话术
const deleteSpecialScript = (index: number) => {
  specialScriptList.value.splice(index, 1);
};

// 提交话术配置
const submitScriptConfig = async () => {
  // 验证话术内容
  const allScripts = [
    ...scriptList.value,
    ...specialScriptList.value.map((item) => ({ ...item, isSpecial: true })),
  ];
  const isValid = allScripts.every((item) => item.nr.trim() !== "");
  if (!isValid) {
    ElMessage.warning("话术内容不能为空");
    return;
  }

  const hasKhhdEmpty = scriptList.value.some((item) => item.khhd === "");
  if (hasKhhdEmpty) {
    ElMessage.warning("需要客户回答的内容不能为空");
    return;
  }

  try {
    if (!currentRow.value?.bizType) {
      ElMessage.error("业务类型不能为空");
      return;
    }

    await ClientApi.updateScriptConfig({
      bizType: currentRow.value?.bizType,
      scriptConfig: JSON.stringify(allScripts),
    });

    ElMessage.success("保存成功");
    showScriptDialog.value = false;
  } catch (error) {
    console.error("保存话术失败", error);
    ElMessage.error("保存话术失败");
  }
};
const formRules = {
  bizCode: [{ required: true, message: "请输入业务编码", trigger: "change" }],
  bizType: [{ required: true, message: "请选择业务类型", trigger: "change" }],
  streamFps: [{ required: true, message: "请选择帧率", trigger: "change" }],
  streamResolution: [
    { required: true, message: "请选择分辨率", trigger: "change" },
  ],
  state: [{ required: true, message: "请选择状态", trigger: "change" }],
};

const resetForm = () => {
  formRef.value.resetFields();
};

const submitForm = () => {
  // 表单校验
  formRef.value.validate((valid: boolean) => {
    if (valid) {
      // 表单校验通过，执行提交逻辑
      console.log("表单校验通过，提交数据：", formData.value);
      if (dialogTitle.value === "新增") {
        ClientApi.addScriptConfig({
          ...formData.value,
          bizType: formData.value.bizType as number,
          scriptConfig: "[]",
        });
      } else {
        ClientApi.updateScriptConfig({
          ...formData.value,
          bizType: formData.value.bizType as number,
        });
      }
      // 关闭对话框
      showEditDialog.value = false;
      // 刷新表格数据
      refetch();
    } else {
      // 表单校验不通过，提示错误信息
    }
  });
};

const handleDelete = (row: any, selectedRows: any[]) => {
  if (!row) {
    ElMessage.warning("请选择要删除的数据");
    return;
  }
  const delName =
    selectedRows.length > 1 ? "选中的数据" : selectedRows[0].bizTypeName;

  ElMessageBox.confirm(`确定要删除业务 ${delName} 吗？`, "删除确认", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  })
    .then(async () => {
      try {
        if (selectedRows.length > 1) {
          const deletePromises = selectedRows.map((row) =>
            ClientApi.deleteScriptConfig(row.bizType)
          );
          await Promise.all(deletePromises);
        } else {
          await ClientApi.deleteScriptConfig(row);
        }
        ElMessage.success("删除成功");
        refetch();
      } catch (error) {
        console.error("删除失败", error);
        ElMessage.error("删除失败");
      }
    })
    .catch(() => {
      // 取消删除操作
    });
};

const { data: dict } = useQuery({
  queryKey: ["getWitnessDict"],
  queryFn: () =>
    ClientApi.getWitnessDict<DictItem[]>({
      dicCodes: ["YWLBMC"],
    }),
});

const bizTypeOptions = computed(() => {
  return getDictConvertOptionsByCode(dict.value, "YWLBMC");
});

// const streamOptions = computed(() => {
//   return getDictConvertOptionsByCode(dict.value, "STREAM");
// });

// const streamFpsOptions = computed(() => {
//   return getDictConvertOptionsByCode(dict.value, "STREAM_FPS");
// });

// const streamResolutionOptions = computed(() => {
//   return getDictConvertOptionsByCode(dict.value, "STREAM_RESOLUTION");
// });

const config = computed<CommonQueryTableProps>(() => ({
  formItem: [
    {
      controlType: ControlTypeEnum.SELECT,
      fieldProps: {
        style: "width: 240px",
        clearable: true,
      },
      prop: "state",
      label: "状态",
      options: [
        { label: "启用", value: 1 },
        { label: "停用", value: 0 },
      ],
    },
  ],
  buttons: [
    {
      label: "新增",
      type: "primary",
      onClick: () => {
        dialogTitle.value = "新增";
        // 重置表单数据为初始值
        formData.value = {
          bizCode: "",
          bizType: undefined,
          bizTypeName: "",
          // stream: 1,
          // streamFps: 1,
          // streamResolution: 1,
          state: 1,
        };
        showEditDialog.value = true;
      },
    },
    {
      label: "删除",
      type: "",
      onClick: handleDelete,
    },
  ],
  columns: [
    {
      prop: "bizCode",
      label: "业务代码",
      type: "show",
    },
    {
      prop: "bizTypeName",
      label: "业务类别名称",
      type: "show",
      // width: 160,
    },
    // {
    //   prop: "stream",
    //   label: "流配置",
    //   type: "show",
    //   width: 240,
    //   formatter(row, column, cellValue) {
    //     return getDictNoteByIbm(dict.value, "STREAM", cellValue);
    //   },
    // },
    // {
    //   prop: "streamFps",
    //   label: "合流帧率",
    //   type: "show",
    //   width: 120,
    //   formatter(row, column, cellValue) {
    //     return getDictNoteByIbm(dict.value, "STREAM_FPS", cellValue);
    //   },
    // },
    // {
    //   prop: "streamResolution",
    //   label: "合流分辨率",
    //   type: "show",
    //   width: 120,
    //   formatter(row, column, cellValue) {
    //     return getDictNoteByIbm(dict.value, "STREAM_RESOLUTION", cellValue);
    //   },
    // },
    {
      prop: "state",
      label: "状态",
      type: "show",
      formatter(row, column, cellValue) {
        if (cellValue === 1) {
          return "启用";
        } else {
          return "停用";
        }
      },
    },
    {
      prop: "scriptModifyDate",
      label: "话术修改日期",
      type: "show",
      // width: 120,
      formatter(row, column, cellValue) {
        return dayjs(cellValue).format("YYYY-MM-DD");
      },
    },
    {
      prop: "scriptModifyTime",
      label: "话术修改时间",
      type: "show",
      // width: 120,
      formatter(row, column, cellValue) {
        let tmp = cellValue.length > 4 ? cellValue : cellValue + "00";
        return dayjs(row.scriptModifyDate + " " + tmp).format("HH:mm:ss");
      },
    },
    {
      prop: "operation",
      label: "操作",
      type: "operate",
      width: 200,
      fixed: "right",
      buttons: [
        {
          label: "修改",
          type: "text",
          size: "small",
          onClick: (scope) => {
            console.log("修改", scope.row);
            showEditDialog.value = true;
            dialogTitle.value = "修改";
            formData.value.bizCode = scope.row.bizCode; // Add bizCode assignment
            formData.value.bizType = scope.row.bizType;
            formData.value.bizTypeName = scope.row.bizTypeName;
            formData.value.state = scope.row.state;
          },
        },
        {
          label: "话术配置",
          type: "text",
          size: "small",
          onClick: (scope: { row: IWitnessScript }) => {
            console.log("话术配置", scope.row);
            // 打开话术配置弹窗
            currentRow.value = scope.row;
            showScriptDialog.value = true;
            if (!scope.row.scriptConfig) {
              scope.row.scriptConfig = "[]";
            }
            loadScriptData(scope.row.scriptConfig);
          },
        },
        {
          label: "话术预览",
          type: "text",
          size: "small",
          onClick: (scope: { row: IWitnessScript }) => {
            currentRow.value = scope.row;
            showScriptDialog.value = true;
            showPreview.value = true;
            if (!scope.row.scriptConfig) {
              scope.row.scriptConfig = "[]";
            }
            loadScriptData(scope.row.scriptConfig);
          },
        },
      ],
    },
  ],
  tableData: data.value?.records ?? [], // Use data.value directly
  pagination: {
    currentPage: queryParams.value.currentPage, // Use queryParams
    pageSize: queryParams.value.pageSize, // Use queryParams
    total: data.value?.total ?? 0, // Use data.value directly
  },
}));

// 处理分页变化
const handlePaginationChange = (pagination: {
  currentPage: number;
  pageSize: number;
}) => {
  // 更新查询参数
  queryParams.value.currentPage = pagination.currentPage;
  queryParams.value.pageSize = pagination.pageSize;
};
// 处理查询条件变化
const handleFormStateChange = ({ state: tmp }: Record<string, any>) => {
  // 更新查询参数
  queryParams.value.state = tmp;
  queryParams.value.currentPage = 1;
};

const handleBizTypeChange = (value: number) => {
  formData.value.bizTypeName =
    bizTypeOptions.value?.find((item) => item.value === value)?.label || "";
};
</script>

<template>
  <div>
    <CommonQueryTable
      :config="config"
      :dict-map="{}"
      row-key="bizType"
      :loading="isFetching"
      @pagination-change="handlePaginationChange"
      @form-state-change="handleFormStateChange"
    />
    <!-- 修改弹窗 -->
    <CommonDialog
      v-model="showEditDialog"
      :title="dialogTitle"
      width="500px"
      @close="resetForm"
      @confirm="submitForm"
    >
      <el-form
        ref="formRef"
        :model="formData"
        :rules="formRules"
        label-width="120px"
        class="mt-4"
      >
        <template v-if="dialogTitle === '新增'">
          <el-form-item label="业务代码" prop="bizCode">
            <el-input v-model="formData.bizCode" placeholder="请输入业务代码" />
          </el-form-item>
        </template>
        <!-- Add v-else for bizCode display in edit mode if needed -->
        <template v-else>
          <el-form-item label="业务代码" prop="bizCode">
            <el-input
              v-model="formData.bizCode"
              placeholder="请输入业务代码"
              disabled
            />
          </el-form-item>
        </template>
        <el-form-item label="业务类别" prop="bizType">
          <el-select
            v-model="formData.bizType"
            placeholder="请输入业务类别"
            :disabled="dialogTitle === '修改'"
            @change="handleBizTypeChange"
          >
            <el-option
              v-for="item in bizTypeOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item v-show="false" label="业务类别名称" prop="bizTypeName">
          <el-input
            v-model="formData.bizTypeName"
            placeholder="请输入业务别名"
          />
        </el-form-item>
        <el-form-item label="状态" prop="state">
          <el-radio-group v-model="formData.state">
            <el-radio :value="1">启用</el-radio>
            <el-radio :value="0">停用</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
    </CommonDialog>
    <!-- 话术配置 -->
    <CommonDialog
      v-model="showScriptDialog"
      :title="showPreview ? '话术预览' : '话术配置'"
      width="800px"
      :hide-body-padding="true"
      :hide-footer="showPreview"
      @close="
        scriptList = [];
        specialScriptList = [];
      "
      @confirm="submitScriptConfig"
    >
      <div v-if="!showPreview">
        <div
          class="flex items-center justify-between p-16 mb-16 border-b-1 border-[#eee]"
        >
          <div class="flex items-center gap-12">
            <div class="text-sm font-bold mr-12">
              {{ currentRow?.bizTypeName }}
            </div>
            <el-button size="small" @click="addScript">新增话术</el-button>
            <el-button size="small" @click="addSpecialScript">
              新增特殊话术
            </el-button>
            <el-button size="small" @click="previewScript">预览</el-button>
          </div>
          <div class="text-xl cursor-pointer" @click="openScriptVarDialog">
            <el-tooltip content="话术变量" placement="top">
              <el-icon><ChatLineSquare /></el-icon>
            </el-tooltip>
          </div>
        </div>

        <div class="max-h-56vh px-16 overflow-y-auto">
          <div class="space-y-4">
            <div
              v-for="(item, index) in scriptList"
              :key="index"
              class="p-4 rounded-md flex items-start gap-10"
            >
              <div class="mb-2 flex items-center w-76 justify-end">
                <span class="text-red-500 mr-1">*</span>
                <span class="text-gray-700">话术 {{ index + 1 }}</span>
              </div>

              <div class="mb-4 flex-1">
                <el-input
                  v-model="item.nr"
                  type="textarea"
                  :rows="3"
                  placeholder="请输入话术内容"
                  class="w-full"
                />

                <div
                  class="flex items-center divide-x-1 divide-[#D6D6D6] bg-[#F8FAFB] h-32 border-1 border-[#d9d9d9] border-t-none"
                >
                  <div
                    class="flex-1 flex-center h-full cursor-pointer"
                    :class="{ 'bg-primary text-white': item.khhd === '无需' }"
                    @click="item.khhd = '无需'"
                  >
                    不需要客户回答
                  </div>
                  <div
                    class="flex-1 flex-center h-full cursor-pointer"
                    :class="{ 'bg-primary text-white': item.khhd !== '无需' }"
                    @click="item.khhd = '是的'"
                  >
                    需要客户回答
                  </div>
                  <!-- <div class="flex-1 flex-center h-full">
                    <input
                      v-model.number="item.pagetime"
                      type="number"
                      :min="5"
                      :max="60"
                      class="w-60 text-center bg-transparent border-none outline-none"
                    />
                    <span class="text-gray-700">等待客户回答时间（秒）</span>
                  </div> -->
                </div>
                <div v-if="item.khhd !== '无需'" class="mt-2">
                  <el-input
                    v-model="item.khhd"
                    placeholder="请输入回答内容"
                    class="w-full"
                  />
                </div>
              </div>

              <div class="cursor-pointer" @click="deleteScript(index)">
                <svg-icon icon-class="delete" size="16px" />
              </div>
            </div>

            <div
              v-if="scriptList.length === 0"
              class="text-center py-8 text-gray-500"
            >
              暂无普通话术，请点击"新增话术"按钮添加
            </div>
          </div>

          <div class="space-y-4 mt-6">
            <div
              v-for="(item, index) in specialScriptList"
              :key="index"
              class="p-4 rounded-md flex items-start gap-10"
            >
              <div class="mb-2 flex items-center w-76">
                <span class="text-red-500 mr-1">*</span>
                <span class="text-orange">特殊话术 {{ index + 1 }}</span>
              </div>

              <div class="mb-4 flex-1">
                <el-input
                  v-model="item.nr"
                  type="textarea"
                  :rows="3"
                  placeholder="请输入话术内容"
                  class="w-full"
                />

                <div
                  class="flex items-center divide-x-1 divide-[#D6D6D6] bg-[#F8FAFB] h-32 border-1 border-[#d9d9d9] border-t-none"
                >
                  <div
                    class="flex-1 flex-center h-full cursor-pointer"
                    :class="{ 'bg-primary text-white': item.khhd === '无需' }"
                    @click="item.khhd = '无需'"
                  >
                    不需要客户回答
                  </div>
                  <div
                    class="flex-1 flex-center h-full cursor-pointer"
                    :class="{ 'bg-primary text-white': item.khhd !== '无需' }"
                    @click="item.khhd = '是的'"
                  >
                    需要客户回答
                  </div>
                  <!-- <div class="flex-1 flex-center h-full">
                    <input
                      v-model.number="item.pagetime"
                      type="number"
                      :min="5"
                      :max="60"
                      class="w-40 text-center bg-transparent border-none outline-none"
                    />
                    <span class="text-gray-700">等待客户回答时间（秒）</span>
                  </div> -->
                </div>
                <div v-if="item.khhd !== '无需'" class="mt-2">
                  <el-input
                    v-model="item.khhd"
                    placeholder="请输入回答内容"
                    class="w-full"
                  />
                </div>
              </div>

              <div class="cursor-pointer" @click="deleteSpecialScript(index)">
                <svg-icon icon-class="delete" size="16px" />
              </div>
            </div>

            <div
              v-if="specialScriptList.length === 0"
              class="text-center py-8 text-gray-500"
            >
              暂无特殊话术，请点击"新增特殊话术"按钮添加
            </div>
          </div>
        </div>
      </div>

      <div v-else class="bg-[#f5f5f5] p-20">
        <div class="flex-y-center cursor-pointer" @click="showPreview = false">
          <el-icon><ArrowLeftBold /></el-icon>
          <div>返回</div>
        </div>
        <div
          class="bg-white rounded-8 p-12 px-20 xxl:px-30 my-14 min-h-90% max-h-60vh overflow-auto"
        >
          <template
            v-for="(hs, index) in [
              ...scriptList.map((s) => ({ ...s, isSpecial: false })),
              ...specialScriptList,
            ]"
            :key="hs.nr + index"
          >
            <div class="flex min-h-60 justify-between items-center mt-16">
              <div class="flex">
                <div class="customer-image mt-9 flex-shrink-0"></div>
                <div class="triangle-left mt-25"></div>
                <div
                  class="flex-y-center p-7 rounded-8 min-h-60 fw-400 text-14 color-[hex-000000]/88 font-leading-22 text-left bg-[#E6F4FF]"
                >
                  <span v-if="hs.isSpecial" class="text-red-500 font-bold mr-1">
                    [特殊]
                  </span>
                  {{ hs.nr }}
                </div>
              </div>
              <div
                class="ml-7 cursor-pointer text-xs flex-center flex-col flex-shrink-0"
              >
                <img :src="playIcon" class="w-18 h-18 mb-5" />
                开始播放
              </div>
            </div>

            <div v-if="hs.khhd !== '无需'" class="flex-y-center mt-16">
              <div class="user-image"></div>
              <div class="triangle-left"></div>
              <div
                class="p-7 flex items-center flex-content-end min-h-60 rounded-8 min-w-117 fw-400 text-14 color-[hex-000000]/88 font-leading-22 text-left bg-[#F2F2F1]"
              >
                投资者:{{ hs.khhd }}
              </div>
            </div>
          </template>
        </div>
      </div>
    </CommonDialog>

    <!-- 话术变量对话框 -->
    <ScriptVar v-model="showScriptVarDialog" title="话术变量" />
  </div>
</template>

<style>
.customer-image {
  @apply w-45 h-45;

  background: url("@/assets/pic_hs.png") 100% no-repeat;
}

.user-image {
  @apply w-45 h-45;

  background: url("@/assets/pic_tzz.png") 100% no-repeat;
}

.triangle-left {
  width: 0;
  height: 0;
  border-top: 1.5rem solid transparent; /* 左边透明 */
  border-right: 1.5rem solid #e6f4ff; /* 三角形颜色 */
  border-bottom: 1.5rem solid transparent; /* 右边透明 */
  border-left: 1.5rem solid transparent; /* 右边透明 */
}

.triangle-right {
  width: 0;
  height: 0;
  border-top: 1.5rem solid transparent;
  border-right: 1.5rem solid transparent;
  border-bottom: 1.5rem solid transparent;
  border-left: 1.5rem solid #f2f2f1;
}
</style>
