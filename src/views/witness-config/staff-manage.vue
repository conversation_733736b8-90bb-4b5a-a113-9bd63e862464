<script lang="ts" setup>
import Agent<PERSON><PERSON>, { AgentItem } from "@/api/agent";
import RemoteWitnessAPI from "@/api/remote-witness";
import SeatApi from "@/api/seat";
import { useOrg } from "@/composables/useOrg";
import { ControlTypeEnum } from "@/enums/ControlTypeEnum";
import { useQuery } from "@tanstack/vue-query";
const tableData = ref<AgentItem[]>([]);
const { org } = useOrg();

const state = reactive({
  techCode: [],
});

const tableRef = ref();
const formRef = ref();

const rules = {
  techCode: [
    { required: true, message: "请选择技能组类别", trigger: "change" },
  ],
};

const { data: allTechSkill } = useQuery({
  queryKey: ["agentTechAll"],
  queryFn: () => SeatApi.queryAll({}),
  select: (res) => {
    return res?.agentTechAll?.map((item) => ({
      value: item?.techCode,
      label: item?.techName,
    }));
  },
});

const showBatchModel = ref(false);
const batchEditRows = ref<AgentItem[]>([]);
const staffManageConfig = computed(() => {
  return {
    formItem: [
      {
        controlType: ControlTypeEnum.INPUT,
        prop: "agentId",
        label: "柜员号",
      },
      {
        controlType: ControlTypeEnum.INPUT,
        prop: "agentName",
        label: "柜员名称",
      },
      {
        label: "客户营业部",
        prop: "org",
        controlType: ControlTypeEnum.SELECT2,
        fieldProps: {
          clearable: true,
          style: "width: 240px",
          filterable: true,
          placeholder: "请选择或搜索营业部",
        },
        options: org.value ?? [],
      },
      {
        controlType: ControlTypeEnum.INPUT,
        prop: "techs",
        label: "技能组编号",
        fieldProps: {
          clearable: true,
        },
      },
      {
        controlType: ControlTypeEnum.SELECT,
        prop: "techCode",
        label: "归属技能组",
        fieldProps: {
          clearable: true,
          style: "width: 240px",
          filterable: true,
          placeholder: "请选择或搜索技能组",
        },
        options: allTechSkill.value ?? [],
      },
    ],
    buttons: [
      {
        prop: "update",
        label: "批量修改",
        onClick: (item: string, selectedRows: any) => {
          if (!item) {
            ElMessage({
              message: "请勾选修改条目",
              type: "warning",
              duration: 2000,
            });
            return;
          }
          showBatchModel.value = true;
          batchEditRows.value = selectedRows;
        },
      },
    ],
    tableData: tableData.value,
    columns: [
      {
        prop: "agentId",
        label: "柜员号",
        type: "show",
      },
      {
        prop: "agentName",
        label: "柜员名称",
        type: "show",
      },
      {
        prop: "org",
        label: "营业部",
        minWidth: "200",
        type: "show",
        dict: dict.value,
      },
      {
        prop: "techs",
        label: "技能组编号",
        type: "show",
      },
      {
        showOverflowTooltip: true,
        prop: "techName",
        label: "归属技能组",
        minWidth: "300",
        type: "show",
      },
    ],
  };
});

const submitBatchEdit = () => {
  formRef.value.validate(async (valid: any) => {
    if (valid) {
      const res = await AgentApi.update({
        techCodes: state?.techCode?.join(";") ?? "",
        userIds: batchEditRows.value?.map((item) => item?.agentId).join(";"),
      });
      ElMessage({
        message: "修改成功",
        type: "success",
        duration: 2000,
      });
      showBatchModel.value = false;
      batchEditRows.value = [];
      search({ pageSize: 10, pageNumber: 1 });
      tableRef.value.clearSelection();
    } else {
      console.log("error submit!!");
      return false;
    }
  });
};

const { data: dict } = useQuery({
  queryKey: ["/test/queryDictionary"],
  queryFn: () => RemoteWitnessAPI.getDict({ fldm: "YYB" }),
  select: (res) => {
    return res.dictionary.YYB;
  },
});

const search = async (params: any) => {
  console.log("params", params);
  const queryAllRes = await AgentApi.query({
    ...params,
    org: params?.org ? params?.org : "-1",
    techName: params?.techCode
      ? allTechSkill.value?.find((item) => item?.value === params?.techCode)
          ?.label
      : "",
  });
  if (queryAllRes && queryAllRes?.records) {
    tableData.value = queryAllRes?.records ?? [];
  }
};

const handleFormStateChange = (params: any) => {
  params["pageSize"] = 10;
  params["pageNumber"] = 1;
  search(params);
};

onMounted(() => {
  search({ pageSize: 10, pageNumber: 1 });
});
</script>
<template>
  <div>
    <commonQueryTable
      ref="tableRef"
      :config="staffManageConfig"
      @form-state-change="handleFormStateChange"
    />
    <CommonDialog
      v-model="showBatchModel"
      :title="'批量修改'"
      width="500px"
      @close="formRef?.resetFields()"
      @confirm="submitBatchEdit"
    >
      <el-form ref="formRef" label-width="30%" :rules="rules" :model="state">
        <el-form-item label="技能组类别" prop="techCode">
          <el-select
            v-model="state['techCode']"
            placeholder="请选择技能组类别"
            :multiple="true"
          >
            <template v-for="item in allTechSkill ?? []" :key="item?.value">
              <el-option :label="item?.label" :value="item?.value" />
            </template>
          </el-select>
        </el-form-item>
      </el-form>
    </CommonDialog>
  </div>
</template>
