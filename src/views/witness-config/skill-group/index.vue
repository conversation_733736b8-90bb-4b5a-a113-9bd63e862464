<script lang="ts" setup>
import ClientApi from "@/api/client";
import RemoteWitnessAPI from "@/api/remote-witness";
import SeatApi from "@/api/seat";
import CommonForm, {
  CommonFormExpose,
} from "@/components/CommonForm/CommonForm.vue";
import { FormItems } from "@/components/CommonForm/type";
import { CommonQueryTableProps } from "@/components/CommonQueryTable/type";
import { FormItemTypeEnum } from "@/enums/FormItemTypeEnum";
const tableData = ref([]);
const state: any = reactive({});
const dictMap: any = ref({});
const dialogVisible = ref(false);
const dialogTitle = ref("新增");
const formRef = ref<CommonFormExpose>();
const currentOptType = ref("");
const onsubmit = ref();
const seatQueryConfig = computed<CommonQueryTableProps>(() => {
  return {
    buttons: [
      {
        prop: "add",
        label: "新增",
        type: "primary",
        onClick: () => {
          dialogTitle.value = "新增";
          onsubmit.value = add;
          showModel("add");
        },
      },
      {
        prop: "update",
        label: "修改",
        type: "",
        onClick: (item: string) => {
          if (!item) {
            ElMessage({
              message: "请勾选修改条目",
              type: "warning",
              duration: 2000,
            });
            return;
          } else if (item?.split(";").length > 1) {
            ElMessage({
              message: "仅支持勾选一条修改条目",
              type: "warning",
              duration: 2000,
            });
            return;
          }
          const currentFormVal: any =
            tableData.value?.find((i: { techCode: string }) => {
              return i?.techCode == item;
            }) || {};

          for (const key in currentFormVal) {
            state[key] = currentFormVal?.[key];
            if (key == "cusLevel" || key == "bizLevel") {
              state[key] = currentFormVal?.[key]?.split(";");
            }
          }
          dialogTitle.value = "修改";
          onsubmit.value = _update;
          showModel("update");
        },
      },
      {
        label: "删除",
        type: "",
        onClick: _delete,
      },
    ],
    rules: {
      // userid: [{ required: true, message: "请输入活动名称", trigger: "blur" }],
    },
    tableData: tableData.value,
    columns: [
      {
        prop: "techCode",
        label: "技能组编号",
        minWidth: "120",
        type: "show",
      },
      {
        prop: "techName",
        label: "技能组名称",
        minWidth: "180",
        type: "show",
      },
      {
        prop: "techType",
        label: "技能组类别",
        width: "160",
        showOverflowTooltip: true,
        dict: dictMap.value?.["ZXJNZ"],
        type: "show",
      },
      {
        prop: "cusLevel",
        label: "限办客户星级",
        // width: "320",
        minWidth: "320",
        showOverflowTooltip: true,
        dict: dictMap.value?.["KHXJ"],
        type: "show",
      },
      {
        prop: "bizLevel",
        label: "限办业务等级",
        width: "180",
        type: "show",
        dict: dictMap.value?.["BLDJ"],
      },
      // {
      //   prop: "ceshi",
      //   label: "测试",
      //   width: "100",
      //   type: "operate",
      //   fixed: "right",
      //   click: (scope: any) => {
      //     console.log(scope);
      //   },
      // },
    ],
  };
});

const dialogCloseCallBack = () => {
  resetForm();
  Object.keys(state).forEach((key) => {
    state[key] = undefined;
  });
};

// 表单项配置
const formItems = computed(
  () =>
    [
      {
        prop: "techCode",
        label: "技能组编号",
        valueType: FormItemTypeEnum.INPUT,
        span: 24,
        fieldProps: {
          disabled: currentOptType.value === "update",
          placeholder: "请输入技能组编号",
        },
      },
      {
        prop: "techName",
        label: "技能组名称",
        valueType: FormItemTypeEnum.INPUT,
        span: 24,
      },
      {
        prop: "techType",
        label: "技能组类别",
        valueType: FormItemTypeEnum.SELECT,
        span: 24,
        options: {
          dictCode: "ZXJNZ",
        },
      },
      {
        prop: "cusLevel",
        label: "限办客户星级",
        valueType: FormItemTypeEnum.SELECT,
        span: 24,
        fieldProps: {
          multiple: true,
        },
        options: {
          dictCode: "KHXJ",
        },
      },
      {
        prop: "bizLevel",
        label: "限办业务等级",
        valueType: FormItemTypeEnum.SELECT,
        span: 24,
        fieldProps: {
          multiple: true,
        },
        options: {
          dictCode: "BLDJ",
        },
      },
    ] as FormItems[]
);

// 表单验证规则
const rules = {
  techCode: [
    {
      required: true,
      message: "请输入技能组编号",
      trigger: "blur",
    },
  ],
  techName: [{ required: true, message: "请输入技能组名称", trigger: "blur" }],
  techType: [
    { required: true, message: "请选择技能组类别", trigger: "change" },
  ],
  cusLevel: [
    { required: true, message: "请选择限办客户星级", trigger: "change" },
  ],
  bizLevel: [
    { required: true, message: "请选择限办业务等级", trigger: "change" },
  ],
};

const valiForm = () => {
  formRef.value?.validate((valid: any) => {
    if (valid) {
      onsubmit.value?.();
    } else {
      console.log("error submit!!");
      return;
    }
  });
};

const resetForm = () => {
  formRef.value?.resetFields();
};

const add = async () => {
  const state = formRef.value?.formData ?? {};
  await SeatApi.add({
    techCode: state["techCode"],
    techName: state["techName"],
    techType: state["techType"],
    cusLevel: state["cusLevel"].join(";"),
    bizLevel: state["bizLevel"].join(";"),
  });

  dialogVisible.value = false;
  search();
  ElMessage({
    message: "添加成功",
    type: "success",
    duration: 2000,
  });
};

const _delete = async (item: any) => {
  if (!item) {
    ElMessage({
      message: "请勾选删除条目",
      type: "error",
      duration: 2000,
    });
    return;
  }

  await SeatApi.remove({ techCode: item });

  search();
  ElMessage({
    message: "删除成功",
    type: "success",
    duration: 2000,
  });
};

const search = async () => {
  const queryAllRes: any = await SeatApi.queryAll({});
  if (queryAllRes && queryAllRes?.agentTechAll) {
    tableData.value = queryAllRes?.agentTechAll;
  }
};

const showModel = (type: string) => {
  currentOptType.value = type;
  dialogVisible.value = true;
};

const _update = async (_item: any) => {
  const state = formRef.value?.formData ?? {};
  await SeatApi.update({
    techCode: state["techCode"],
    techName: state["techName"],
    techType: state["techType"],
    cusLevel: state["cusLevel"].join(";"),
    bizLevel: state["bizLevel"].join(";"),
  });

  dialogVisible.value = false;
  search();
  ElMessage({
    message: "修改成功成功",
    type: "success",
    duration: 2000,
  });
};

onMounted(async () => {
  await RemoteWitnessAPI.getDict({ fldm: "BLDJ;KHXJ" }).then((res) => {
    dictMap.value = res.dictionary;
  });
  const dictRes = await ClientApi.getWitnessDict({ dicCode: "ZXJNZ" });
  dictMap.value["ZXJNZ"] = dictRes;
  search();
});
</script>
<template>
  <div>
    <div class="setQuery">
      <div class="containter">
        <CommonQueryTable
          row-key="techCode"
          :config="seatQueryConfig"
          :dictMap="dictMap"
        />
      </div>
    </div>
    <div>
      <CommonDialog
        v-model="dialogVisible"
        width="40%"
        :title="dialogTitle"
        @close="dialogCloseCallBack"
        @confirm="valiForm"
      >
        <div class="main">
          <CommonForm
            ref="formRef"
            :form-items="formItems"
            :rules="rules"
            :dict-object-array="dictMap"
            :initial-values="state"
            label-width="30%"
            :show-buttons="false"
          />
        </div>
      </CommonDialog>
    </div>
  </div>
</template>
