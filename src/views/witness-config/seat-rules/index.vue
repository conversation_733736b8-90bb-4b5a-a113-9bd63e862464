<script lang="ts" setup>
import ClientApi, { DictItem } from "@/api/client";
import DispatchAPI, { DispatchRuleItem } from "@/api/dispatch";
import { useQuery } from "@tanstack/vue-query";
import { ElMessage } from "element-plus";
import { computed, reactive, ref, watch } from "vue";

// 表单数据 - 用于存储用户编辑的值
const formData = reactive<Record<string, any>>({});

// 获取所有规则
const { data: rulesData, isLoading } = useQuery({
  queryKey: ["dispatch", "queryAllRules"],
  queryFn: () => DispatchAPI.queryAllRules(),
  select: (res) => {
    return res.allRules;
  },
});

// 根据规则数据收集所需的字典代码
const dictCodes = computed(() => {
  if (!rulesData.value) return [];

  // 从规则中收集所有需要的字典代码
  return rulesData.value
    .filter(
      (rule: DispatchRuleItem) => rule.ruleDictType === 1 && rule.ruleDictKey
    )
    .map((rule: DispatchRuleItem) => rule.ruleDictKey)
    .filter((value, index, self) => self.indexOf(value) === index); // 去重
});

// 获取字典数据（对于ruleDictType=1的情况）
const { data: dictData, isLoading: isDictLoading } = useQuery({
  queryKey: ["getWitnessDict", dictCodes],
  queryFn: () =>
    ClientApi.getWitnessDict<{ dict: DictItem[] }>({
      dicCodes: dictCodes.value,
    }),
  enabled: computed(() => dictCodes.value.length > 0),
  select: (res) => {
    // 将字典数据转换为以 dicCode 为键的对象
    if (!res) return {};

    const dictMap: Record<string, any> = {};
    res.forEach((item: any) => {
      if (!dictMap[item.dicCode]) {
        dictMap[item.dicCode] = {};
      }
      dictMap[item.dicCode][item.cbm] = item.note;
    });
    return dictMap;
  },
});

// 根据规则类型和优先级对规则进行分组
const taskRules = computed(() => {
  if (!rulesData.value) return [];
  return rulesData.value
    .filter((rule: DispatchRuleItem) => rule.ruleType === 1)
    .sort(
      (a: DispatchRuleItem, b: DispatchRuleItem) => a.priority - b.priority
    );
});

const staffRules = computed(() => {
  if (!rulesData.value) return [];
  return rulesData.value
    .filter((rule: DispatchRuleItem) => rule.ruleType === 2)
    .sort(
      (a: DispatchRuleItem, b: DispatchRuleItem) => a.priority - b.priority
    );
});

// 初始化表单数据
watch(
  [rulesData, dictData],
  ([newData]) => {
    if (!newData) return;

    // 初始化表单数据
    newData.forEach((rule: DispatchRuleItem) => {
      // 解析ruleValue (如果已设置)
      if (rule.ruleValue) {
        if (rule.htmlElementType === 3) {
          // 多选框，需要拆分为数组
          formData[rule.ruleCode] = rule.ruleValue.split("|");
        } else if (rule.htmlElementType === 5) {
          // 文本标签，保存原始值，显示时做转换
          formData[rule.ruleCode] = rule.ruleValue;
        } else {
          // 其他控件类型，直接使用值
          formData[rule.ruleCode] = rule.ruleValue;
        }
      } else if (rule.ruleDictType === 2) {
        // 对于ruleDictType为2的情况，解析第一个选项作为默认值
        const options = parseOptions(rule.ruleDictKey);
        if (options.length > 0) {
          if ([2, 4].includes(rule.htmlElementType)) {
            // 单选框或下拉框
            formData[rule.ruleCode] = options[0].value;
          } else if (rule.htmlElementType === 3) {
            // 多选框
            formData[rule.ruleCode] = [];
          }
        }
      }
    });
  },
  { immediate: true }
);

// 解析选项 (当ruleDictType=2时)
function parseOptions(dictKey: string) {
  if (!dictKey) return [];

  return dictKey.split(";").map((item) => {
    const parts = item.split("|");
    return {
      key: parts[0],
      label: parts[1] || parts[0],
      value: parts[0], //1|十分钟|10;2|二十分钟|20;3|三十分钟|30 1才是字典值
    };
  });
}

// 获取指定规则的选项
function getOptions(rule: DispatchRuleItem) {
  if (rule.ruleDictType === 1) {
    // 从字典获取
    if (dictData.value && dictData.value[rule.ruleDictKey]) {
      return Object.entries(dictData.value[rule.ruleDictKey]).map(
        ([key, value]) => ({
          key,
          label: String(value),
          value: key,
        })
      );
    }
    return [];
  } else if (rule.ruleDictType === 2) {
    // 直接从ruleDictKey解析
    return parseOptions(rule.ruleDictKey);
  }
  return [];
}

// 将多选值转换为文本显示
function getDictLabelsText(rule: DispatchRuleItem) {
  if (!rule.ruleValue) return "";

  // 将值分割为数组
  const values = rule.ruleValue.split("|");
  let result = [];

  if (
    rule.ruleDictType === 1 &&
    dictData.value &&
    dictData.value[rule.ruleDictKey]
  ) {
    // 从字典中获取标签
    for (const val of values) {
      const label = dictData.value[rule.ruleDictKey][val];
      if (label) result.push(label);
    }
  } else if (rule.ruleDictType === 2) {
    // 从规则选项中获取标签
    const options = parseOptions(rule.ruleDictKey);
    for (const val of values) {
      const option = options.find((opt) => opt.value === val);
      if (option) result.push(option.label);
    }
  }

  // 返回用 > 连接的字符串
  return result.join(" > ");
}

// 编辑状态
const isEditing = ref(false);

// 备份原始数据（用于取消操作）
const originalData = ref<Record<string, any>>({});

// 开始编辑
const startEditing = () => {
  // 深拷贝当前数据作为备份
  originalData.value = JSON.parse(JSON.stringify(formData));
  isEditing.value = true;
};

// 取消编辑
const cancelEditing = () => {
  // 恢复原始数据
  Object.assign(formData, originalData.value);
  isEditing.value = false;
};

// 保存编辑
const saveChanges = () => {
  // 在提交前将多选框数组转换回分隔符字符串
  const submitData = { ...formData };

  if (rulesData.value) {
    rulesData.value.forEach((rule: DispatchRuleItem) => {
      if (
        rule.htmlElementType === 3 &&
        Array.isArray(submitData[rule.ruleCode])
      ) {
        // 将数组转换为用|分隔的字符串
        submitData[rule.ruleCode] = submitData[rule.ruleCode].join("|");
      }
    });
  }

  console.log("提交数据", submitData);

  // 收集需要更新的规则
  const updatedRules =
    rulesData.value
      ?.filter(
        (rule: DispatchRuleItem) =>
          submitData[rule.ruleCode] !== rule.ruleValue &&
          submitData[rule.ruleCode]
      )
      .map((rule: DispatchRuleItem) => ({
        ruleCode: rule.ruleCode,
        ruleValue: submitData[rule.ruleCode],
      })) || [];

  if (updatedRules.length > 0) {
    DispatchAPI.updateRules({
      values: updatedRules,
    })
      .then(() => {
        ElMessage.success("保存成功");
        isEditing.value = false;
      })
      .catch((error) => {
        ElMessage.error(`保存失败: ${error.message || "未知错误"}`);
      });
  } else {
    ElMessage.success("没有规则需要更新");
    isEditing.value = false;
  }
};
</script>

<template>
  <div class="">
    <!-- 标题和按钮区域 -->
    <div
      class="flex justify-between items-center mb-20 p-14 border-b-1 border-[#eee]"
    >
      <div></div>
      <div>
        <template v-if="!isEditing">
          <el-button type="primary" @click="startEditing">编辑</el-button>
        </template>
        <template v-else>
          <el-button class="mr-2" @click="cancelEditing">取消</el-button>
          <el-button type="primary" @click="saveChanges">保存</el-button>
        </template>
      </div>
    </div>

    <div v-if="isLoading || isDictLoading" class="flex-center p-20">
      <el-skeleton :rows="5" animated />
    </div>

    <div v-else class="m-20">
      <!-- 见证任务监控规则 -->
      <div class="mb-6">
        <div>
          <div class="text-lg font-bold">
            见证任务监控规则
            <span class="text-sm text-[#aaa]">
              (优先级数字越小，优先级越高）
            </span>
          </div>
        </div>

        <div
          class="grid grid-cols-1 lg:grid-cols-2 gap-20 border-solid border-[#eee] border-1 py-20 px-16 my-20 rounded-8"
        >
          <template v-for="rule in taskRules" :key="rule.ruleCode">
            <div class="flex items-center">
              <div
                class="bg-[#EBF3FF] text-[#9AAAC1] text-sm rounded-4 px-4 flex-center py-2 mr-8"
              >
                优先级{{ rule.priority }}
              </div>
              <div class="flex-1 flex items-center">
                <div class="flex-shrink-0 w-175 text-sm text-[#333]">
                  {{ rule.ruleName }}:
                </div>

                <!-- 根据控件类型动态渲染 -->
                <!-- 1:文本框 -->
                <template v-if="rule.htmlElementType === 1">
                  <el-input
                    v-model="formData[rule.ruleCode]"
                    :disabled="!isEditing"
                    class="w-48"
                  />
                </template>

                <!-- 2:单选框 -->
                <template v-else-if="rule.htmlElementType === 2">
                  <el-radio-group
                    v-model="formData[rule.ruleCode]"
                    :disabled="!isEditing"
                    class="ml-4"
                  >
                    <el-radio
                      v-for="option in getOptions(rule)"
                      :key="option.key"
                      :label="option.value"
                    >
                      {{ option.label }}
                    </el-radio>
                  </el-radio-group>
                </template>

                <!-- 3:多选框 (使用select多选模式) -->
                <template v-else-if="rule.htmlElementType === 3">
                  <el-select
                    v-model="formData[rule.ruleCode]"
                    :disabled="!isEditing"
                    multiple
                    class="w-64"
                  >
                    <el-option
                      v-for="option in getOptions(rule)"
                      :key="option.key"
                      :label="option.label"
                      :value="option.value"
                    />
                  </el-select>
                </template>

                <!-- 4:下拉框 -->
                <template v-else-if="rule.htmlElementType === 4">
                  <el-select
                    v-model="formData[rule.ruleCode]"
                    :disabled="!isEditing"
                    class="w-48"
                  >
                    <el-option
                      v-for="option in getOptions(rule)"
                      :key="option.key"
                      :label="option.label"
                      :value="option.value"
                    />
                  </el-select>
                </template>

                <!-- 5:文本标签 (只读文本) -->
                <template v-else-if="rule.htmlElementType === 5">
                  <div class="text-[#464A55] text-sm ml-4">
                    {{
                      formData[rule.ruleCode]
                        ? getDictLabelsText(rule)
                        : rule.defaultRule === 1
                          ? "(默认规则)"
                          : ""
                    }}
                  </div>
                </template>
              </div>
            </div>
          </template>
        </div>
      </div>

      <!-- 见证人员监控规则 -->
      <div>
        <div class="flex items-center">
          <div class="text-lg font-bold">
            见证人员监控规则
            <span class="text-sm text-[#aaa]">
              (优先级数字越小，优先级越高）
            </span>
          </div>
        </div>

        <div
          class="grid grid-cols-1 lg:grid-cols-2 gap-20 my-20 border-solid border-[#eee] border-1 py-20 px-16 rounded-8"
        >
          <template v-for="rule in staffRules" :key="rule.ruleCode">
            <div class="flex items-center">
              <div
                class="bg-[#EBF3FF] text-[#9AAAC1] text-sm rounded-4 px-4 flex-center py-2 mr-8"
              >
                优先级{{ rule.priority }}
              </div>
              <div class="flex-1 flex items-center">
                <div class="flex-shrink-0 w-175 text-sm text-[#333]">
                  {{ rule.ruleName }}:
                </div>

                <!-- 根据控件类型动态渲染 -->
                <!-- 1:文本框 -->
                <template v-if="rule.htmlElementType === 1">
                  <el-input
                    v-model="formData[rule.ruleCode]"
                    :disabled="!isEditing"
                    class="w-48"
                  />
                </template>

                <!-- 2:单选框 -->
                <template v-else-if="rule.htmlElementType === 2">
                  <el-radio-group
                    v-model="formData[rule.ruleCode]"
                    :disabled="!isEditing"
                    class="ml-4"
                  >
                    <el-radio
                      v-for="option in getOptions(rule)"
                      :key="option.key"
                      :label="option.value"
                    >
                      {{ option.label }}
                    </el-radio>
                  </el-radio-group>
                </template>

                <!-- 3:多选框 (使用select多选模式) -->
                <template v-else-if="rule.htmlElementType === 3">
                  <el-select
                    v-model="formData[rule.ruleCode]"
                    :disabled="!isEditing"
                    multiple
                    class="w-64"
                  >
                    <el-option
                      v-for="option in getOptions(rule)"
                      :key="option.key"
                      :label="option.label"
                      :value="option.value"
                    />
                  </el-select>
                </template>

                <!-- 4:下拉框 -->
                <template v-else-if="rule.htmlElementType === 4">
                  <el-select
                    v-model="formData[rule.ruleCode]"
                    :disabled="!isEditing"
                    class="w-48"
                  >
                    <el-option
                      v-for="option in getOptions(rule)"
                      :key="option.key"
                      :label="option.label"
                      :value="option.value"
                    />
                  </el-select>
                </template>

                <!-- 5:文本标签 (只读文本) -->
                <template v-else-if="rule.htmlElementType === 5">
                  <div class="text-[#464A55] text-sm ml-4">
                    {{
                      formData[rule.ruleCode]
                        ? getDictLabelsText(rule)
                        : rule.defaultRule === 1
                          ? "(默认规则)"
                          : ""
                    }}
                  </div>
                </template>
              </div>
            </div>
          </template>
        </div>
      </div>
    </div>
  </div>
</template>
