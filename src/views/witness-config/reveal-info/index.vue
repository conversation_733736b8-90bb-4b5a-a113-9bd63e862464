<script setup lang="ts">
import ClientApi, { ICoworkScript } from "@/api/client";
import CommonQueryTable from "@/components/CommonQueryTable/index.vue";
import { CommonQueryTableProps } from "@/components/CommonQueryTable/type";
import { useDict } from "@/composables/useDict";
import { usePaginationHandler } from "@/composables/usePaginationHandler";
import { ControlTypeEnum } from "@/enums/ControlTypeEnum";
import { useQuery } from "@tanstack/vue-query";
import { ElMessage, ElMessageBox } from "element-plus";
import "element-plus/es/components/message-box/style/index.mjs";
import "element-plus/es/components/message/style/index.mjs";
import RevealInfoDialog from "./components/RevealInfoDialog.vue";

defineOptions({
  name: "RevealInfo",
});

const queryParams = ref<{
  businessCode: string;
  initiateChannel: number | "";
  currentPage: number;
  pageSize: number;
}>({
  businessCode: "",
  initiateChannel: "",
  currentPage: 1,
  pageSize: 10,
});

const { data, isFetching, refetch } = useQuery({
  queryKey: ["revealInfoList", queryParams],
  queryFn: async () => {
    const res = await ClientApi.getCoworkScriptList({
      businessCode: queryParams.value.businessCode || undefined,
      initiateChannel: queryParams.value.initiateChannel || undefined,
      pageNumber: queryParams.value.currentPage,
      pageSize: queryParams.value.pageSize,
    });

    return {
      total: res.total,
      records: res.records.map((item: ICoworkScript) => ({
        businessCode: item.businessCode,
        initiateChannel: item.initiateChannel,
        channelName: item.channelName || "",
        script: item.script,
      })),
    };
  },
});

// 使用分页处理工具
const { handleCreateSuccess, handleUpdateSuccess, handleDeleteSuccess } =
  usePaginationHandler(
    queryParams,
    refetch,
    { createStrategy: "last" } // 新增后跳转到第一页
  );

// 创建稳定的分页对象引用
const paginationRef = computed(() => ({
  currentPage: queryParams.value.currentPage,
  pageSize: queryParams.value.pageSize,
  total: data.value?.total ?? 0,
}));

const showEditDialog = ref(false);
const dialogTitle = ref("修改");
const currentFormData = ref({
  businessCode: "",
  initiateChannel: 0 as number,
  script: "",
});

const handleDelete = (value: any, selectedRows: any[]) => {
  if (!value) {
    ElMessage.warning("请选择要删除的数据");
    return;
  }
  const delName =
    selectedRows.length > 1
      ? "选中的数据"
      : `业务代码：${selectedRows[0].businessCode},发起渠道：${
          selectedRows[0].initiateChannel
        }`;

  ElMessageBox.confirm(`确定要删除 ${delName} 的揭示信息吗？`, "删除确认", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  })
    .then(async () => {
      try {
        if (selectedRows.length > 1) {
          // 批量删除
          const deletePromises = selectedRows.map((row) =>
            ClientApi.deleteCoworkScript(row.businessCode)
          );
          await Promise.all(deletePromises);
          // 使用分页处理工具处理批量删除
          handleDeleteSuccess(
            {
              currentPage: queryParams.value.currentPage,
              pageSize: queryParams.value.pageSize,
              total: data.value?.total ?? 0,
            },
            selectedRows.length
          );
        } else {
          // 单个删除
          await ClientApi.deleteCoworkScript(value);
          // 使用分页处理工具处理单个删除
          handleDeleteSuccess({
            currentPage: queryParams.value.currentPage,
            pageSize: queryParams.value.pageSize,
            total: data.value?.total ?? 0,
          });
        }
        ElMessage.success("删除成功");
      } catch (error) {
        console.error("删除失败", error);
        ElMessage.error("删除失败");
      }
    })
    .catch(() => {
      // 取消删除操作
    });
};

// 获取发起渠道字典数据
const { dictMap } = useDict("FQQD");

// 将字典数据转换为选项格式
const channelOptions = computed(() => {
  const fqqdDict = dictMap.value?.FQQD;
  if (!fqqdDict) return [];

  return fqqdDict.map((item) => ({
    label: item.note,
    value: parseInt(item.ibm),
  }));
});

// 将字典数据转换为CommonQueryTable期望的格式
const channelDict = computed(() => {
  const fqqdDict = dictMap.value?.FQQD;
  if (!fqqdDict) return [];

  return fqqdDict.map((item) => ({
    ibm: item.ibm, // 保持字符串类型，因为translate函数会转换为字符串进行匹配
    note: item.note,
  }));
});

const config = computed<CommonQueryTableProps>(() => ({
  formItem: [
    {
      controlType: ControlTypeEnum.INPUT,
      fieldProps: {
        style: "width: 240px",
        clearable: true,
        placeholder: "请输入业务代码",
      },
      prop: "businessCode",
      label: "业务代码",
    },
    {
      controlType: ControlTypeEnum.SELECT,
      fieldProps: {
        style: "width: 240px",
        clearable: true,
      },
      prop: "initiateChannel",
      label: "发起渠道",
      options: channelOptions.value,
    },
  ],
  buttons: [
    {
      label: "新增",
      type: "primary",
      onClick: () => {
        dialogTitle.value = "新增";
        // 获取第一个渠道选项作为默认值，而不是0
        const defaultChannel =
          channelOptions.value.length > 0 ? channelOptions.value[0].value : 1;
        currentFormData.value = {
          businessCode: "",
          initiateChannel: defaultChannel,
          script: "",
        };
        showEditDialog.value = true;
      },
    },
    {
      label: "删除",
      type: "",
      onClick: handleDelete,
    },
  ],
  columns: [
    {
      prop: "businessCode",
      label: "业务代码",
      type: "show",
      width: 120,
    },
    {
      prop: "initiateChannel",
      label: "发起渠道",
      type: "show",
      width: 120,
      dict: channelDict.value,
    },
    {
      prop: "script",
      label: "揭示信息",
      type: "show",
      showOverflowTooltip: true,
    },
    {
      prop: "operation",
      label: "操作",
      type: "operate",
      width: 120,
      fixed: "right",
      buttons: [
        {
          label: "修改",
          type: "text",
          size: "small",
          onClick: (scope) => {
            console.log("修改", scope.row);
            dialogTitle.value = "修改";
            currentFormData.value = {
              businessCode: scope.row.businessCode,
              initiateChannel: scope.row.initiateChannel,
              script: scope.row.script,
            };
            showEditDialog.value = true;
          },
        },
      ],
    },
  ],
  tableData: data.value?.records ?? [],
  pagination: paginationRef.value,
}));

// 处理分页变化
const handlePaginationChange = (pagination: {
  currentPage: number;
  pageSize: number;
}) => {
  console.log("🚀 ~ handlePaginationChange ~ pagination:", pagination);
  console.log(
    "🚀 ~ handlePaginationChange ~ before update queryParams:",
    queryParams.value
  );

  // 防止重复更新：如果分页参数没有变化，不进行更新
  if (
    pagination.currentPage === queryParams.value.currentPage &&
    pagination.pageSize === queryParams.value.pageSize
  ) {
    console.log("🚀 ~ handlePaginationChange ~ 分页参数未变化，跳过更新");
    return;
  }

  queryParams.value.currentPage = pagination.currentPage;
  queryParams.value.pageSize = pagination.pageSize;

  console.log(
    "🚀 ~ handlePaginationChange ~ after update queryParams:",
    queryParams.value
  );
};

// 处理查询条件变化
const handleFormStateChange = ({
  businessCode,
  initiateChannel,
}: Record<string, any>) => {
  queryParams.value.businessCode = businessCode || "";
  queryParams.value.initiateChannel = initiateChannel || "";
  queryParams.value.currentPage = 1;
  refetch();
};

// 揭示信息配置成功后的回调
const handleRevealInfoSuccess = () => {
  if (dialogTitle.value === "新增") {
    // 新增成功，使用分页处理工具
    handleCreateSuccess({
      currentPage: queryParams.value.currentPage,
      pageSize: queryParams.value.pageSize,
      total: data.value?.total ?? 0,
    });
  } else {
    // 修改成功，使用分页处理工具
    handleUpdateSuccess();
  }
};
</script>

<template>
  <div>
    <CommonQueryTable
      :config="config"
      :dict-map="{}"
      row-key="businessCode"
      :loading="isFetching"
      @pagination-change="handlePaginationChange"
      @form-state-change="handleFormStateChange"
    />
    <!-- 揭示信息配置弹窗 -->
    <RevealInfoDialog
      v-model="showEditDialog"
      :title="dialogTitle"
      :channel-options="channelOptions"
      :initial-data="currentFormData"
      @success="handleRevealInfoSuccess"
    />
  </div>
</template>

<style scoped>
/* 如果需要特殊样式可以在这里添加 */
</style>
