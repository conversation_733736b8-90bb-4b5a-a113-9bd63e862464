<script setup lang="ts">
import ClientApi from "@/api/client";
import CommonDialog from "@/components/CommonDialog/index.vue";
import { ElMessage } from "element-plus";

interface Props {
  modelValue: boolean;
  title: string;
  channelOptions?: Array<{ label: string; value: number }>;
  initialData?: {
    businessCode: string;
    initiateChannel: number;
    script: string;
  };
}

interface Emits {
  (_e: "update:modelValue", _value: boolean): void;
  (_e: "success"): void;
}

const props = withDefaults(defineProps<Props>(), {
  channelOptions: () => [],
  initialData: () => ({
    businessCode: "",
    initiateChannel: 1, // 改为1，而不是0
    script: "",
  }),
});

const emit = defineEmits<Emits>();

const formRef = ref();
const formData = ref({
  businessCode: "",
  initiateChannel: 1 as number, // 改为1，而不是0
  script: "",
});

const formRules = {
  businessCode: [
    { required: true, message: "请输入业务代码", trigger: "change" },
  ],
  initiateChannel: [
    { required: true, message: "请选择发起渠道", trigger: "change" },
  ],
  script: [{ required: true, message: "请输入揭示信息", trigger: "change" }],
};

// 监听对话框显示状态，重置表单数据
watch(
  () => props.modelValue,
  (newValue) => {
    if (newValue) {
      // 对话框打开时，初始化表单数据
      formData.value = { ...props.initialData };
    }
  }
);

const resetForm = () => {
  formRef.value?.resetFields();
  formData.value = { ...props.initialData };
};

const submitForm = async () => {
  // 表单校验
  const valid = await formRef.value.validate();
  if (valid) {
    try {
      // 表单校验通过，执行提交逻辑
      console.log("表单校验通过，提交数据：", formData.value);
      if (props.title === "新增") {
        await ClientApi.addCoworkScript(formData.value);
        ElMessage.success("新增成功");
      } else {
        await ClientApi.updateCoworkScript(formData.value);
        ElMessage.success("修改成功");
      }
      // 关闭对话框
      emit("update:modelValue", false);
      // 通知父组件刷新数据
      emit("success");
    } catch (error) {
      console.error("提交失败", error);
      ElMessage.error("提交失败");
    }
  }
};

const handleClose = () => {
  resetForm();
  emit("update:modelValue", false);
};
</script>

<template>
  <CommonDialog
    :model-value="modelValue"
    :title="title"
    width="500px"
    @close="handleClose"
    @confirm="submitForm"
    @update:model-value="
      (value: boolean | undefined) => $emit('update:modelValue', value ?? false)
    "
  >
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="120px"
      class="mt-4"
    >
      <el-form-item label="业务代码" prop="businessCode">
        <el-input
          v-model="formData.businessCode"
          placeholder="请输入业务代码"
          :disabled="title === '修改'"
        />
      </el-form-item>

      <el-form-item label="发起渠道" prop="initiateChannel">
        <el-select
          v-model="formData.initiateChannel"
          placeholder="请选择发起渠道"
          :disabled="title === '修改'"
        >
          <el-option
            v-for="item in channelOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>

      <el-form-item label="揭示信息" prop="script">
        <el-input
          v-model="formData.script"
          type="textarea"
          :rows="4"
          placeholder="请输入揭示信息"
          maxlength="500"
          show-word-limit
        />
      </el-form-item>
    </el-form>
  </CommonDialog>
</template>

<style scoped>
/* 组件特定样式 */
</style>
