<script lang="ts" setup>
import WebSocketSingleton from "@/utils/WebSocketSingleton";
import { Client } from "@stomp/stompjs";
import useRemoteWitnessStore from "@/store/modules/remoteWitness";
import { WebSocketAction } from "@/enums/WebSocketActionEnum";
const router = useRouter();

let socket: Client | null = null;
const store = useRemoteWitnessStore();
async function setupWebSocket(token: string) {
  const ws = WebSocketSingleton.getInstance();

  try {
    // 建立连接
    socket = await ws.connect(token, true);

    console.log("WebSocket connection established.");

    // 订阅消息
    ws.subscribe("/message", (message) => {
      console.log("Received message:", message);
      const result = JSON.parse(message);
      if (result.action === WebSocketAction.ENTER_ROOM) {
        store.$patch({ roomId: result.roomId });
        router.push("/pad-witness/client");
      }
    });
  } catch (error) {
    console.error("WebSocket connection error:", error);
  }
}

onMounted(async () => {
  await setupWebSocket(store.socketToken);
});
</script>

<template>
  <div class="flex flex-col h-screen bg-gray-100 font-sans">
    <!--    <header class="bg-primary text-white text-center py-30 text-24">-->
    <!--      <span>存管账户开户 - 视频见证 3/3</span>-->
    <!--    </header>-->

    <div
      class="flex-1 bg-white border border-gray-200 mx-6rem mt-6rem mb-6rem rounded-2xl flex flex-col"
    >
      <main class="flex-1 flex flex-col">
        <h1 class="text-9rem text-dark-500 text-center mt-8rem mb-30rem">
          单人视频见证
        </h1>

        <div class="flex items-center justify-center">
          <div class="flex flex-col items-center">
            <img
              src="@/assets/pic_tzz2.png"
              alt="tzz"
              class="w-53rem mb-6rem"
            />
            <p class="text-5.5rem text-gray-400 font-medium">坐席连接中...</p>
          </div>
        </div>
      </main>
    </div>
  </div>
</template>

<style scoped>
.circle-progress {
  position: relative;
  width: 30rem;
  height: 30rem;
}

.progress-arrow {
  transform-origin: center;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }

  to {
    transform: rotate(360deg);
  }
}

.absolute-center {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}
</style>

<style>
body {
  background-color: #f3f4f6;
}
</style>
