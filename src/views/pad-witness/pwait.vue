<script lang="ts" setup>
// import { useFaceDetection } from "@/composables/useFaceDetection";
import { WebSocketAction } from "@/enums/WebSocketActionEnum";
import { useClientWaitStore } from "@/store";
import { useHtmlReadyStore } from "@/store/modules/htmlReady";
import useRemoteWitnessStore from "@/store/modules/remoteWitness";
import { getParamKey } from "@/utils/utils";
import WebSocketSingleton from "@/utils/WebSocketSingleton";

const position = ref<number>(-1);

const clientWaitStore = useClientWaitStore();
// const { sendReferenceImage } = useFaceDetection();

const router = useRouter();

const status = ref<"INIT" | "WAITING" | "CONNECTING">("INIT");

let ywqqid = getParamKey("ywqqid"); // 业务请求id"440192"
let clientId = getParamKey("clientId"); // 客户id
let filepath = getParamKey("filepath");

const loading = ref(false);
// const sdkAppId = parseInt(getParamKey("sdkAppId"), 10);
// const userSig = getParamKey("userSig");
const route = useRoute();
const store = useRemoteWitnessStore();
// const token: string = route.query.customerToken as string;
const socket = ref<WebSocket | null>(null); // 建立和 pad 端的连接
// const roomId = getParamKey("roomId");
let connectId = "sl001";
// const myUserId = getParamKey("userId");

// 监听上层的数据
console.log("init parent listener");
window.addEventListener(
  "message",
  function (event) {
    // alert(event.data);
    const initParamStr = event.data.initParam;
    if (!initParamStr) {
      return;
    }
    const initParam =
      typeof initParamStr == "string" ? JSON.parse(initParamStr) : initParamStr;
    console.log("parent initParam", initParam);
    if (initParam.ywqqid) {
      ywqqid = initParam.ywqqid;
    }
    if (initParam.clientId) {
      clientId = initParam.clientId;
    }
    if (initParam.filepath) {
      filepath = initParam.filepath;
    }
    beginQueue();
  },
  false
);

/**
 * 返回数据给上层
 * @param code 见证结果，1，成功，0，失败，-1,，意外结束见证
 * @param note 见证结果说明
 * @param agentId
 * @param agentName
 */
async function returnParent(
  code: number,
  note: string,
  agentId?: string,
  agentName?: string
) {
  console.info("返回数据给上层", code, note, agentId, agentName);
  window.parent.postMessage(
    JSON.stringify({
      // 见证结果，1，成功，0，失败，-1,，意外结束见证
      code,
      // 见证结果说明
      note,
      // 见证流水号（也就是上一代见证系统的排队房间号）
      witnessId: clientWaitStore.pwaitId,
      // 见证人的userid
      agentId,
      // 见证人的名称
      agentName,
    }),
    "*"
  );
}

async function setupWebSocket(token: string) {
  const ws = WebSocketSingleton.getInstance();

  try {
    // 建立连接
    socket.value = await ws.connect(token, false);
    console.log("WebSocket connection established.");
  } catch (error) {
    console.error("WebSocket connection error:", error);
  }
}
async function sendMessage() {
  try {
    loading.value = true;
    const ws = WebSocketSingleton.getInstance();
    await ws.publish({
      destination: "/message",

      body: JSON.stringify({
        recipient: connectId,
        content: JSON.stringify({
          action: WebSocketAction.ENTER_ROOM,
          // roomId: clientWaitStore.pwaitId,
          userId: clientWaitStore.clientId,
        }),
      }),
    });
    store.$patch({
      roomId: clientWaitStore.pwaitId,
      socketToken: clientWaitStore.customerToken,
      connectId: connectId,
      userId: clientWaitStore.clientId,
      sdkAppId: clientWaitStore.sdkAppId,
      userSig: clientWaitStore.customerSign,
    });
    // alert("排队完成，正在开始视频见证...");
    router.push("/pad-witness/client");

    loading.value = false;
  } catch (e) {
    console.log(e);
    loading.value = false;
  }
}

let pwaitTimer: any; // 排队请求状态获取定时器
const beginQueue = async () => {
  if (!clientId && ywqqid) {
    // 如果clientId为空，ywqqid不为空，则用ywqqid表示clientId
    clientId = ywqqid;
  }
  if (!(clientId && ywqqid)) {
    clientId = clientWaitStore.clientId;
    ywqqid = clientWaitStore.bizRequestId;
  }
  if (!(clientId && ywqqid)) {
    ElMessage({
      message: "clientId或ywqqid为空",
      type: "warning",
    });
    return;
  }
  status.value = "WAITING";
  clientWaitStore.init(clientId, ywqqid);
  await clientWaitStore.addPwait();
  pwaitTimer = setInterval(getPwaitStatus, clientWaitStore.witnessLoopTime);
};

async function getPwaitStatus() {
  const data = await clientWaitStore.getPwaitStatus();
  // 判断data.state：-1，客户主动取消请求；-2，无可服务坐席在线；-3，其他原因导致见证请求终止。如上三个状态都表示此次见证已结束，客户端需停止轮询。
  if (data.state === -1 || data.state === -2 || data.state === -3) {
    clearInterval(pwaitTimer);
    pwaitTimer = null;
    if (data.state == -1) {
      status.value = "INIT";
    } else if (data.state == -2) {
      ElMessage({
        message: "无可服务坐席在线",
        type: "warning",
      });
      status.value = "INIT";
    } else if (data.state == -3) {
      ElMessage({
        message: "其他错误",
        type: "warning",
      });
      status.value = "INIT";
    }
    return;
  }
  // data.state为0且data.serveAgentId为空，表示此时正在等待见证
  if (data.state === 0 && !data.serveAgentId) {
    console.log("等待中");
    position.value = data.index;
  }
  // data.state为1且data.serveAgentId不为空，表示已被坐席接受，前端可创建视频通话连接
  if (data.state === 1 && data.serveAgentId) {
    connectId = data.serveAgentId;
    if (socket.value == null) {
      console.log("已被坐席接受");
      // 获取客户端的token和sign，创建客户端ws腾讯连接
      await clientWaitStore.getClientSocketInfo();
      await setupWebSocket(clientWaitStore.customerToken);
      await sendMessage(); // 跳转到等待连接建立页面

      // 保存客户的头像
      // await sendReferenceImage(filepath);
    }
    if (data.result !== undefined && data.result !== null) {
      // data.result不为空，表示坐席已完成视频见证，客户端需停止轮询。
      clearInterval(pwaitTimer);
      pwaitTimer = null;
      status.value = "WAITING";
      // data.result为见证结果，true为成功，false为失败，data.reason表示见证的结果说明
      if (data.result) {
        console.log("见证成功");
      } else {
        console.log("见证失败");
      }
      await returnParent(
        data.result === "true" ? 1 : 0,
        data.reason,
        data.serveAgentId,
        data.serveAgentName
      );
    }
  }
}

const cancelQueue = async () => {
  const res = await clientWaitStore.cancelPwait();
  console.log("取消排队:", res);
  status.value = "INIT";
  position.value = -1;
  // alert("您已取消排队");
  // position.value = 1;
  await returnParent(-2, "客户取消排队");
};

const htmlReadyStore = useHtmlReadyStore();
onMounted(() => {
  // setupWebSocket(token);
  // nextTick(() => {
  //   window.parent.postMessage({ ready: true }, "*");
  // });
  htmlReadyStore.isReady();
});

onBeforeUnmount(() => {
  if (pwaitTimer) {
    clearInterval(pwaitTimer);
    pwaitTimer = null;
  }
});
</script>

<template>
  <div class="flex flex-col h-screen bg-gray-100 font-sans">
    <!--    <header class="bg-primary text-white text-center py-30 text-24">-->
    <!--      <span>存管账户开户 - 视频见证 3/3</span>-->
    <!--    </header>-->

    <div
      class="flex-1 bg-white border border-gray-200 rounded-2xl flex flex-col"
    >
      <main class="flex-1 flex flex-col">
        <h1 class="text-9rem text-dark-500 text-center mt-8rem">视频见证</h1>

        <div
          v-if="status === 'WAITING'"
          class="flex-1 flex items-center justify-center"
        >
          <div class="flex flex-col items-center">
            <div class="relative">
              <div class="circle-progress">
                <svg class="w-30rem h-30rem" viewBox="0 0 100 100">
                  <circle
                    cx="50"
                    cy="50"
                    r="45"
                    fill="none"
                    stroke="#E6F0FF"
                    stroke-width="8"
                  />
                  <path
                    d="M 50 5 A 45 45 0 0 1 95 50"
                    fill="none"
                    stroke="#007bff"
                    stroke-width="8"
                    stroke-linecap="round"
                    class="progress-arrow"
                  />
                </svg>
              </div>
              <div
                v-show="position > -1"
                class="absolute-center text-12rem text-primary font-bold"
              >
                {{ position + 1 }}
              </div>
            </div>
            <p class="text-5rem text-gray-400 mt-8rem">正在排队中...</p>
            <p v-show="position > -1" class="text-5rem text-gray-600 mt-0rem">
              您当前排在第 {{ position + 1 }} 位
            </p>
          </div>
        </div>
      </main>

      <footer class="mb-8rem text-center">
        <button
          v-if="status === 'INIT'"
          class="bg-primary hover:bg-primary-dark text-white px-40 py-13 text-18 rounded-4 transition-colors duration-300 cursor-pointer"
          @click="beginQueue"
        >
          开始见证
        </button>
        <button
          v-if="status === 'WAITING'"
          class="bg-primary hover:bg-primary-dark text-white px-40 py-13 text-18 rounded-4 transition-colors duration-300 cursor-pointer"
          @click="cancelQueue"
        >
          取消排队
        </button>
      </footer>
    </div>
  </div>
</template>

<style scoped>
.circle-progress {
  position: relative;
  width: 30rem;
  height: 30rem;
}

.progress-arrow {
  transform-origin: center;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }

  to {
    transform: rotate(360deg);
  }
}

.absolute-center {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}
</style>

<style>
body {
  background-color: #f3f4f6;
}
</style>
