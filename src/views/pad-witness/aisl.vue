<script setup lang="ts">
import CustomerAPI from "@/api/customer";
import { useFaceDetection } from "@/composables/useFaceDetection";
import { useUserStore } from "@/store";
import useRemoteWitnessStore from "@/store/modules/remoteWitness";
import { HSNRItem } from "@/types/type";
import ASR from "@/utils/asr/asr.esm";
import { emitter, EventTypes } from "@/utils/eventBus";
import WebSocketSingleton from "@/utils/WebSocketSingleton";
import { WarningFilled } from "@element-plus/icons-vue";
import TRTC from "trtc-sdk-v5";
import { nextTick, ref } from "vue";
const hsnr = ref<HSNRItem[]>([]);
let ywqqid: string = "";
let customerData: any = {};
const isRecording = ref(false);
const playIndex = ref(0);
const showSettingDialog = ref(false);
const router = useRouter();
const ttssub = ref();
const showAIDetectionTip = ref(false); // 控制智能检测提示框显示
const aiDetectionMessage = ref(""); // 检测提示内容
const loading = ref(false);
const showDialog = ref(false); // 控制弹窗显示
let ttsPlayering = ref<boolean>(false);
let asring = ref<boolean>(false);
const showVideoIcon = ref(true);
const WIDTH = 336;
const userStore = useUserStore();
const HEIGHT = 99;
const speed = ref(1);
const isLoading = ref(false);
const route = useRoute();
const store = useRemoteWitnessStore();
const voiceMic = ref();
const trtc = markRaw(TRTC.create());
const mediaUrl = ref("");
const showVideoPreview = ref(false);
const coverUrl = ref("");
const roomId = store.roomId;
let socket = null;
const sdkAppId = store.sdkAppId;
const marqueeTextRef = ref<any[]>([]);
let waterSyncClearup: (() => void) | undefined;
const userId = store.userId;
const userSig = store.userSig;
const isScreenSharing = ref(false);
const hasRecord = ref(false);
const khhd = ref("");
const centerDialogVisible = ref(false);
const animationFrameId = ref<number | null>(null);
let nowPlayIndex = ref(0);
const subtitles = ref([]);
const slItemData = ref({});
const sbzp_file = ref("");
let cacheClear = null;
const streamCanvas = document.createElement("canvas");
streamCanvas.width = WIDTH;
streamCanvas.height = HEIGHT;
const clientId = Math.floor(100000 + Math.random() * 900000);
const streamCtx = streamCanvas.getContext("2d", { alpha: false });
// 添加一个定时器引用
let scrollTimerId: number | null = null;
const MESSAGE_SOURCE_ID = "aisl-" + Date.now();

let token = "";
const scrollContainer = ref(null);
const textContainers = ref([]);

// 添加进度处理函数
const handleMarqueeProgress = (scrollerNumber: number) => {
  console.log(scrollerNumber, "scrollerNumber");
  const currentContainer = textContainers.value[playIndex.value];
  if (currentContainer && scrollContainer.value) {
    const containerRect = scrollContainer.value.getBoundingClientRect();
    const elementRect = currentContainer.getBoundingClientRect();
    // 如果元素底部超出可视区域，则滚动
    if (elementRect.bottom > containerRect.bottom) {
      // 检查是否已经滚动到底部
      const isAtBottom =
        Math.abs(
          scrollContainer.value.scrollTop +
            scrollContainer.value.clientHeight -
            scrollContainer.value.scrollHeight
        ) < 1;
      // 如果已经滚动到底部，则不再滚动
      if (isAtBottom) return;
      // 计算行高（根据实际情况调整）
      const lineHeight = 35; // 根据您的 font-leading-35 设置
      // 限制最大滚动距离为2行高度
      const maxScroll = lineHeight * (scrollerNumber - 1);
      // 计算需要滚动的实际距离
      const scrollDistance = Math.min(
        elementRect.bottom - containerRect.bottom,
        maxScroll
      );
      scrollContainer.value.scrollTo({
        top: scrollContainer.value.scrollTop + scrollDistance,
        behavior: "smooth",
      });
    }
  }
};
const handlePrevious = () => {
  if (playIndex.value > 0) {
    playIndex.value--;
    scrollContainer.value?.scrollTo({
      top: 0,
      behavior: "smooth",
    });
    playTTS(playIndex.value);
  }
};
const rePlayTTS = () => {
  centerDialogVisible.value = false;
  playTTS(playIndex.value);
  playTTS(playIndex.value);
};
const videoPreviewLoading = ref(false);
async function searchMedia() {
  videoPreviewLoading.value = true; // 开始加载，设置 loading 状态为 true

  // 定义轮询函数
  const pollMedia = async () => {
    const res = await CustomerAPI.searchMedia(store.roomId, token);
    console.log("🚀 ~ searchMedia ~ res:", res);

    // 更新当前值
    mediaUrl.value = res.mediaUrl || "";
    coverUrl.value = res.coverUrl || "";

    // 检查是否 mediaUrl 和 coverUrl 都有值
    if (res.mediaUrl && res.coverUrl) {
      // 如果都有值，结束轮询，更新 loading 状态
      videoPreviewLoading.value = false;
      // 只有在获取成功后才显示视频预览弹窗
      showVideoPreview.value = true;
      return true;
    }
    return false;
  };

  // 首次尝试获取
  let isComplete = await pollMedia();

  // 如果首次获取未成功，开始轮询
  if (!isComplete) {
    // 显示轮询开始的提示信息
    ElMessage({
      message: "视频合成中，请稍候...",
      type: "info",
      duration: 3000,
    });
    // 最多轮询 30 次，每次间隔 2 秒，总共最多等待 60 秒
    let attempts = 0;
    const maxAttempts = 30;
    const interval = 2000; // 2 秒

    const polling = setInterval(async () => {
      attempts++;

      try {
        isComplete = await pollMedia();

        // 如果获取成功或达到最大尝试次数，清除轮询
        if (isComplete || attempts >= maxAttempts) {
          clearInterval(polling);

          // 如果达到最大尝试次数但仍未获取到，也结束 loading 状态
          if (!isComplete && attempts >= maxAttempts) {
            videoPreviewLoading.value = false;
            console.warn("视频预览链接获取超时");
            ElMessage({
              message: "视频预览链接获取超时，请稍后再试",
              type: "warning",
              duration: 3000,
            });
          }
        }
      } catch (error) {
        console.error("轮询视频预览链接出错:", error);
        clearInterval(polling);
        videoPreviewLoading.value = false;
        ElMessage({
          message: "获取视频预览出错，请稍后再试",
          type: "error",
          duration: 3000,
        });
      }
    }, interval);
  }
}

// 监听上层的数据
window.addEventListener("message", function (event) {
  console.log(event.data);
  try {
    const initParamStr = event.data.initParam;
    if (!initParamStr) {
      return;
    }
    const initData =
      typeof initParamStr == "string" ? JSON.parse(initParamStr) : initParamStr;
    console.log(initData);
    ywqqid = initData.ywqqid;
    hsnr.value = initData.slItemData.slhs;
    slItemData.value = initData.slItemData;
    customerData = initData.customerData;
    sbzp_file.value = initData.khzpImage;
    loginFunc(initData.token);
  } catch (e) {}

  // if (event.data.clientId) {
  //   clientId = event.data.clientId;
  // }
  // if (event.data.ywqqid) {
  //   ywqqid = event.data.ywqqid;
  // }
});

// 返回数据给上层
function returnParent(message: any) {
  window.parent.postMessage(message, "*");
}
const handleNext = () => {
  if (playIndex.value < hsnr.value.length - 1) {
    playIndex.value++;
    scrollContainer.value?.scrollTo({
      top: 0,
      behavior: "smooth",
    });
    playTTS(playIndex.value);
  }
};
// 在变量声明区域添加
const currentTime = ref(
  new Date()
    .toLocaleString("zh-CN", {
      year: "numeric",
      month: "2-digit",
      day: "2-digit",
      hour: "2-digit",
      minute: "2-digit",
      second: "2-digit",
      hour12: false,
    })
    .replace(/\//g, "-")
);

// 添加更新时间的函数
const updateCurrentTime = () => {
  currentTime.value = new Date()
    .toLocaleString("zh-CN", {
      year: "numeric",
      month: "2-digit",
      day: "2-digit",
      hour: "2-digit",
      minute: "2-digit",
      second: "2-digit",
      hour12: false,
    })
    .replace(/\//g, "-");
};
const currentAsrText = ref(""); // 添加一个 ref 来存储当前的识别文本

let ttemConfig = {
  asrSecretId: "",
  asrSecretKey: "",
  asrToken: "",
  appId: "",
  customerSign: "",
  sdkAppId: "",
  customerId: "",
  subId: "",
  subSign: "",
};
let AudioAsrTimeOut: number | null = null;
let webAudioSpeechRecognizer: any = null;
let taskId;
const historyList = ref();
let asrIng = ref(false);
let timer: ReturnType<typeof setInterval> | null = null; // 计时器的类型
let cleanupFaceDetect: (() => void) | undefined;
const countdownSeconds = ref(10);

const {
  detectFaces,
  detectionResult,
  startCapture,
  detectionHistory,
  isProcessing,
  sendReferenceImage,
  qualityAlert,
  clearupFaceDetection,
} = useFaceDetection();
const startTimer = (): void => {
  if (timer) return;
  timer = store.startTimer();
};
watch(
  detectionHistory,
  (val) => {
    nextTick(() => {
      scrollToBottom();
    });
  },
  { deep: true, immediate: true }
);
const isMobileDevice = computed(() => {
  const ua = navigator.userAgent.toLowerCase();

  return /android|webos|iphone|ipad|ipod|blackberry|iemobile|opera mini/i.test(
    ua
  );
});
function scrollToBottom() {
  const lastItems = historyList.value?.querySelectorAll(".time-container");
  if (lastItems && lastItems.length > 0) {
    lastItems[lastItems.length - 1].scrollIntoView({ behavior: "smooth" });
  }
}
const toggleRecording = async () => {
  if (isLoading.value) return; // 防止重复点击

  if (isRecording.value) {
    isLoading.value = true;
    try {
      await handleStopRecord();
    } finally {
      isLoading.value = false;
    }
  } else {
    isLoading.value = true;
    try {
      await handleStartRecord();
    } finally {
      isLoading.value = false;
    }
  }
};
const handleStartRecord = async () => {
  hsnr.value.forEach((_, id) => {
    if (hsnr.value[id].isPlaying) {
      hsnr.value[id].isPlaying = false;
      hsnr.value[id].currentTime = 0;
      hsnr.value[id].asr_content = "";
      hsnr.value[id].start_time = undefined;
      hsnr.value[id].asr_time = undefined;
      hsnr.value[id].isPlaying = false;
      marqueeTextRef.value[id]?.stopPlay();
    }
  });
  detectionHistory.value = [];
  store.$patch({ detectionHistory: [], elapsedSeconds: 0 });

  await handleEnter();
  const result = await CustomerAPI.startRecord(
    store.roomId,
    ttemConfig.customerId,
    ttemConfig.subId,
    "1",
    token
  );
  hasRecord.value = false;
  isRecording.value = true;
  startTimer();
  taskId = result.taskId;

  cleanupFaceDetect = await startDetectFace();
  setTimeout(() => {
    playTTS(0);
  }, 500);
};
async function startDetectFace() {
  // 1. 获取视频轨道并创建视频元素
  const video = document.createElement("video");
  let sourceVideoTrack = trtc.getVideoTrack({ userId: store.connectId });
  // let sourceVideoTrack = trtc.getVideoTrack();
  if (!sourceVideoTrack) {
    throw new Error("获取视频轨道失败");
  }

  // 2. 设置视频流
  const mediaStream = new MediaStream();
  mediaStream.addTrack(sourceVideoTrack);
  video.srcObject = mediaStream;
  await video.play();
  await sendReferenceImage(sbzp_file.value);
  await detectFaces(video, true);
  await startCapture(video);

  // 返回清理函数
  return () => {
    // 停止视频播放
    video.pause();
    // 清理视频源
    video.srcObject = null;
    // 移除视频元素
    video.remove();
    // 停止媒体流中的所有轨道
    mediaStream.getTracks().forEach((track) => track.stop());
    clearupFaceDetection();
  };
}
const handleStopRecord = async () => {
  hsnr.value.forEach((_, id) => {
    if (hsnr.value[id].isPlaying) {
      hsnr.value[id].isPlaying = false;
      hsnr.value[id].currentTime = 0;
      marqueeTextRef.value[id]?.stopPlay();
    }
  });
  hasRecord.value = true;
  stopScreenShare();
  waterSyncClearup && waterSyncClearup();
  stopAsr();
  //退出房间，清理资源
  scrollTimerId && clearInterval(scrollTimerId);
  scrollTimerId = null;
  cleanupFaceDetect && cleanupFaceDetect();
  timer && clearInterval(timer);
  timer = null;
  const result = await CustomerAPI.stopRecord(taskId, token);
  isRecording.value = false;

  // await CustomerAPI.modifyWitness(
  //   {
  //     id: Number(store.roomId),
  //     markerData: JSON.stringify({
  //       detectionHistory: detectionHistory.value,
  //       hsnr: hsnr.value,
  //     }),
  //   },
  //   token
  // );

  await handleExit();
};
function startScreenShare() {
  if (!isScreenSharing.value && trtc && !isMobileDevice.value) {
    // 首次启动屏幕共享
    const canvasStream = streamCanvas.captureStream(30);
    trtc?.startScreenShare({
      publish: true,
      option: { videoTrack: canvasStream.getVideoTracks()[0] },
    });
    isScreenSharing.value = true;
  }
}
function backToParent() {
  if (!hasRecord.value) {
    ElMessage.error("还未进行视频录制");
    return;
  }
  returnParent({
    roomId: store.roomId,
  });
}
function stopScreenShare() {
  if (isScreenSharing.value && trtc) {
    trtc?.stopScreenShare();
    isScreenSharing.value = false;
  }
}
/**
 * 为视频流添加动态水印（优化版）
 * @param trtc TRTC 实例
 * @returns 清理函数
 */
async function startWaterMark(targetElementCanvas: HTMLCanvasElement) {
  if (!targetElementCanvas) {
    console.warn("未找到目标 Canvas 元素");
    return;
  }

  let animationFrameId: number | null = null;
  let lastRenderTime = 0;
  const RENDER_INTERVAL = 60; // 渲染间隔（毫秒）

  let isDestroyed = false; // 添加标志位

  const context = targetElementCanvas.getContext("2d");
  if (!context) {
    console.warn("无法获取 Canvas 2D 上下文");
    return;
  }

  const CanvasWIDTH = targetElementCanvas.width;
  const CanvasHEIGHT = targetElementCanvas.height;

  const updateWatermark = async (timestamp: number) => {
    if (isDestroyed) {
      return;
    }
    try {
      // 检查时间间隔，避免过于频繁渲染
      if (timestamp - lastRenderTime < RENDER_INTERVAL) {
        animationFrameId = requestAnimationFrame(updateWatermark);
        return;
      }

      if (CanvasWIDTH === 0 || CanvasHEIGHT === 0) {
        console.warn("Source canvas has invalid dimensions");
        animationFrameId = requestAnimationFrame(updateWatermark);
        return;
      }
      if (isMobileDevice.value) {
        sendFrame();
      } else {
        streamCtx?.clearRect(0, 0, WIDTH, HEIGHT);
        streamCtx?.drawImage(targetElementCanvas, 0, 0, WIDTH, HEIGHT);
      }

      lastRenderTime = timestamp;
    } catch (error) {
      console.error("采集或发送帧失败:", error);
    }

    animationFrameId = requestAnimationFrame(updateWatermark);
  };

  // 发送帧的函数
  const sendFrame = () => {
    const base64Data = targetElementCanvas.toDataURL("image/jpeg", 0.8);
    // 发送 base64 数据
    window.parent.postMessage(
      {
        type: "videoFrame",
        data: base64Data,
        width: WIDTH,
        height: HEIGHT,
        timestamp: Date.now(),
        rotation: 0,
      },
      "*" // 目标源，生产环境应指定具体域名
    );
  };

  // 启动渲染循环
  animationFrameId = requestAnimationFrame(updateWatermark);

  // 返回清理函数
  return () => {
    isDestroyed = true; // 设置销毁标志
    if (animationFrameId) {
      cancelAnimationFrame(animationFrameId);
      animationFrameId = null;
    }
  };
}
const playTTS = async (index: number) => {
  if (!isRecording.value) {
    return;
  }
  // 停止当前播放
  const stopCurrent = (id: number) => {
    hsnr.value[id].isPlaying = false;
    hsnr.value[id].currentTime = 0;
    marqueeTextRef.value[id]?.stopPlay();
    waterSyncClearup && waterSyncClearup();

    // 清除滚动定时器
    if (scrollTimerId) {
      clearInterval(scrollTimerId);
      scrollTimerId = null;
    }
  };

  // 开始新的播放
  const startNew = async (id: number) => {
    const item = hsnr.value[id];
    scrollContainer.value?.scrollTo({
      top: 0,
      behavior: "smooth",
    });
    // 获取当前跑马灯 DOM 元素
    const currentMarqueeCanvas = marqueeTextRef.value[id]?.canvas;

    if (currentMarqueeCanvas) {
      // 启动水印，传入当前跑马灯 DOM
      startScreenShare();
      waterSyncClearup = await startWaterMark(currentMarqueeCanvas);
    }

    await marqueeTextRef.value[id]?.startPlay();
    item.start_time = store.elapsedSeconds;
    item.isPlaying = true;
    playIndex.value = id;
  };

  // 停止所有其他播放
  hsnr.value.forEach((_, id) => {
    if (id !== index) stopCurrent(id);
  });

  // 切换目标项的播放状态
  const targetItem = hsnr.value[index];
  if (!targetItem) return;
  if (targetItem.isPlaying) {
    stopCurrent(index);
  } else {
    await startNew(index);
  }
};
async function handleAudioEnded(sourceHsnr: HSNRItem) {
  let findIsPlayingIndex = hsnr.value.findIndex((i) => i.isPlaying);
  const findIsPlaying = hsnr.value[findIsPlayingIndex];
  if (sourceHsnr !== findIsPlaying) return;
  if (!findIsPlaying) return;

  const khhd = findIsPlaying.khhd;
  debugger;
  // return;
  console.log("handleAudioEnded");
  waterSyncClearup && waterSyncClearup();
  console.log(AudioAsrTimeOut);
  // return;
  if (AudioAsrTimeOut) return;
  if (khhd && "无需".includes(khhd)) {
    playTTS(++findIsPlayingIndex);
    return;
  }

  const audioTrack = await trtc.getAudioTrack();
  if (!audioTrack) return;
  webAudioSpeechRecognizer = new ASR({
    secretKey: ttemConfig.asrSecretKey,
    secretId: ttemConfig.asrSecretId,
    appId: ttemConfig.appId,
    token: ttemConfig.asrToken,
    // 实时识别接口参数
    engine_model_type: "16k_zh_large", // 引擎
    voice_format: 1,
    audioTrack,
    // 以下为非必填参数，可跟据业务自行修改
    needvad: 1,
    hotword_list: "是|11,是的|11,确认|11,准确|10,无误|6,准确无误|7,对|8,没错|9",
    filter_modal: 1,
  });
  // 开始识别
  webAudioSpeechRecognizer.OnRecognitionStart = (res) => {
    console.log("开始识别", res);
    asrIng.value = true;
    // voiceMic.value.startCountdown();
    AudioAsrTimeOut = setInterval(() => {
      if (countdownSeconds.value <= 0) {
        stopAsr();
        centerDialogVisible.value = true;
        emitter.emit(EventTypes.FACE_DETECTION_UPDATE, {
          forceUpdate: true,
          result: {
            detail: `客户回答内容【${currentAsrText.value || "未检测到语音"}】与业务要求回答【${khhd}】不一致`,
          },
        });
        return;
      }
      countdownSeconds.value--;
    }, 1000);
  };
  // 一句话开始
  webAudioSpeechRecognizer.OnSentenceBegin = (res) => {
    console.log("一句话开始", res);
  };
  // 识别变化时
  webAudioSpeechRecognizer.OnRecognitionResultChange = (res) => {
    console.log(res);
    console.log("识别变化时", res.result.voice_text_str);
    currentAsrText.value = res.result.voice_text_str; // 更新当前识别文本
    const text = res.result.voice_text_str;
    const result = /[。，、；：？！,.;:?!]$/.test(text)
      ? text.slice(0, -1)
      : text;
    if (khhd?.split("|").includes(result)) {
      findIsPlaying.asr_time = store.elapsedSeconds;
      findIsPlaying.asr_content = text;
      playTTS(++findIsPlayingIndex);
      stopAsr();
    }
  };
  // 一句话结束
  webAudioSpeechRecognizer.OnSentenceEnd = (res) => {
    // asrText.innerText = resultText;
    currentAsrText.value = res.result.voice_text_str;
    console.log(res, "一句话结束");
    // stopAsr();
  };
  // 识别结束
  webAudioSpeechRecognizer.OnRecognitionComplete = (res) => {
    console.log("识别结束", res);
    // stopAsr();
  };
  // 识别错误
  webAudioSpeechRecognizer.OnError = (res) => {
    console.log("识别失败", res);
    // stopAsr();
  };
  webAudioSpeechRecognizer.start();
}
const stopAsr = () => {
  AudioAsrTimeOut && clearTimeout(AudioAsrTimeOut);
  AudioAsrTimeOut = null;
  asrIng.value = false;
  countdownSeconds.value = 10; // 重置倒计时
  webAudioSpeechRecognizer && webAudioSpeechRecognizer.stop();
  webAudioSpeechRecognizer = null;
};
let timeInterval = null;
onMounted(async () => {
  try {
    timeInterval = setInterval(updateCurrentTime, 1000);
    await trtc.startLocalVideo({
      view: "local",
      publish: false,
      option: {
        profile: "720p",
        fillMode: "contain",
        cameraId: store.videoDeviceId,
      },
    });
    await trtc.startLocalAudio({
      option: { microphoneId: store.audioDeviceId },
    });
    // const loginData = await AuthAPI.login({
    //   username: "sl003",
    //   password: "abc**123",
    //   captchaKey: "",
    //   captchaCode: "",
    //   mode: "customerSelf",
    // });
    // loginFunc(loginData);
    await nextTick();
    await returnParent({ ready: true });
  } catch (error: any) {
    ElMessage({
      message: error.message || "系统错误",
      type: "error",
    });
  }
});

const handleCancel = () => {
  console.log("handleCancel");
  // startPlay();
  // startWaterMark();
};
async function loginFunc(loginData: string) {
  isLoading.value = true; // 开始加载
  try {
    // 更新store和token
    token = loginData;
    // localStorage.setItem(TOKEN_KEY, loginData);
    store.$patch({ socketToken: loginData });
    const ws = WebSocketSingleton.getInstance();
    await ws.connect(loginData);

    // 获取配置信息
    const data = await CustomerAPI.ttem(loginData);

    // 更新配置
    Object.assign(ttemConfig, {
      asrSecretId: data.asrSecretId,
      asrSecretKey: data.asrSecretKey,
      asrToken: data.asrToken,
      appId: data.appId,
      subSign: data.subSign,
      subId: data.subId,
      customerSign: data.customerSign,
      customerId: data.customerId,
      sdkAppId: data.sdkAppId,
    });
  } catch (error) {
    console.error("登录或获取配置失败:", error);
    ElMessage.error("初始化失败，请刷新页面重试");
  } finally {
    isLoading.value = false; // 结束加载
  }
}

async function handleEnter() {
  try {
    await trtc.startLocalVideo({
      view: "local",
      publish: false,
      option: {
        profile: "720p",
        fillMode: "contain",
      },
    });
    await trtc.startLocalAudio({
      option: { microphoneId: store.audioDeviceId },
    });
  } catch (e) {}
  try {
    let roomId = Math.floor(Math.random() * 100000);
    store.$patch({ roomId: roomId });

    await trtc.enterRoom({
      roomId: roomId,
      sdkAppId: Number(ttemConfig.sdkAppId),
      userId: ttemConfig.customerId,
      userSig: ttemConfig.customerSign,
      scene: TRTC.TYPE.SCENE_LIVE,
      role: TRTC.TYPE.ROLE_ANCHOR,
    });

    // trtc.on(TRTC.EVENT.REMOTE_VIDEO_AVAILABLE, handleRemoteVideoAvailable);
    // trtc.on(TRTC.EVENT.REMOTE_VIDEO_UNAVAILABLE, handleRemoteVideoUnavailable);

    await trtc.updateLocalVideo({
      view: "local",
      publish: true,
    });
    returnParent({
      subSign: ttemConfig.subSign,
      subId: ttemConfig.subId,
      SDKAPPID: ttemConfig.sdkAppId,
      type: "ttem",
      roomId: roomId,
    });
    // const video = document.querySelector("#local video");
    // console.log(video);
    // if (video) {
    //   video.muted = true;
    //   video.setAttribute("muted", "");
    //   video.volume = 0;
    // }
    showVideoIcon.value = false;
    // if (isWaterTTS.value) {
    //   cacheClear = await startWaterMark(); //tts字幕加入到视频流中，性能有限,会引发卡顿。
    // }
  } catch (error: any) {
    ElMessage({
      message: error.message,
      type: "error",
    });
  }
}

async function handleRemoteVideoAvailable(event: any) {
  console.log("[demo] Video Available", event);
  const { userId, streamType } = event;
  try {
    if (streamType === TRTC.TYPE.STREAM_TYPE_MAIN) {
      store.invitedRemoteUsers.push(`${userId}_main`);
      await nextTick();
      await trtc.startRemoteVideo({
        userId,
        streamType,
        option: { fillMode: "contain" },
        view: `remote`,
      });
    } else {
      store.invitedRemoteUsers.push(`${userId}_screen`);
      await nextTick();
      trtc.startRemoteVideo({
        userId,
        streamType,
        option: { fillMode: "contain" },
        view: `screen`,
      });
    }
    console.log(`startRemoteVideo success: [${userId}]`);
  } catch (error: any) {
    console.log(
      `startRemoteVideo failed: [${userId}], error: ${error.message}`
    );
  }
}

async function handleRemoteVideoUnavailable(event: any) {
  console.log("[demo] Video Unavailable", event);
  const { streamType } = event;
  trtc.stopRemoteVideo({ userId: event.userId, streamType });

  if (streamType === TRTC.TYPE.STREAM_TYPE_MAIN) {
    store.invitedRemoteUsers = store.invitedRemoteUsers.filter(
      (userId: string) => userId !== `${event.userId}_main`
    );
    await handleExit();
  } else {
    store.invitedRemoteUsers = store.invitedRemoteUsers.filter(
      (userId: string) => userId !== `${event.userId}_screen`
    );
  }
}

async function handleExit() {
  try {
    // trtc.off(TRTC.EVENT.REMOTE_VIDEO_AVAILABLE, handleRemoteVideoAvailable);
    // trtc.off(TRTC.EVENT.REMOTE_VIDEO_UNAVAILABLE, handleRemoteVideoUnavailable);
    await trtc.exitRoom();
    await trtc.stopLocalAudio();
    await trtc.stopLocalVideo();
    returnParent({ type: "exitRoom" });
  } catch (error: any) {
    ElMessage({
      message: `Exit room failed. Error: ${error.message}`,
      type: "error",
    });
  }
}

// 组件卸载时清理
onBeforeUnmount(() => {
  cacheClear && cacheClear();

  timeInterval && clearInterval(timeInterval);
  // 清除滚动定时器
  if (scrollTimerId) {
    clearInterval(scrollTimerId);
    scrollTimerId = null;
  }
  cleanupFaceDetect && cleanupFaceDetect();
  const wsSingleton = WebSocketSingleton.getInstance();
  wsSingleton.unsubscribeAll();
  wsSingleton.disconnect();
});
</script>

<template>
  <div class="min-h-screen flex flex-col continersl p20 h-100vh">
    <!-- 主要内容区域 -->
    <div
      class="flex-1 w-full flex flex-col bg-hex-FFFFFF shadow-[0px_0px_7px_0px_rgba(85,85,85,0.6)] rounded-6 h-full"
    >
      <!-- 视频和脚本区域 -->
      <div
        class="flex-1 grid grid-cols-1 gap-24 min-h-0 lg:grid-cols-[3fr_2fr]"
      >
        <!-- 视频预览区 -->
        <div
          class="relative flex flex-col min-h-[300px] lg:min-h-0 h-full min-h-0"
        >
          <div class="flex flex-row justify-between ml-17 mt-28 mb-21">
            <div class="fw-400 text-16 color-hex-000000 flex flex-row">
              视频区
              <div>
                <div class="nav-action-item" @click="showSettingDialog = true">
                  <svg-icon icon-class="setting" />
                </div>
              </div>
            </div>
            <div
              v-if="isRecording"
              class="fw-400 text-15 color-hex-FF0000 flex-row flex flex-gap4 flex-center"
            >
              <div class="w-8 h-8 bg-hex-D9001B rounded-4"></div>
              <span class="">
                {{ store.formattedTime }}
              </span>
            </div>
            <div class="fw-400 text-15 color-hex-555555">
              {{ currentTime }}
            </div>
          </div>
          <div class="flex-1 flex flex-col min-h-0 overflow-hidden">
            <div class="flex-3 flex pl-18 bg-white min-h-0">
              <div
                class="wh-full bg-hex-FFFFFF rounded-6px border-1px border-solid border-hex-AAAAAA flex-center relative"
              >
                <div class="absolute-center z-1">
                  <el-icon color="#CCCCCC" size="96">
                    <video-camera />
                  </el-icon>
                </div>
                <div id="local" class="absolute inset-0 z-2"></div>
              </div>
            </div>
            <div class="flex-2 flex flex-col ml-18 min-h-0 overflow-hidden">
              <div class="text-16 m-y-12">实时质检结果</div>
              <div
                ref="historyList"
                class="bg-hex-FFFFFF rounded-6px border-1px border-solid border-hex-AAAAAA flex-1 overflow-y-auto min-h-0"
              >
                <div
                  v-for="item in detectionHistory"
                  :key="item.timestamp"
                  ref="lastItem"
                  class="time-container"
                >
                  <div class="flex-col w-full">
                    <div class="flex">
                      <div class="time color-[hex-FF1313]">
                        {{ item.timestamp }}
                      </div>
                      <div class="timesub">{{ `${item.detail}` }}</div>
                    </div>
                    <div class="line mt-20"></div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 脚本区域 -->
        <div
          class="flex flex-col bg-white rounded-lg min-h-[300px] lg:min-h-0 pr-21"
        >
          <div class="flex flex-row justify-between">
            <div class="fw-400 text-16 color-hex-000000 mt-28 mb-21">
              {{ slItemData.slmc }}双录话术 ({{ playIndex + 1 }} /
              {{ hsnr.length }})
            </div>
            <div
              v-if="asrIng"
              class="bg-hex-000000 flex-center mt-20 w-118 h-38 shadow-[0px_0px_2px_0px_rgba(102,102,102,0.5)] rounded-19 fw-400 text-14 color-hex-FFFFFF"
            >
              回答倒计时
              <div class="text-18 color-hex-FFFFFF">{{ countdownSeconds }}</div>
              S
            </div>
          </div>

          <div
            class="wh-full bg-hex-FFFFFF rounded-6 border-1 border-solid border-hex-AAAAAA flex flex-col justify-between"
          >
            <div
              ref="scrollContainer"
              class="flex-1 p-4 overflow-y-auto scroll-smooth"
              style="max-height: calc(80vh - 180px)"
            >
              <div class="flex items-start mb-4">
                <div
                  :class="[
                    'rounded-full shrink-0 w-44 h-44 flex-center ml-16 mt-18',
                    isRecording ? 'bg-blue-400' : 'bg-hex-CCCCCC',
                  ]"
                >
                  <svg-icon
                    icon-class="voice"
                    size="17"
                    :color="isRecording ? 'white' : '#999999'"
                    :class="{
                      'cursor-not-allowed': !isRecording,
                      'cursor-pointer': isRecording,
                    }"
                    @click="isRecording && playTTS(playIndex)"
                  />
                </div>
                <div class="flex-1 pt-21 pl-19 pr-21 wh-full flex flex-col">
                  <div
                    v-for="(hs, index) in hsnr"
                    :key="hs.nr"
                    ref="textContainers"
                    class="hsnr-container"
                  >
                    <marquee-text
                      v-show="playIndex === index"
                      :ref="(el) => (marqueeTextRef[index] = el)"
                      :hsnr="hs.nr"
                      :hsnr-item="hs"
                      :speed="speed"
                      :canvas-height="HEIGHT"
                      :canvas-width="WIDTH"
                      :trtc="trtc"
                      :hand-audio-end="handleAudioEnded"
                      @scroller="handleMarqueeProgress"
                    />
                  </div>
                </div>
              </div>
            </div>

            <div class="mr-18">
              <div class="flex items-center h-100">
                <div class="flex gap-9 ml-17">
                  <button
                    :disabled="playIndex <= 0"
                    class="rounded-full hover:bg-gray-200 h-40 w-40 bg-white border-1px border-solid border-hex-999999 disabled:opacity-50 disabled:cursor-not-allowed"
                    @click="handlePrevious"
                  >
                    <svg-icon icon-class="上一条" size="18" color="#666666" />
                  </button>
                  <button
                    :disabled="playIndex >= hsnr.length - 1"
                    class="rounded-full hover:bg-gray-200 h-40 w-40 bg-white border-1px border-solid border-hex-999999 disabled:opacity-50 disabled:cursor-not-allowed"
                    @click="handleNext"
                  >
                    <svg-icon icon-class="下一条" size="18" color="#666666" />
                  </button>
                </div>
                <div class="flex items-center gap-12 justify-end grow-1">
                  <div
                    class="bg-hex-EEEEEE rounded-3px min-h-46 max-w-[75%] flex-center p-x-10"
                  >
                    <span
                      class="fw-400 text-18 color-hex-333333 font-leading-29 break-words whitespace-normal line-clamp-3"
                    >
                      投资者：{{ hsnr[playIndex]?.khhd }}
                    </span>
                  </div>

                  <div class="rounded-full khtx"></div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 底部控制栏 -->
      <div class="mt-14 bg-white rounded-lg shadow p-4">
        <div class="flex flex-wrap flex-row justify-between p-x-20 mt-13 mb-17">
          <div class="gap-17 flex flex-row flex-x-center">
            <el-button
              type="primary"
              class="el-button-size"
              :loading="isLoading"
              :disabled="isLoading"
              @click="toggleRecording"
            >
              {{ isRecording ? "结束录制" : "开始录制" }}
            </el-button>
            <el-button
              class="el-button-size"
              type="primary"
              :disabled="!hasRecord"
              @click="searchMedia"
            >
              播放视频
            </el-button>
            <div class="fw-400 text-16 color-hex-000000 font-leading-47">
              注意录制前请调拍摄角度，并注意麦克风是否正常
            </div>
          </div>
          <el-button
            class="el-button-size"
            type="primary"
            @click="backToParent"
          >
            下一步
          </el-button>
        </div>
      </div>
    </div>
    <VideoPreviewDialog
      v-model:visible="showVideoPreview"
      :video-url="mediaUrl"
      title="视频预览"
      :hsnr="hsnr"
    />
    <el-dialog
      v-model="showSettingDialog"
      :destroy-on-close="true"
      width="500"
      :close-on-click-modal="false"
      @close="showSettingDialog = false"
    >
      <template #title>
        <tag-with-indicator title="设备选择" />
      </template>
      <div class="overflow-y-auto flex-center">
        <webrtc-check />
      </div>
    </el-dialog>
    <el-dialog
      v-model="centerDialogVisible"
      width="519"
      align-center
      :show-close="false"
    >
      <template #header>
        <div
          class="w-full flex-row flex-center fw-400 text-21 color-hex-000000"
        >
          提示
        </div>
      </template>
      <div class="flex-x-center ml-46 mr-38">
        <el-icon color="#FF7800" size="46">
          <warning-filled />
        </el-icon>
        <div class="ml-23 fw-400 text-17 color-hex-000000 font-leading-35 mt-5">
          客户回答内容与业务要求回答
          <span class="color-hex-FF1919">不一致</span>
          ！是否重新录制！
        </div>
      </div>

      <template #footer>
        <div class="dialog-footer flex-x-center m-y-15">
          <el-button
            class="el-button-size mr-27"
            @click="centerDialogVisible = false"
          >
            否
          </el-button>
          <el-button type="primary" class="el-button-size" @click="rePlayTTS">
            是
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<style scoped lang="scss">
/* 确保在 iOS Safari 上也能全屏显示 */
@supports (-webkit-touch-callout: none) {
  .min-h-screen {
    min-height: -webkit-fill-available;
  }
}
.continersl {
  background: linear-gradient(
    -24deg,
    rgba(239, 223, 235, 0.5),
    rgba(211, 227, 240, 0.5)
  );
}
.hsnr-container {
  :deep(span) {
    @apply fw-400 text-18 color-hex-333333 font-leading-35;
  }
}
.absolute-center {
  @apply absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2;
}
.khtx {
  background: url("@/assets/<EMAIL>") 100% no-repeat;
  @apply w-44 h-44;
  background-size: contain;
}
.el-button-size {
  @apply w-127 h-47 text-18;
}
.time-container {
  @apply flex  p-y-10 font-leading-16  w-full flex-row p-x-24;
}

.time {
  @apply fw-400 text-16 text-left;
}

.timesub {
  @apply fw-400 text-16 color-hex-333333 font-leading-16 text-left ml-8;
}
.line {
  width: 100%;
  height: 0;
  border-top: 1px solid var(--el-border-color);
}
</style>
