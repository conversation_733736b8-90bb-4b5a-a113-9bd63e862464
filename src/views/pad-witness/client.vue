<script lang="ts" setup>
import VoiceAssistantIcon from "@/components/remoteWitness/VoiceAssistantIcon.vue";
import { DEFAULTS } from "@/config/voice-assistant";
import { useClientWaitStore } from "@/store";
import useRemoteWitnessStore from "@/store/modules/remoteWitness";
import WebSocketSingleton from "@/utils/WebSocketSingleton";
import { Check } from "@element-plus/icons-vue";
import { Client } from "@stomp/stompjs";
import TRTC from "trtc-sdk-v5";
import { ref } from "vue";
// use vite

import ClientApi from "@/api/client";
import { WebSocketAction } from "@/enums/WebSocketActionEnum";
import { Edit } from "@element-plus/icons-vue";
import { ElLoading } from "element-plus";
import { isEmpty } from "lodash-es";

const clientWaitStore = useClientWaitStore();

const showSettingDialog = ref(false);
const router = useRouter();
const ttssub = ref();
const showAIDetectionTip = ref(false); // 控制智能检测提示框显示
const aiDetectionMessage = ref(""); // 检测提示内容
const isWaterTTS = ref(true); //是否开启tts字幕，会增加性能消耗，导致视频卡顿，依赖客户端性能。
// const activenotshow = computed(() => {
//   if (isWaterTTS.value) {
//     return {
//       top: "-9999px",
//       left: "-9999px",
//     };
//   }
//   return null;
// });
// watch(
//   () => isWaterTTS.value,
//   (newval) => {
//     if (newval) {
//       startWaterMark();
//     } else {
//       trtc.updateLocalVideo({
//         option: {
//           cameraId: store.videoDeviceId,
//         },
//       });
//     }
//   }
// );
const loading = ref(false);
const showDialog = ref(false); // 控制弹窗显示
let ttsPlayering = ref<boolean>(false);
let asring = ref<boolean>(false);
let webAudioSpeechRecognizer: any = null;

const route = useRoute();
const store = useRemoteWitnessStore();
const voiceMic = ref();
const trtc = TRTC.create();
const roomId = store.roomId;
let socket: Client | null = null;
const sdkAppId = store.sdkAppId;
const userId = store.userId;
const userSig = store.userSig;
const khhd = ref("");
const animationFrameId = ref<number | null>(null);
let nowPlayIndex = ref(0);
const subtitles = ref([]);
let cacheClear = null;
const serverAgentInfo = ref<Record<string, string>>(); //见证人员信息

const videoTimekeep = ref("00:00:00");
// 定义计时器变量
let seconds = 0;
let minutes = 0;
let hours = 0;

// 更新计时器显示
function updateVideoTimekeep() {
  seconds++;
  if (seconds === 60) {
    seconds = 0;
    minutes++;
  }
  if (minutes === 60) {
    minutes = 0;
    hours++;
  }
  // 格式化时间
  const formattedTime = `${hours.toString().padStart(2, "0")}:${minutes.toString().padStart(2, "0")}:${seconds.toString().padStart(2, "0")}`;
  // 更新计时器显示
  videoTimekeep.value = formattedTime;
}

// 计时器变量
let videoTimekeepInterval: number | null = null;

// 开始计时功能
const startVideoTimekeep = () => {
  ElMessage.info("远程坐席开始录制视频");
  // 重置计时器
  seconds = 0;
  minutes = 0;
  hours = 0;
  videoTimekeep.value = "00:00:00";

  // 清除可能存在的旧定时器
  if (videoTimekeepInterval) {
    clearInterval(videoTimekeepInterval);
  }

  // 启动新定时器
  videoTimekeepInterval = window.setInterval(updateVideoTimekeep, 1000);
};

// 停止计时功能
const stopVideoTimekeep = () => {
  ElMessage.info("远程坐席结束录制视频");
  if (videoTimekeepInterval) {
    clearInterval(videoTimekeepInterval);
    videoTimekeepInterval = null;
  }
};

// 保留点击事件以便UI交互，但不再启动计时器
const onVideoTimekeepClick = () => {
  console.log("视频计时显示：", videoTimekeep.value);
};

/**
 * 返回数据给上层
 * @param code 见证结果，1，成功，0，失败，-1,，意外结束见证
 * @param note 见证结果说明
 * @param agentId
 * @param agentName
 */
async function returnParent(
  code: number,
  note: string,
  agentId?: string,
  agentName?: string
) {
  console.info("返回数据给上层", code, note, agentId, agentName);
  window.parent.postMessage(
    JSON.stringify({
      // 见证结果，1，成功，0，失败，-1,，意外结束见证
      code,
      // 见证结果说明
      note,
      // 见证流水号（也就是上一代见证系统的排队房间号）
      witnessId: clientWaitStore.pwaitId,
      // 见证人的userid
      agentId,
      // 见证人的名称
      agentName,
    }),
    "*"
  );
}
let pwaitTimer: any; // 排队请求状态获取定时器
pwaitTimer = setInterval(getPwaitStatus, 2000);
async function getPwaitStatus() {
  const data = await clientWaitStore.getPwaitStatus();
  if (isEmpty(serverAgentInfo.value)) {
    const res = await ClientApi.queryUserById({
      userId: data.serveAgentId,
    });
    serverAgentInfo.value = res.userInfo[0];
  }
  // 判断data.state：-1，客户主动取消请求；-2，无可服务坐席在线；-3，其他原因导致见证请求终止。如上三个状态都表示此次见证已结束，客户端需停止轮询。
  if (data.state === -1 || data.state === -2 || data.state === -3) {
    clearInterval(pwaitTimer);
    pwaitTimer = null;
    if (data.state == -1) {
      console.log("取消请求");
    } else if (data.state == -2) {
      ElMessage({
        message: "无可服务坐席在线",
        type: "warning",
      });
    } else if (data.state == -3) {
      ElMessage({
        message: data.reason || "其他错误",
        type: "warning",
      });
    }
    router.back();
    return;
  }

  // data.state为0且data.serveAgentId为空，表示此时正在等待见证
  // if (data.state === 0 && !data.serveAgentId) {
  //   console.log("等待中");
  // }

  // data.state为1且data.serveAgentId不为空，表示已被坐席接受，前端可创建视频通话连接
  if (data.state === 1 && data.serveAgentId) {
    if (data.result !== undefined && data.result !== null) {
      // data.result不为空，表示坐席已完成视频见证，客户端需停止轮询。
      // data.result为见证结果，true为成功，false为失败，data.reason表示见证的结果说明
      if (data.result === "true") {
        console.log("成功了见证");
      } else {
        console.log("见证失败");
      }
      if (pwaitTimer) {
        clearInterval(pwaitTimer);
        pwaitTimer = null;
      }
      await returnParent(
        data.result === "true" ? 1 : 0,
        data.reason,
        data.serveAgentId,
        data.serveAgentName
      );
      await router.push("/pad-witness/pwait");
      ElMessage({
        message: `见证结果:${JSON.stringify(data)}`,
        type: "warning",
      });
    }
  }
}

// 存储摄像头设备列表
const cameraList = ref<MediaDeviceInfo[]>([]);
// 当前摄像头索引
const currentCameraIndex = ref(0);

// 获取摄像头设备列表
const getCameraDevices = async () => {
  try {
    // 获取所有媒体设备
    const devices = await TRTC.getCameraList();
    cameraList.value = devices;
    // 找到当前使用的摄像头索引
    const currentIndex = devices.findIndex(
      (device) => device.deviceId === store.videoDeviceId
    );
    if (currentIndex !== -1) {
      currentCameraIndex.value = currentIndex;
    }
    console.log("可用摄像头列表:", devices);
  } catch (error) {
    console.error("获取摄像头设备失败:", error);
    ElMessage.error("获取摄像头设备失败");
  }
};

// 处理摄像头选择
const handleCameraSelect = async (index: number) => {
  // 如果选择的是当前摄像头，不做任何操作
  if (index === currentCameraIndex.value) {
    return;
  }

  try {
    const selectedCamera = cameraList.value[index];

    // 更新当前摄像头索引
    currentCameraIndex.value = index;

    // 更新store中的摄像头设备ID
    store.videoDeviceId = selectedCamera.deviceId;

    // 停止当前摄像头
    await trtc.stopLocalVideo();

    // 启用新摄像头
    await trtc.startLocalVideo({
      view: "local",
      option: {
        profile: "1080p",
        mirror: false,
        cameraId: selectedCamera.deviceId,
      },
    });

    ElMessage.success(
      `已切换到${selectedCamera.label || `摄像头 ${index + 1}`}`
    );
  } catch (error) {
    console.error("切换摄像头失败:", error);
    ElMessage.error("切换摄像头失败");
  }
};

const showAgentTextInfo = ref(true);
const showAgentInfo = ref(true);
const handleSwitchShowAgentInfo = () => {
  showAgentInfo.value = !showAgentInfo.value;
  if (showAgentInfo.value) {
    showAgentTextInfo.value = true;
  } else {
    showAgentTextInfo.value = false;
  }
};

const handleCloseAgentTextInfo = () => {
  showAgentTextInfo.value = false;
};

const handleCancel = () => {
  console.log("handleCancel");
  // startPlay();
  // startWaterMark();
};

const AUTO_CLOSE_DELAY = 2000; // 2秒后自动关闭
let autoCloseTimer: number | null = null;

const showAIDetectionWithAutoClose = () => {
  // 清除之前的定时器
  if (autoCloseTimer) {
    clearTimeout(autoCloseTimer);
  }

  showAIDetectionTip.value = true;

  // 设置新的定时器
  autoCloseTimer = window.setTimeout(() => {
    showAIDetectionTip.value = false;
  }, AUTO_CLOSE_DELAY);
};
// 播放"嘟"声提示音
const playBeepSound = () => {
  return new Promise<void>((resolve) => {
    // 创建一个音频上下文
    const audioContext = new (window.AudioContext ||
      (window as any).webkitAudioContext)();
    // 创建一个振荡器节点
    const oscillator = audioContext.createOscillator();
    // 创建一个增益节点（用于控制音量）
    const gainNode = audioContext.createGain();

    // 设置振荡器类型为正弦波
    oscillator.type = "sine";
    // 设置频率为1000Hz（典型的"嘟"声频率）
    oscillator.frequency.value = 1000;

    // 连接节点
    oscillator.connect(gainNode);
    gainNode.connect(audioContext.destination);

    // 设置音量（0-1之间）
    gainNode.gain.value = 0.5;

    // 开始播放
    oscillator.start();

    // 0.3秒后停止播放
    setTimeout(() => {
      oscillator.stop();
      // 关闭音频上下文
      setTimeout(() => {
        audioContext.close();
        resolve();
      }, 100);
    }, 300);
  });
};

const triggerVoiceAssistantClick = () => {
  voiceMic.value?.$el.click();
};

async function setupWebSocket(token: string) {
  const ws = WebSocketSingleton.getInstance();

  try {
    // 建立连接
    socket = await ws.connect(token, false);

    console.log("WebSocket connection established.");

    // 订阅消息
    ws.subscribe("/message", (message) => {
      console.log("Received message:", message);
      const result = JSON.parse(message);
      if (result.action === WebSocketAction.TTS_SUB) {
        ttsPlayering.value = true;
        asring.value = false;
        subtitles.value = result.sub;
        //需要区分是否清除之前旧的。
        if (!result.hasSync) {
          ttssub.value.clearSub();
          ttssub.value.startPlay();
        }
      } else if (result.action === WebSocketAction.ASR_START) {
        ttssub.value.clearSub();
        asring.value = true;
        ttsPlayering.value = false;
        khhd.value = result.khhd;
        voiceMic.value.startCountdown();
      } else if (result.action === WebSocketAction.ASR_END) {
        asring.value = false;
      } else if (result.action === WebSocketAction.ASR_FAIL) {
        ttsPlayering.value = true;
        ttssub.value.clearSub();
        subtitles.value = [];
      } else if (result.action === WebSocketAction.START_RECORD) {
        // 开始录制，开始计时
        startVideoTimekeep();
      } else if (result.action === WebSocketAction.STOP_RECORD) {
        // 停止录制，结束计时
        stopVideoTimekeep();
      } else if (result.action === WebSocketAction.FACE_DETECTION_RESULT) {
        console.log("🚀 ~ 检测到异常行为:", result);
        if (result.asrFail) {
          aiDetectionMessage.value =
            '您的回答不准确，请在"嘟"声之后按要求重新回答';
          showAIDetectionWithAutoClose();
          // 先播放"嘟"声，然后再触发语音助手
          playBeepSound().then(() => {
            triggerVoiceAssistantClick();
          });
        } else {
          aiDetectionMessage.value = result.content || "检测到异常行为";
          showAIDetectionWithAutoClose();
        }
      }
      // else if (result.action === WebSocketAction.ASR_FAIL) {
      //   aiDetectionMessage.value = "您的回答不准确，请按要求重新回答";
      //   showAIDetectionWithAutoClose();
      //   triggerVoiceAssistantClick();
      // }
    });
  } catch (error) {
    console.error("WebSocket connection error:", error);
  }
}
async function restartAsr() {
  const ws = WebSocketSingleton.getInstance();
  await ws.publish({
    destination: "/message",

    body: JSON.stringify({
      recipient: store.connectId,
      content: JSON.stringify({
        action: WebSocketAction.ASR_RESTART,
      }),
    }),
  });
}

async function handleEnter() {
  try {
    await trtc.enterRoom({
      roomId: parseInt(store.roomId),
      sdkAppId: parseInt(sdkAppId),
      userId: store.userId,
      userSig: store.userSig,
    });

    trtc.on(TRTC.EVENT.REMOTE_VIDEO_AVAILABLE, handleRemoteVideoAvailable);
    trtc.on(TRTC.EVENT.REMOTE_VIDEO_UNAVAILABLE, handleRemoteVideoUnavailable);

    await trtc.startLocalAudio({
      option: { microphoneId: store.audioDeviceId },
    });
    trtc.setCurrentSpeaker(store.speakerId);
    await trtc.startLocalVideo({
      view: "local",
      option: {
        profile: "1080p",
        mirror: false,
        cameraId: store.videoDeviceId,
      },
    });
    // if (isWaterTTS.value) {
    //   cacheClear = await startWaterMark(); //tts字幕加入到视频流中，性能有限,会引发卡顿。
    // }
  } catch (error: any) {
    ElMessage({
      message: error.message,
      type: "error",
    });
  }
}

async function handleRemoteVideoAvailable(event: any) {
  console.log("[demo] Video Available", event);
  const { userId, streamType } = event;
  try {
    if (streamType === TRTC.TYPE.STREAM_TYPE_MAIN) {
      store.invitedRemoteUsers.push(`${userId}_main`);
      await nextTick();
      await trtc.startRemoteVideo({
        userId,
        streamType,
        view: `remote`,
      });
    } else {
      store.invitedRemoteUsers.push(`${userId}_screen`);
      await nextTick();
      trtc.startRemoteVideo({ userId, streamType, view: `${userId}_screen` });
    }
    console.log(`startRemoteVideo success: [${userId}]`);
  } catch (error: any) {
    console.log(
      `startRemoteVideo failed: [${userId}], error: ${error.message}`
    );
  }
}

async function handleRemoteVideoUnavailable(event: any) {
  console.log("[demo] Video Unavailable", event);
  const { streamType } = event;
  trtc.stopRemoteVideo({ userId: event.userId, streamType });

  if (streamType === TRTC.TYPE.STREAM_TYPE_MAIN) {
    store.invitedRemoteUsers = store.invitedRemoteUsers.filter(
      (userId: string) => userId !== `${event.userId}_main`
    );
    const loading = ElLoading.service({
      lock: true,
      text: "Loading",
      background: "rgba(0, 0, 0, 0.7)",
    });
    setTimeout(async () => {
      loading.close();
      await handleExit();
      await returnParent(-1, "视频意外退出");
    }, clientWaitStore.witnessLoopTime * 2);
  } else {
    store.invitedRemoteUsers = store.invitedRemoteUsers.filter(
      (userId: string) => userId !== `${event.userId}_screen`
    );
  }
}

async function handleExit() {
  try {
    trtc.off(TRTC.EVENT.REMOTE_VIDEO_AVAILABLE, handleRemoteVideoAvailable);
    trtc.off(TRTC.EVENT.REMOTE_VIDEO_UNAVAILABLE, handleRemoteVideoUnavailable);
    await trtc.exitRoom();
    await trtc.stopLocalVideo();
    await trtc.stopLocalAudio();
    // router.push("/");
    // router.back();
    await router.push("/pad-witness/pwait");
  } catch (error: any) {
    ElMessage({
      message: `Exit room failed. Error: ${error.message}`,
      type: "error",
    });
  }
}

// 组件卸载时清理
onBeforeUnmount(async () => {
  cacheClear?.();
  if (autoCloseTimer) {
    clearTimeout(autoCloseTimer);
  }
  if (pwaitTimer) {
    clearInterval(pwaitTimer);
    pwaitTimer = null;
  }
  // 清理计时器
  if (videoTimekeepInterval) {
    clearInterval(videoTimekeepInterval);
    videoTimekeepInterval = null;
  }
  await handleExit();
  const wsSingleton = WebSocketSingleton.getInstance();
  wsSingleton.unsubscribeAll();
  wsSingleton.disconnect();
});

onMounted(async () => {
  // const ws = WebSocketSingleton.getInstance();
  // await store.createInviteLink();
  // const videoTimekeepInterval = setInterval(updateVideoTimekeep, 1000);
  // onUnmounted(() => {
  //   clearInterval(videoTimekeepInterval);
  // });
  await getCameraDevices();
  await setupWebSocket(store.socketToken);
  await handleEnter();
});
</script>
<template>
  <div>
    <div class="relative w-full h-screen">
      <div
        class="fixed top-0 left-0 w-full h-[75px] flex items-center justify-center z-50"
        style="background-color: #000"
      >
        <div class="flex justify-between gap-4 w-full px-4">
          <!--          <div class="flex-1 flex justify-start">-->
          <!--            <el-button-->
          <!--              @click="handleCancel"-->
          <!--              :loading="loading"-->
          <!--              class="ml-4 text-white text-lg font-medium"-->
          <!--              style="-->
          <!--                font-size: 20px;-->
          <!--                color: #fff;-->
          <!--                background-color: #000;-->
          <!--                border-color: #000;-->
          <!--              "-->
          <!--            >-->
          <!--              取消-->
          <!--            </el-button>-->
          <!--          </div>-->
          <div class="flex items-center justify-center flex-1">
            <span
              class="inline-block w-8 h-8 bg-red-500 rounded-full mr-6px"
            ></span>
            <span class="color-white text-20px" @click="onVideoTimekeepClick">
              {{ videoTimekeep }}
            </span>
          </div>
          <div class="color-white flex items-center justify-end flex-1">
            <span
              class="mr-40 cursor-pointer flex-center"
              @click="handleSwitchShowAgentInfo"
            >
              <span class="mr-4">见证人员信息</span>
              <svg-icon
                icon-class="drop-down"
                size="20"
                :class="{ 'transform-rotate-180': !showAgentInfo }"
              />
            </span>
            <el-dropdown trigger="click" @command="handleCameraSelect">
              <span class="cursor-pointer flex items-center color-white">
                <svg-icon icon-class="switch" size="30" />
              </span>
              <template #dropdown>
                <el-dropdown-menu v-if="cameraList.length > 0">
                  <el-dropdown-item
                    v-for="(camera, index) in cameraList"
                    :key="camera.deviceId"
                    :command="index"
                    :class="{ 'bg-blue-100': index === currentCameraIndex }"
                  >
                    <div class="flex items-center gap-2">
                      <el-icon v-if="index === currentCameraIndex">
                        <Check />
                      </el-icon>
                      <span>{{ camera.label || `摄像头 ${index + 1}` }}</span>
                    </div>
                  </el-dropdown-item>
                </el-dropdown-menu>
                <el-dropdown-menu v-else>
                  <el-dropdown-item disabled>没有可用摄像头</el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </div>
          <!--          <el-icon-->
          <!--            size="32"-->
          <!--            @click="showSettingDialog = true"-->
          <!--            class="mr-20 color-white"-->
          <!--          >-->
          <!--            <Setting />-->
          <!--          </el-icon>-->
          <!--          <el-tooltip content="点击切换水印状态" placement="top">-->
          <!--            <el-icon-->
          <!--              size="32"-->
          <!--              @click="isWaterTTS = !isWaterTTS"-->
          <!--              class="mr-20 color-white"-->
          <!--            >-->
          <!--              <Edit />-->
          <!--            </el-icon>-->
          <!--          </el-tooltip>-->
        </div>
      </div>
      <div id="local" class="absolute inset-0"></div>

      <div
        id="remote"
        class="fixed top-85 right-10 h-250 w-320 lg:h-250 lg:w-320 xs:h-160 xs:w-213"
        :class="{ hidden: !showAgentInfo }"
      ></div>

      <div
        class="bg-[rgba(0,0,0,0.8)] text-white rounded-5 w-320 xs:w-213 xs:top-255 absolute right-10 lg:top-345"
        :class="{ hidden: !showAgentTextInfo || !serverAgentInfo }"
      >
        <div class="relative px-14 py-20">
          <div @click="handleCloseAgentTextInfo">
            <svg-icon
              icon-class="close"
              size="20"
              class="absolute right-10 top-10"
            />
          </div>
          <div class="mb-16">见证人员信息</div>
          <div>
            {{ serverAgentInfo?.name }}（工号{{ serverAgentInfo?.userid }}）
          </div>
          <div class="mt-12">
            执业证号
            <span>{{ serverAgentInfo?.zsbh || "--" }}</span>
          </div>
        </div>
      </div>

      <div
        class="fixed h-200 bottom-10 left-1/2 transform -translate-x-1/2 rounded-[15px]"
        style="
          width: calc(100% - 40px);
          background: linear-gradient(
            180deg,
            rgb(0 0 0 / 10%) 0%,
            rgb(0 0 0 / 80%) 100%
          );
          box-shadow: 0 2px 12px rgb(0 0 0 / 20%);
        "
      >
        <div
          id="htmlToCanvasDom"
          class="marquee-container h-full flex items-center relative"
        >
          <div class="h-full flex w-full">
            <tts-subtitle
              v-show="ttsPlayering"
              ref="ttssub"
              :subtitles="subtitles"
            />
            <div v-show="asring" class="w-full flex-center flex-col">
              <div
                class="fw-500 lg:text-44 xs:text-16 color-hex-FFFFFF font-leading-20 w-full flex flex-row flex-center mb-20"
              >
                <div>请您回答：“</div>
                <div class="color-hex-FF7800">
                  {{ khhd }}
                </div>
                <div>”</div>
              </div>

              <VoiceAssistantIcon
                ref="voiceMic"
                :duration="10000"
                :stroke-width="DEFAULTS.STROKE_WIDTH"
                :size="DEFAULTS.SIZE"
                :icon-size="DEFAULTS.ICON_SIZE"
                @click="restartAsr"
              />
            </div>
          </div>
        </div>
      </div>
    </div>
    <!--智能提示-->
    <div
      v-show="showAIDetectionTip"
      class="fixed top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 min-w-400 min-h-150 rounded-[12px] flex items-center gap-10"
      style="background: rgb(220 38 37 / 40%)"
    >
      <div class="w-100 text-right">
        <el-icon size="80" color="#fff"><WarnTriangleFilled /></el-icon>
      </div>
      <div class="color-white flex flex-col items-center flex-auto">
        <span>智能检测</span>
        <span>{{ aiDetectionMessage }}</span>
      </div>
    </div>
    <!--坐席信息确认-->
    <div
      v-show="false"
      class="fixed top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-600 h-400 rounded-[12px] color-white flex flex-col justify-center"
    >
      坐席人员正在确认视频信息，请勿操作！
    </div>

    <!-- 弹窗 -->
    <el-dialog
      v-model="showDialog"
      width="30%"
      :show-close="false"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      :center="true"
      class="dialog-center"
    >
      <div class="flex flex-col items-center gap-4">
        <el-icon class="text-[24px]">
          <edit />
        </el-icon>
        <p class="text-[16px]">坐席人员正在确认视频信息，请勿操作！</p>
      </div>
    </el-dialog>
    <el-dialog
      v-model="showSettingDialog"
      :show-close="false"
      :destroy-on-close="true"
      width="50%"
      @close="showSettingDialog = false"
    >
      <div class="overflow-y-auto flex-center">
        <webrtc-check />
      </div>
    </el-dialog>
  </div>
</template>
