<script setup lang="ts">
import ClientApi from "@/api/client";
import CustomerAPI from "@/api/customer";
import VideoPreview from "@/components/remoteWitness/VideoPreview.vue";
import { HSNR } from "@/config/appConfig";
import { useRemoteWitnessStore } from "@/store";
import { HSNRItem } from "@/types/type";
import { onMounted, ref } from "vue";

const store = useRemoteWitnessStore();

const hsnr = ref<HSNRItem[]>(HSNR);
const mediaUrl = ref("");

const route = useRoute();

async function searchMedia() {
  const roomId = route.query.roomId?.toString() || "";
  const token = route.query.token?.toString() || "";
  const res = await CustomerAPI.searchMedia(roomId, token);
  mediaUrl.value = res.mediaUrl || "";

  const res2 = await ClientApi.getWitness(Number(roomId));
  const markerData = res2.witnessRequest.markerData
    ? JSON.parse(res2.witnessRequest.markerData)
    : {};
  hsnr.value = markerData.hsnr || [];

  // 更新 detectionHistory 数据到 store
  if (
    markerData.detectionHistory &&
    Array.isArray(markerData.detectionHistory)
  ) {
    store.$patch((state) => {
      state.detectionHistory.push(...markerData.detectionHistory);
    });
  }
}

// 在组件挂载时获取必要的数据
onMounted(async () => {
  // 获取登录token
  // const loginData = await AuthAPI.login({
  //   username: "sl001",
  //   password: "abc**123",
  //   captchaKey: "",
  //   captchaCode: "",
  //   mode: "customerSelf",
  // });

  // // 更新store和token
  // // token = loginData;
  // localStorage.setItem(TOKEN_KEY, loginData);
  await searchMedia();
});
</script>

<template>
  <VideoPreview :video-url="mediaUrl" :hsnr="hsnr" :visible="true" />
</template>
