<script setup lang="ts">
import { SizeEnum } from "@/enums/SizeEnum";
import { ThemeEnum } from "@/enums/ThemeEnum";
import defaultSettings from "@/settings";
import { useAppStore, useRemoteWitnessStore, useSettingsStore } from "@/store";
// const $aegis: any = inject("$aegis");
const appStore = useAppStore();
const settingsStore = useSettingsStore();
const remoteStore = useRemoteWitnessStore();

// 初始化remoteWitnessStore，确保在web端也能正确加载设备信息
onMounted(() => {
  remoteStore.initStore();
});
// remoteStore的初始化已在onMounted中通过initStore方法完成
// $aegis.reportEvent({
//   name: "loaded",
//   ext1: "loaded-success",
//   ext2: "webrtcQuickDemoVue3",
// });
const locale = computed(() => appStore.locale);
const size = computed(() => appStore.size as SizeEnum);
const watermarkEnabled = computed(() => settingsStore.watermarkEnabled);

// 明亮/暗黑主题水印字体颜色适配
const fontColor = computed(() => {
  return settingsStore.theme === ThemeEnum.DARK
    ? "rgba(255, 255, 255, .15)"
    : "rgba(0, 0, 0, .15)";
});
</script>

<template>
  <el-config-provider :locale="locale" :size="size">
    <!-- 开启水印 -->
    <el-watermark
      v-if="watermarkEnabled"
      :font="{ color: fontColor }"
      :content="defaultSettings.watermarkContent"
      :z-index="9999"
      class="wh-full"
    >
      <router-view />
    </el-watermark>
    <!-- 关闭水印 -->
    <router-view v-else />
  </el-config-provider>
</template>
