// pad端排队等待使用
import ClientApi from "@/api/client";

export const useClientWaitStore = defineStore("pwait", () => {
  const clientId = ref<any>(); // 客户id
  const bizRequestId = ref<any>(); // ywqqid
  const org = ref<string>("100");
  const connectId = ref<string>(); // 坐席员工id
  const pwaitId = ref<any>(); // 排队id
  const customerToken = ref<string>(""); // 客户ws的token
  const customerSign = ref<string>(""); // 客户腾讯云的sign
  const sdkAppId = ref<string>(""); // 腾讯云的配置
  // 轮询获取排队消息的间隔时长，单位毫秒
  const witnessLoopTime = 2000;

  function init(consumerId: string, ywqqid: string) {
    clientId.value = consumerId;
    bizRequestId.value = ywqqid;
  }

  /**
   * 发起排队
   */
  async function addPwait() {
    const res = await ClientApi.addWitnessRequest({
      clientId: clientId.value,
      org: org.value,
      bizRequestId: bizRequestId.value, // 业务请求id
    });
    pwaitId.value = res.id;
  }
  /**
   * 取消排队
   */
  async function cancelPwait() {
    return await ClientApi.cancelWitnessRequest(pwaitId.value);
  }

  /**
   * 获取当前排队状态
   * @returns
   */
  async function getPwaitStatus() {
    return await ClientApi.getWitnessRequest({
      requestId: pwaitId.value,
    });
  }

  /**
   * 获取客户端ws腾讯连接要用的数据
   */
  async function getClientSocketInfo() {
    const res = await ClientApi.getClientSocketInfo({
      clientId: clientId.value,
    });
    customerToken.value = res.customerToken;
    customerSign.value = res.customerSign;
    sdkAppId.value = res.sdkAppId;
  }

  window.addEventListener("beforeunload", async () => {
    // @todo 这里后续再优化，是否只针对特定路由进行断开操作
    console.log("即将离开页面");
    // 可以在此处执行一些清理操作
    await cancelPwait();
  });
  return {
    pwaitId,
    clientId,
    bizRequestId,
    init,
    addPwait,
    cancelPwait,
    getPwaitStatus,
    getClientSocketInfo,
    sdkAppId,
    customerToken,
    customerSign,
    witnessLoopTime,
  };
});
