export const useAudioStore = defineStore("audio", () => {
  const callAudio = document.getElementById("callAudio");
  let callInterval: any = null;
  const playCallAudio = () => {
    try {
      if (callAudio) {
        (callAudio as HTMLAudioElement).play();
      }
    } catch (e) {
      console.warn("您的浏览器不支持音频播放。", e);
    }
  };
  const playCall = () => {
    if (callInterval != null) {
      clearInterval(callInterval);
    }
    playCallAudio();
    callInterval = setInterval(playCallAudio, 5000);
  };
  const stopCall = () => {
    clearInterval(callInterval);
    callInterval = null;
  };
  return {
    callAudio,
    playCall,
    stopCall,
  };
});
