import AuthAPI, { type LoginData } from "@/api/auth";
import User<PERSON><PERSON>, { type UserInfo } from "@/api/user";
import { TOKEN_KEY } from "@/enums/CacheEnum";
import { resetRouter } from "@/router";
import { store, useRemoteWitnessStoreHook } from "@/store";

export const useUserStore = defineStore("user", () => {
  const user = ref<UserInfo>({
    roles: [],
    perms: [],
  });

  /**
   * 登录
   *
   * @param {LoginData}
   * @returns
   */
  function login(loginData: LoginData) {
    return new Promise<void>((resolve, reject) => {
      AuthAPI.login(loginData)
        .then((data) => {
          localStorage.setItem(TOKEN_KEY, "33"); // Bearer eyJhbGciOiJIUzI1NiJ9.xxx.xxx
          resolve();
        })
        .catch((error) => {
          reject(error);
        });
    });
  }

  // 获取信息(用户昵称、头像、角色集合、权限集合)
  function getUserInfo() {
    return new Promise<UserInfo>((resolve, reject) => {
      UserAPI.getInfo()
        .then((data) => {
          if (!data) {
            reject("Verification failed, please Login again.");
            return;
          }
          if (!data.roles || data.roles.length <= 0) {
            reject("getUserInfo: roles must be a non-null array!");
            return;
          }
          Object.assign(user.value, { ...data });
          const useRemoteWitnessStore = useRemoteWitnessStoreHook();

          useRemoteWitnessStore.$patch({
            userId: data.userid,
            sdkAppId: data.SDKAppID,
            agentInfoMessage: `上海证券有限责任公司工号${data.userid}为您服务。您可通过我们公司网站或 4008918918服务热线来核实我的信息或执业资格。`,
          });
          // const store = useRemoteWitnessStore();
          // store.$patch({
          //   userId: getParamKey("userId"),
          //   roomId: getParamKey("roomId"),
          // });
          resolve(data);
        })
        .catch((error) => {
          reject(error);
        });
    });
  }

  // user logout
  function logout() {
    return new Promise<void>((resolve, reject) => {
      AuthAPI.logout()
        .then(() => {
          // document.cookie =
          //   "token=; path=/; expires=Thu, 01 Jan 1970 00:00:00 GMT";
          location.reload(); // 清空路由
          resolve();
        })
        .catch((error) => {
          reject(error);
        });
    });
  }

  // remove token
  function resetToken() {
    return new Promise<void>((resolve) => {
      localStorage.setItem(TOKEN_KEY, "");
      resetRouter();
      resolve();
    });
  }

  return {
    user,
    login,
    getUserInfo,
    logout,
    resetToken,
  };
});

/**
 * 用于在组件外部（如在Pinia Store 中）使用 Pinia 提供的 store 实例。
 * 官方文档解释了如何在组件外部使用 Pinia Store：
 * https://pinia.vuejs.org/core-concepts/outside-component-usage.html#using-a-store-outside-of-a-component
 */
export function useUserStoreHook() {
  return useUserStore(store);
}
