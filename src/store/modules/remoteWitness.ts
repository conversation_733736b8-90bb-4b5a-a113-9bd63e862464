import ClientApi from "@/api/client";
import remoteWitnessApi from "@/api/remote-witness";
import User<PERSON><PERSON> from "@/api/user";
import { Status } from "@/config/appConfig";
import { store } from "@/store";
import { FaceDetectionResult } from "@/types/FaceDetectionResult";
import {
  ClientUserBusinessSummaryItem,
  RecordFileInfoItem,
  YwqqyscsItem,
  YxrwmxItem,
  ZsrwjgItem,
} from "@/types/remote-witness";
import { HSNRItem } from "@/types/type";
import { ClientUserInfoType } from "@/views/remote-witness/type";
import { cloneDeep } from "lodash-es";
import { defineStore } from "pinia";

interface Log {
  type: "success" | "failed";
  content: string;
}

interface DetectionResults {
  idCardPhotoDetection: boolean | null; // 客户证件照片检测
  livePhotoDetection: boolean | null; // 客户现场照片检测
  policeVerification: boolean | null; // 公安核查
  videoDetection: boolean | null; // 视频检测
  customerInfoDetection: boolean | null; // 客户信息检测
  customerName: boolean | null; // 客户名称
  idCardNumber: boolean | null; // 证件号码
  idCardExpiry: boolean | null; // 证件有效期
}

//TODO: 多个业务请求 id 的情况处理,影像助审等等这样是否符合？
function getFirstBizRequestId(bizRequestId: string) {
  return bizRequestId.split(";")[0];
}

const initState = {
  sdkAppId: "",
  userId: "",
  roomId: "",
  userSig: "",
  connectId: "",
  socketToken: "",
  audioDeviceId: "",
  videoDeviceId: "",
  speakerId: "",
  cameraList: [] as any[],
  microphoneList: [] as any[],
  speakerList: [] as any[],
  logs: [] as Log[],
  remoteUsersViews: [] as string[],
  invitedRemoteUsers: [] as string[],
  elapsedSeconds: 0,
  detectionHistory: [] as FaceDetectionResult[],
  hsnr: [] as HSNRItem[],
  /** 最终选择的话术（包含特殊话术） */
  finalSpeeches: [] as any[],
  /** 转换后的anychat ttsInfo格式 */
  ttsInfo: [] as any[],
  /** 添加通话状态，默认为空闲 */
  callStatus: Status.Idle,
  /** 录制文件列表 */
  recordFileInfoList: [] as RecordFileInfoItem[],
  /** 揭示信息 */
  revealInfo: "",
  /** 坐席信息 */
  agentInfoMessage: "",

  /** 客户信息相关数据 */
  businessData: {} as ClientUserInfoType,
  businessDataSupplement: {} as ClientUserInfoType,
  businessDataArray: [] as ClientUserInfoType[],
  businessDataSupplementArray: [] as ClientUserInfoType[],

  clientUserBusinessSummary: [] as ClientUserBusinessSummaryItem[],
  tellerInfo: {} as Record<string, string>,
  /** 证件照切片 filepath */
  idCardPartFilepath: [] as Record<string, any>[],
  /** 影像任务明细 */
  yxrwmx: [] as YxrwmxItem[],
  /** 助审任务结果 */
  zsrwjg: [] as ZsrwjgItem[],
  /** 业务请求要素 */
  ywqqyscs: [] as Array<Array<YwqqyscsItem>>,

  /** 退回原因数据 */
  returnReasons: {
    fqcs: "",
    scthyy: "",
    ywdm: "",
  },

  /** 添加已查阅的tab状态 */
  viewedTabs: new Set<string>(),

  /** 检测结果字段 */
  detectionResults: {
    /** 客户证件照片检测 */
    idCardPhotoDetection: null,
    /** 客户现场照片检测 */
    livePhotoDetection: null,
    /** 公安核查 */
    policeVerification: null,
    /** 视频检测 */
    videoDetection: null,
    /** 客户信息检测 */
    customerInfoDetection: null,
    /** 客户名称 */
    customerName: null,
    /** 证件号码 */
    idCardNumber: null,
    /** 证件有效期 */
    idCardExpiry: null,
  } as DetectionResults,
  /** 公安图 filepath */
  gongAnFilepath: "",
  /** 证件照 filepath */
  idCardFilepath: "",
  /** 现场图 base64 数据 */
  photoOfTheScene: "",
};
export const useRemoteWitnessStore = defineStore("remoteWitnessStore", {
  state: () => cloneDeep(initState),

  actions: {
    // 初始化方法，确保在web端也能正确调用
    initStore() {
      console.log("初始化remoteWitnessStore");
      // 从本地存储中获取设备ID
      this.getDeviceFromStorage();
    },
    getInitParamsStates() {
      return !!(this.sdkAppId && this.roomId && this.userId);
    },
    async getUserSig() {
      return this.userSig || (await UserAPI.getGenSign());
    },

    clearRoomState() {
      // 保存需要保留的设备设置
      const preservedSettings = {
        sdkAppId: this.sdkAppId,
        userId: this.userId,
        userSig: this.userSig,
        socketToken: this.socketToken,
        audioDeviceId: this.audioDeviceId,
        videoDeviceId: this.videoDeviceId,
        speakerId: this.speakerId,
        cameraList: this.cameraList,
        microphoneList: this.microphoneList,
        speakerList: this.speakerList,
      };

      // 恢复到初始状态（使用深拷贝避免引用共享）
      Object.assign(this, cloneDeep(initState));

      // 恢复保留的设置
      Object.assign(this, preservedSettings);
    },

    // 从本地存储中获取设备ID
    getDeviceFromStorage() {
      const videoId = localStorage.getItem("videoDeviceId");
      const audioId = localStorage.getItem("audioDeviceId");
      const speakerId = localStorage.getItem("speakerId");

      if (videoId) this.videoDeviceId = videoId;
      if (audioId) this.audioDeviceId = audioId;
      if (speakerId) {
        this.speakerId = speakerId;
      }
    },

    // 保存设备ID到本地存储
    saveDeviceToStorage() {
      localStorage.setItem("videoDeviceId", this.videoDeviceId);
      localStorage.setItem("audioDeviceId", this.audioDeviceId);
      localStorage.setItem("speakerId", this.speakerId);
    },

    updateDetectionHistory(historyItem: FaceDetectionResult) {
      this.detectionHistory.push(historyItem);
    },
    async getHsnr(bizType?: string, bizCode?: string) {
      // 根据业务代码获取话术，没有就根据业务类型获取话术
      console.log("获取话术参数:", { bizType, bizCode });

      // 根据业务代码和业务类型来获取对应的话术
      const data = await ClientApi.getScriptConfig({
        bizType,
        bizCode,
      });
      if (data?.configuration) {
        this.hsnr = JSON.parse(data?.configuration?.scriptConfig || "[]") || [];
      } else {
        const data = await ClientApi.getScriptConfig({
          bizType,
        });
        this.hsnr = JSON.parse(data?.configuration?.scriptConfig || "[]") || [];
      }
    },
    async getRevealInfo(bizCode: string, initiateChannel: number) {
      const data = await ClientApi.getCoworkScriptList({
        businessCode: bizCode,
        initiateChannel: initiateChannel,
        pageNumber: 1,
        pageSize: 1,
      });
      this.revealInfo = data.records[0].script;
    },

    // 提交检测结果
    submitDetectionResult({
      idCardPhotoDetection,
      livePhotoDetection,
      policeVerification,
      videoDetection,
      customerInfoDetection,
      customerName,
      idCardNumber,
      idCardExpiry,
    }: Partial<DetectionResults>) {
      // 更新检测结果
      if (idCardPhotoDetection !== undefined) {
        this.detectionResults.idCardPhotoDetection = idCardPhotoDetection;
      }
      if (livePhotoDetection !== undefined) {
        this.detectionResults.livePhotoDetection = livePhotoDetection;
      }
      if (policeVerification !== undefined) {
        this.detectionResults.policeVerification = policeVerification;
      }
      if (videoDetection !== undefined) {
        this.detectionResults.videoDetection = videoDetection;
      }
      if (customerInfoDetection !== undefined) {
        this.detectionResults.customerInfoDetection = customerInfoDetection;
      }
      if (customerName !== undefined) {
        this.detectionResults.customerName = customerName;
      }
      if (idCardNumber !== undefined) {
        this.detectionResults.idCardNumber = idCardNumber;
      }
      if (idCardExpiry !== undefined) {
        this.detectionResults.idCardExpiry = idCardExpiry;
      }
    },
    submitGongAnFilepath(filepath: string) {
      this.gongAnFilepath = filepath;
    },
    submitIdCardFilepath(filepath: string) {
      this.idCardFilepath = filepath;
    },

    /**
     * 提交现场图base64数据
     */
    submitPhotoOfTheScene(base64: string) {
      this.photoOfTheScene = base64;
    },

    /**
     * 获取客户业务信息
     * @param bizRequestId 业务请求ID，支持多个ID用分号分隔，如'1;2;3'
     */
    async fetchBusinessInfo(bizRequestId: string) {
      try {
        // 检查是否包含多个ID（用分号分隔）
        if (bizRequestId.includes(";")) {
          const ids = bizRequestId.split(";").filter((id) => id.trim() !== "");
          const results = await Promise.all(
            ids.map((id) => remoteWitnessApi.getBusinessData(id.trim()))
          );

          // 将所有结果的BusinessData收集到数组中
          this.businessDataArray = results
            .map((data) => data.BusinessData)
            .filter(Boolean);

          // 将所有结果的BusinessDataSupplement收集到数组中
          this.businessDataSupplementArray = results
            .map((data) => data.BusinessDataSupplement || data.BusinessData)
            .filter(Boolean);

          // 保持向后兼容，使用第一个结果作为默认值
          const firstResult = results[0];
          this.businessData = firstResult.BusinessData;
          this.businessDataSupplement =
            firstResult.BusinessDataSupplement || firstResult.BusinessData;
        } else {
          const data = await remoteWitnessApi.getBusinessData(bizRequestId);
          this.businessDataSupplement =
            data.BusinessDataSupplement || data.BusinessData;
          this.businessData = data.BusinessData;

          // 单个业务时，也要填充数组
          this.businessDataArray = [data.BusinessData];
          this.businessDataSupplementArray = [
            data.BusinessDataSupplement || data.BusinessData,
          ];
        }
      } catch (error) {
        console.error("获取客户业务信息失败:", error);
        throw error;
      }
    },

    /**
     * 获取业务摘要信息
     * @param bizRequestId 业务请求ID，支持多个ID用分号分隔，如'1;2;3'
     */
    async fetchBusinessSummary(bizRequestId: string) {
      try {
        // 检查是否包含多个ID（用分号分隔）
        if (bizRequestId.includes(";")) {
          const ids = bizRequestId.split(";").filter((id) => id.trim() !== "");
          const results = await Promise.all(
            ids.map((id) => remoteWitnessApi.getBusinessSummary(id.trim()))
          );
          // 将所有结果的businessSummary[0]收集到数组中
          this.clientUserBusinessSummary = results
            .map((data) => data?.[0])
            .filter(Boolean);
        } else {
          const data = await remoteWitnessApi.getBusinessSummary(bizRequestId);
          this.clientUserBusinessSummary = data;
        }

        // 获取业务摘要信息后，自动获取退回原因
        await this.fetchReturnReasons();
      } catch (error) {
        console.error("获取业务摘要信息失败:", error);
        throw error;
      }
    },

    /**
     * 获取退回原因
     * 使用业务摘要信息中的khh和ywdm来获取退回原因
     */
    async fetchReturnReasons() {
      try {
        if (
          !this.clientUserBusinessSummary ||
          this.clientUserBusinessSummary.length === 0
        ) {
          console.warn("业务摘要信息为空，无法获取退回原因");
          return;
        }

        // 提取所有的ywdm并去重
        const ywdmSet = new Set<string>();

        this.clientUserBusinessSummary.forEach((summary) => {
          const { khh, ywdm } = summary;
          if (khh && ywdm) {
            ywdmSet.add(ywdm);
          } else {
            console.warn("业务摘要信息中缺少khh或ywdm字段:", summary);
          }
        });

        if (ywdmSet.size === 0) {
          console.warn("没有有效的业务代码，无法获取退回原因");
          return;
        }

        // 将去重后的ywdm用分号分隔
        const ywdmString = Array.from(ywdmSet).join(";");

        // 使用第一个khh发起请求（因为API可能需要khh参数，但实际以ywdm为准）
        const firstKhh = this.clientUserBusinessSummary[0].khh;

        try {
          const result = await remoteWitnessApi.cxkhdrywfqcsjthyy(
            firstKhh,
            ywdmString
          );

          // 处理返回的数据
          if (result && Array.isArray(result)) {
            this.returnReasons = result[0];
            console.log("获取退回原因成功:", this.returnReasons);
          } else {
            this.returnReasons = {
              fqcs: "",
              scthyy: "",
              ywdm: "",
            };
            console.log("未获取到退回原因数据");
          }
        } catch (error) {
          console.error(`获取退回原因失败，ywdm: ${ywdmString}:`, error);
          this.returnReasons = {
            fqcs: "",
            scthyy: "",
            ywdm: "",
          };
        }
      } catch (error) {
        console.error("获取退回原因失败:", error);
        this.returnReasons = {
          fqcs: "",
          scthyy: "",
          ywdm: "",
        };
      }
    },

    /**
     * 获取影像任务明细
     */
    async fetchYxrwmx(bizRequestId: string) {
      const data = await remoteWitnessApi.getYxrwmxByYwqqid({
        ywqqid: String(getFirstBizRequestId(bizRequestId)),
        rwlx: 3,
      });
      this.yxrwmx = data.records || [];
    },

    /**
     * 获取助审任务结果
     */
    async fetchZsrwjg(bizRequestId: string) {
      const data = await remoteWitnessApi.getZsrwjgByYwqqid({
        ywqqid: String(getFirstBizRequestId(bizRequestId)),
        rwlx: 3,
      });
      this.zsrwjg = data.records || [];
    },

    /**
     * 查询业务请求要素
     * @param ywdmArray 业务代码数组
     */
    async fetchYwqqyscs(ywdmArray: string[]) {
      if (!Array.isArray(ywdmArray) || ywdmArray.length === 0) {
        this.ywqqyscs = [];
        return;
      }

      const data = await Promise.all(
        ywdmArray.map((item) =>
          remoteWitnessApi.queryYwqqyscs({ ywdm: String(item) })
        )
      );
      this.ywqqyscs = data;
    },

    /**
     * 标记tab为已查阅
     * @param tabName tab名称
     */
    markTabAsViewed(tabName: string) {
      this.viewedTabs.add(tabName);
    },

    /**
     * 检查是否所有tab都已查阅
     * @param allTabNames 所有tab名称数组
     * @returns 是否全部查阅
     */
    areAllTabsViewed(allTabNames: string[]): boolean {
      return allTabNames.every((tabName) => this.viewedTabs.has(tabName));
    },

    /**
     * 获取未查阅的tab数量
     * @param allTabNames 所有tab名称数组
     * @returns 未查阅的tab数量
     */
    getUnviewedTabCount(allTabNames: string[]): number {
      return allTabNames.filter((tabName) => !this.viewedTabs.has(tabName))
        .length;
    },
  },

  getters: {
    formattedTime: (state) => {
      return formattedTime(state.elapsedSeconds);
    },
  },
});
export function formattedTime(elapsedSeconds: number) {
  const minutes = String(Math.floor(elapsedSeconds / 60)).padStart(2, "0");
  const seconds = String(elapsedSeconds % 60).padStart(2, "0");
  return `${minutes}:${seconds}`;
}
export function useRemoteWitnessStoreHook() {
  return useRemoteWitnessStore(store);
}

export default useRemoteWitnessStore;
