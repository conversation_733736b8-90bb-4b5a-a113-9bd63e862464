import { useRemoteWitnessStore, useUserStore } from "@/store";
import { useAudioStore } from "@/store/modules/audio";
import { ApexQueueCowork, Constants } from "agent-queue-ts-websdk";

interface CustomerRequestData {
  id: string;
  jid: string;
  timeout: string;
  khfsmc: string;
  khxm: string;
  workgroup: string;
  ywqqid: string;
  "voice-url": string;
  customerInfo: string;
  type: string;
  "external-video-proxy": string;
  userID: string;
  H5: string;
  version: string;
  url: string;
  khdj: string;
  question_ex: string;
  "h5-video-port": string;
  khfs: string;
  "video-ip": string;
  anonymousUser: string;
  khzjbh: string;
  "video-port": string;
  username: string;
}

export const usePwaitStore = defineStore("pwait", () => {
  type PwaitState = "wait" | "confirmConnect";
  const pwaitState = ref<PwaitState>("wait");
  let disconnectedBySelf: boolean = false;
  const bizRequestId = ref<string>("481170;479666"); //481170 、 478978
  const centerUrl = ref<string>(""); //中间区域的 url 链接
  const witnessRequestId = ref<string | number>(); // 见证请求id 就是 roomId
  const nSubCode = ref<CustomerRequestData>();
  const remoteWitnessStore = useRemoteWitnessStore();
  const waiterNum = ref<number>(0);
  const bizType = ref<string>(""); // 业务类型
  const initiateChannel = ref<number>(0); // 发起渠道
  const bizCode = ref<string>(""); // 业务代码
  let notify: Notification | null = null;

  Notification.requestPermission().then(function (result) {
    if (result === "denied") {
      console.log("拒绝显示系统通知");
      return;
    }
    if (result === "default") {
      console.log("默认");
      return;
    }
    console.log("允许显示系统通知");
  });

  const audioStore = useAudioStore();
  const startNotify = () => {
    notify = new Notification("新客户请求", {
      body: "新的客户呼叫\n来自排队服务器",
      requireInteraction: true,
    });
    notify.onclick = () => {
      window.focus();
      console.log("notification click");
    };
    audioStore.playCall();
  };

  const stopNotify = () => {
    if (notify) {
      notify.close();
    }
    audioStore.stopCall();
  };

  function changePwaitState(state: PwaitState) {
    pwaitState.value = state;
  }

  const userStore = useUserStore();
  const router = useRouter();

  /**
   * SDK 初始化和登录
   */
  async function initAndLogin() {
    disconnectedBySelf = false;
    const queueServiceUrlFromEnv = import.meta.env.VITE_APP_QUEUE_SERVICE_URL;
    if (!queueServiceUrlFromEnv) {
      console.error("VITE_APP_QUEUE_SERVICE_URL is not defined in .env file");
      ElMessage({ message: "排队服务地址未配置", type: "error" });
      return;
    }
    const wsUrl = queueServiceUrlFromEnv as string;

    console.log(
      "🚀 ~ Pwait Event initAndLogin ~ clientConfig.userStore.user.userid:",
      userStore.user.userid
    );
    const clientConfig = {
      wsURL: wsUrl,
      userId: "zhouxue",
      password: "123456",
    };

    ApexQueueCowork.init(clientConfig);

    ApexQueueCowork.login({
      timeout: 30,
      interval: 30,
      spjzHandler: (data: any) => {
        if (data && data.nState) {
          const nState = data.nState;
          const nSubCode = data.nSubCode;
          const sParam1 = data.sParam1;

          // console.log("Pwait Event: ", data, nState, nSubCode, sParam1);
          //{
          //   "nState": 6,
          //   "nSubCode": "连接失败",
          //   "sParam1": "",
          //   "sParam2": ""
          // }

          switch (nState) {
            case Constants.STATE_LOGINFAIL:
              ElMessage({
                message: `排队服务登录失败: ${nSubCode} - ${sParam1}`,
                type: "error",
              });
              break;
            case Constants.STATE_LOGINOK:
              ElMessage({ message: "排队服务登录成功", type: "success" });
              ApexQueueCowork.agentStatus();
              break;
            case Constants.STATE_NOTIFY_QUEUE:
              if (
                nSubCode &&
                typeof nSubCode === "object" &&
                nSubCode !== null &&
                "count" in nSubCode
              ) {
                waiterNum.value = (nSubCode as { count: number }).count;
              }
              break;
            case Constants.STATE_RECIEVE_CUSTOMER_REQUESTING:
              console.log(
                "排队成功Pwait Event: ",
                data,
                "nState:",
                nState,
                "nSubCode:",
                nSubCode,
                "sParam1:",
                sParam1
              );
              if (
                nSubCode &&
                typeof nSubCode === "object" &&
                nSubCode !== null
              ) {
                nSubCode.value = nSubCode as CustomerRequestData;
                bizRequestId.value = nSubCode.ywqqid;
                // clientId.value = nSubCode.userID;
                witnessRequestId.value = nSubCode.id;
                centerUrl.value = nSubCode.url;
                bizType.value = nSubCode.type;
                initiateChannel.value = nSubCode.initiateChannel;
                bizCode.value = nSubCode.ywdm;
                remoteWitnessStore.$patch({
                  tellerInfo: {
                    khjlid: nSubCode.khjlid,
                    khjlmc: nSubCode.khjlmc,
                  },
                });
              }
              changePwaitState("confirmConnect");
              startNotify();
              break;
            case Constants.STATE_RECIEVE_CUSTOMER_CANCELQUEUE:
              console.log(
                "Pwait Event: ",
                data,
                "nState:",
                nState,
                "nSubCode:",
                nSubCode,
                "sParam1:",
                sParam1
              );
              changePwaitState("wait");
              stopNotify();
              ElMessage({ message: "客户已取消排队", type: "info" });
              break;
            case Constants.STATE_NO_NOTIFY_QUEUE:
              ElMessage({ message: "该坐席没有可服务的队列", type: "warning" });
              break;
            case Constants.STATE_OFFLINE:
              if (!disconnectedBySelf) {
                ElMessage({ message: "与排队服务连接已断开", type: "error" });
                router.push("/remote-witness/wait");
              }
              stopNotify();
              break;
            case Constants.STATE_LOGIN_ON_OTHER:
              ElMessage({
                message: "该坐席已在其他地方登录排队服务",
                type: "error",
              });
              disconnectedBySelf = true;
              router.push("/remote-witness/wait");
              stopNotify();
              break;
            default: {
              // if (nState === 991) {
              //   if (!notify) {
              //     changePwaitState("confirmConnect");
              //     startNotify();
              //   }
              // }
              // console.log("Unhandled Pwait Event: ", nState, nSubCode, sParam1);
              break;
            }
          }
        }
      },
      error: (error: any) => {
        ElMessage({ message: `排队服务连接错误: ${error}`, type: "error" });
        if (!disconnectedBySelf) {
          console.warn("排队服务连接错误，未由用户主动断开。错误详情: ", error);
        }
      },
    });
  }

  /**
   * 修改坐席状态 (示闲, 示忙/学习)
   * @param status - "idle" or "dnd" (Do Not Disturb, for busy/study)
   */
  function changeStatus(status: "idle" | "dnd" | "study"): void {
    if (status === "idle") {
      ApexQueueCowork.agentStatus();
    } else {
      ApexQueueCowork.agentStatusDnd();
    }
  }

  /**
   * 坐席接受客户的见证请求
   */
  function acceptCall() {
    ApexQueueCowork.offerAccept();
    stopNotify();
  }

  /**
   * 坐席拒绝客户的见证请求
   */
  function rejectCall() {
    ApexQueueCowork.offerReject();
    changePwaitState("wait");
    stopNotify();
    ElMessage({ message: "已拒绝客户请求", type: "info" });
  }

  /**
   * 见证通过
   * @param reason
   */
  const pass = (reason: string) => {
    ApexQueueCowork.videoResult(true, reason);
  };

  /**
   * 见证不通过
   * @param reason
   */
  const unpass = (reason: string) => {
    ApexQueueCowork.videoResult(false, reason);
  };

  /**
   * 签出 (坐席登出)
   */
  const disconnectFromQueue = () => {
    disconnectedBySelf = true;
    ApexQueueCowork.disconnect();
    ElMessage({ message: "已从排队服务签出", type: "info" });
    changePwaitState("wait");
    waiterNum.value = 0;
    router.push("/remote-witness/wait");
  };

  return {
    witnessRequestId,
    waiterNum,
    bizRequestId,
    centerUrl,
    pwaitState,
    nSubCode,
    bizType,
    initiateChannel,
    bizCode,
    changePwaitState,
    init: initAndLogin,
    changeStatus,
    acceptCall,
    rejectCall,
    pass,
    unpass,
    disconnect: disconnectFromQueue,
  };
});
