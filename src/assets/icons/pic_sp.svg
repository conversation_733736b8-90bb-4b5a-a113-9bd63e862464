<?xml version="1.0" encoding="UTF-8"?>
<svg width="59px" height="55px" viewBox="0 0 59 55" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <title>pic_sp</title>
    <defs>
        <linearGradient x1="5.70331551%" y1="164.280045%" x2="5.70331849%" y2="-25.7410765%" id="linearGradient-1">
            <stop stop-color="#0257FF" offset="0%"></stop>
            <stop stop-color="#0257FF" offset="32.7004224%"></stop>
            <stop stop-color="#4E97FF" offset="60.5626464%"></stop>
            <stop stop-color="#3D8DFF" offset="100%"></stop>
        </linearGradient>
        <path d="M42.651779,14.3974411 C42.651779,14.3974411 49.0791018,14.3974411 49.0791018,14.3974411 C49.0791018,14.3974411 66.3334396,32.6665604 66.3334396,32.6665604 C66.3334396,32.6665604 25.3974411,32.6665604 25.3974411,32.6665604 C25.3974411,32.6665604 42.651779,14.3974411 42.651779,14.3974411 Z" id="path-2"></path>
        <filter x="-6.1%" y="-13.7%" width="112.2%" height="127.4%" filterUnits="objectBoundingBox" id="filter-3">
            <feGaussianBlur stdDeviation="2.5" in="SourceAlpha" result="shadowBlurInner1"></feGaussianBlur>
            <feOffset dx="0" dy="0" in="shadowBlurInner1" result="shadowOffsetInner1"></feOffset>
            <feComposite in="shadowOffsetInner1" in2="SourceAlpha" operator="arithmetic" k2="-1" k3="1" result="shadowInnerInner1"></feComposite>
            <feColorMatrix values="0 0 0 0 0.966666639   0 0 0 0 0.992777765   0 0 0 0 1  0 0 0 0.75999999 0" type="matrix" in="shadowInnerInner1"></feColorMatrix>
        </filter>
        <linearGradient x1="2.12614015%" y1="9.59175289%" x2="111.473858%" y2="71.4018851%" id="linearGradient-4">
            <stop stop-color="#8FBCFF" offset="0%"></stop>
            <stop stop-color="#8FBCFF" offset="15.4425964%"></stop>
            <stop stop-color="#CBE0FF" stop-opacity="0.699999988" offset="100%"></stop>
        </linearGradient>
        <path d="M0.000390625,0 C0.000390625,0 46.132207,0 46.132207,0 C46.132207,0 46.132207,42.9821634 46.132207,42.9821634 C46.132207,42.9821634 0.000390625,42.9821634 0.000390625,42.9821634 C0.000390625,42.9821634 0.000390625,0 0.000390625,0 Z" id="path-5"></path>
        <filter x="-17.3%" y="-18.6%" width="143.4%" height="146.5%" filterUnits="objectBoundingBox" id="filter-6">
            <feOffset dx="2" dy="2" in="SourceAlpha" result="shadowOffsetOuter1"></feOffset>
            <feGaussianBlur stdDeviation="3" in="shadowOffsetOuter1" result="shadowBlurOuter1"></feGaussianBlur>
            <feComposite in="shadowBlurOuter1" in2="SourceAlpha" operator="out" result="shadowBlurOuter1"></feComposite>
            <feColorMatrix values="0 0 0 0 0.228489578   0 0 0 0 0.315427095   0 0 0 0 0.445833325  0 0 0 0.150000006 0" type="matrix" in="shadowBlurOuter1"></feColorMatrix>
        </filter>
        <linearGradient x1="33.3494961%" y1="11.2140194%" x2="81.3163042%" y2="80.8357545%" id="linearGradient-7">
            <stop stop-color="#CBE0FF" offset="0%"></stop>
            <stop stop-color="#CBE0FF" stop-opacity="0.839999974" offset="100%"></stop>
        </linearGradient>
        <path d="M0,0 C0,0 46.1318162,0 46.1318162,0 C46.1318162,0 46.1318162,42.9821634 46.1318162,42.9821634 C46.1318162,42.9821634 0,42.9821634 0,42.9821634 C0,42.9821634 0,0 0,0 Z" id="path-8"></path>
        <filter x="-17.3%" y="-18.6%" width="143.4%" height="146.5%" filterUnits="objectBoundingBox" id="filter-10">
            <feOffset dx="2" dy="2" in="SourceAlpha" result="shadowOffsetOuter1"></feOffset>
            <feGaussianBlur stdDeviation="3" in="shadowOffsetOuter1" result="shadowBlurOuter1"></feGaussianBlur>
            <feComposite in="shadowBlurOuter1" in2="SourceAlpha" operator="out" result="shadowBlurOuter1"></feComposite>
            <feColorMatrix values="0 0 0 0 0.228489578   0 0 0 0 0.315427095   0 0 0 0 0.445833325  0 0 0 0.150000006 0" type="matrix" in="shadowBlurOuter1"></feColorMatrix>
        </filter>
        <filter x="-67.1%" y="-70.3%" width="234.2%" height="240.6%" filterUnits="objectBoundingBox" id="filter-11">
            <feGaussianBlur stdDeviation="10.8600448" in="SourceGraphic"></feGaussianBlur>
        </filter>
        <filter x="-12.4%" y="-13.3%" width="124.8%" height="126.6%" filterUnits="objectBoundingBox" id="filter-12">
            <feGaussianBlur stdDeviation="1.56166063" in="SourceGraphic"></feGaussianBlur>
        </filter>
        <linearGradient x1="-11.4714026%" y1="56.4487368%" x2="105.911791%" y2="74.1045967%" id="linearGradient-13">
            <stop stop-color="#FFFFFF" stop-opacity="0" offset="0%"></stop>
            <stop stop-color="#FFFFFF" stop-opacity="0" offset="11.1470543%"></stop>
            <stop stop-color="#FFFFFF" offset="100%"></stop>
        </linearGradient>
        <path d="M0,0 C0,0 46.1318162,0 46.1318162,0 C46.1318162,0 46.1318162,42.9821634 46.1318162,42.9821634 C46.1318162,42.9821634 0,42.9821634 0,42.9821634 C0,42.9821634 0,0 0,0 Z" id="path-14"></path>
        <filter x="-17.3%" y="-18.6%" width="143.4%" height="146.5%" filterUnits="objectBoundingBox" id="filter-15">
            <feOffset dx="2" dy="2" in="SourceAlpha" result="shadowOffsetOuter1"></feOffset>
            <feMorphology radius="1.0118535" operator="erode" in="SourceAlpha" result="shadowInner"></feMorphology>
            <feOffset dx="2" dy="2" in="shadowInner" result="shadowInner"></feOffset>
            <feComposite in="shadowOffsetOuter1" in2="shadowInner" operator="out" result="shadowOffsetOuter1"></feComposite>
            <feGaussianBlur stdDeviation="3" in="shadowOffsetOuter1" result="shadowBlurOuter1"></feGaussianBlur>
            <feColorMatrix values="0 0 0 0 0.228489578   0 0 0 0 0.315427095   0 0 0 0 0.445833325  0 0 0 0.150000006 0" type="matrix" in="shadowBlurOuter1"></feColorMatrix>
        </filter>
        <path d="M-1.54459351,4.83053101 C-1.54459351,4.83053101 6.80674048,4.83053101 6.80674048,4.83053101 C6.80674048,4.83053101 6.80674048,10.092678 6.80674048,10.092678 C6.80674048,10.092678 -1.54459351,10.092678 -1.54459351,10.092678 C-1.54459351,10.092678 -1.54459351,4.83053101 -1.54459351,4.83053101 Z" id="path-16"></path>
        <filter x="-101.8%" y="-142.5%" width="351.5%" height="499.1%" filterUnits="objectBoundingBox" id="filter-17">
            <feOffset dx="2" dy="3" in="SourceAlpha" result="shadowOffsetOuter1"></feOffset>
            <feGaussianBlur stdDeviation="3" in="shadowOffsetOuter1" result="shadowBlurOuter1"></feGaussianBlur>
            <feComposite in="shadowBlurOuter1" in2="SourceAlpha" operator="out" result="shadowBlurOuter1"></feComposite>
            <feColorMatrix values="0 0 0 0 0.20427084   0 0 0 0 0.418111086   0 0 0 0 0.662500024  0 0 0 0.119999997 0" type="matrix" in="shadowBlurOuter1"></feColorMatrix>
        </filter>
        <path d="M13.3953153,4.8261691 C13.3953153,4.8261691 21.7466493,4.8261691 21.7466493,4.8261691 C21.7466493,4.8261691 21.7466493,10.0970399 21.7466493,10.0970399 C21.7466493,10.0970399 13.3953153,10.0970399 13.3953153,10.0970399 C13.3953153,10.0970399 13.3953153,4.8261691 13.3953153,4.8261691 Z" id="path-18"></path>
        <filter x="-101.8%" y="-142.3%" width="351.5%" height="498.4%" filterUnits="objectBoundingBox" id="filter-19">
            <feOffset dx="2" dy="3" in="SourceAlpha" result="shadowOffsetOuter1"></feOffset>
            <feGaussianBlur stdDeviation="3" in="shadowOffsetOuter1" result="shadowBlurOuter1"></feGaussianBlur>
            <feComposite in="shadowBlurOuter1" in2="SourceAlpha" operator="out" result="shadowBlurOuter1"></feComposite>
            <feColorMatrix values="0 0 0 0 0.20427084   0 0 0 0 0.418111086   0 0 0 0 0.662500024  0 0 0 0.119999997 0" type="matrix" in="shadowBlurOuter1"></feColorMatrix>
        </filter>
        <path d="M2.65149888,4.81633483 C2.65149888,4.81633483 17.548118,4.81633483 17.548118,4.81633483 C17.548118,4.81633483 17.548118,10.0802843 17.548118,10.0802843 C17.548118,10.0802843 2.65149888,10.0802843 2.65149888,10.0802843 C2.65149888,10.0802843 2.65149888,4.81633483 2.65149888,4.81633483 Z" id="path-20"></path>
        <filter x="-57.1%" y="-142.5%" width="241.0%" height="498.9%" filterUnits="objectBoundingBox" id="filter-21">
            <feOffset dx="2" dy="3" in="SourceAlpha" result="shadowOffsetOuter1"></feOffset>
            <feGaussianBlur stdDeviation="3" in="shadowOffsetOuter1" result="shadowBlurOuter1"></feGaussianBlur>
            <feColorMatrix values="0 0 0 0 0.20427084   0 0 0 0 0.418111086   0 0 0 0 0.662500024  0 0 0 0.119999997 0" type="matrix" in="shadowBlurOuter1"></feColorMatrix>
        </filter>
    </defs>
    <g id="pc" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="1-3远程见证" transform="translate(-761.000000, -497.000000)">
            <g id="弹窗" transform="translate(710.000000, 413.000000)">
                <g id="pic_sp" transform="translate(55.000000, 88.000000)">
                    <g id="Rectangle-6" opacity="0.889999986" fill-rule="nonzero" transform="translate(45.865440, 23.532001) rotate(-90.000000) translate(-45.865440, -23.532001) ">
                        <use fill="url(#linearGradient-1)" xlink:href="#path-2"></use>
                        <use fill="black" fill-opacity="1" filter="url(#filter-3)" xlink:href="#path-2"></use>
                    </g>
                    <g id="Rectangle-39" fill-rule="nonzero">
                        <use fill="black" fill-opacity="1" filter="url(#filter-6)" xlink:href="#path-5"></use>
                        <use fill-opacity="0.800000012" fill="url(#linearGradient-4)" xlink:href="#path-5"></use>
                    </g>
                    <g id="Mask-group">
                        <mask id="mask-9" fill="white">
                            <use xlink:href="#path-8"></use>
                        </mask>
                        <g id="Rectangle-41" fill-rule="nonzero">
                            <use fill="black" fill-opacity="1" filter="url(#filter-10)" xlink:href="#path-8"></use>
                            <use fill-opacity="0.800000012" fill="url(#linearGradient-7)" xlink:href="#path-8"></use>
                        </g>
                        <ellipse id="Ellipse-7" fill-opacity="0.800000012" fill="#4284FF" fill-rule="nonzero" filter="url(#filter-11)" mask="url(#mask-9)" transform="translate(-7.112195, 1.001088) rotate(5.486657) translate(7.112195, -1.001088) " cx="-7.11219494" cy="1.00108792" rx="24.2783401" ry="23.1802188"></ellipse>
                        <path d="M0,0 C0,0 46.1318162,0 46.1318162,0 C46.1318162,0 46.1318162,42.9821634 46.1318162,42.9821634 C46.1318162,42.9821634 0,42.9821634 0,42.9821634 C0,42.9821634 0,0 0,0 Z" id="Rectangle-40" stroke-opacity="0.800000012" stroke="#E4EBFF" stroke-width="2.05534725" fill-rule="nonzero" filter="url(#filter-12)" mask="url(#mask-9)"></path>
                        <g id="Rectangle-40" fill-rule="nonzero" mask="url(#mask-9)">
                            <use fill="black" fill-opacity="1" filter="url(#filter-15)" xlink:href="#path-14"></use>
                            <path stroke="url(#linearGradient-13)" stroke-width="1.0118535" d="M45.6258895,0.505926752 L45.6258895,42.4762367 L0.505926752,42.4762367 L0.505926752,0.505926752 L45.6258895,0.505926752 Z" stroke-linejoin="square"></path>
                        </g>
                    </g>
                    <g id="Group-28" transform="translate(12.900569, 14.262486)" fill-rule="nonzero">
                        <g id="Rectangle-10" transform="translate(2.631073, 7.461604) scale(1, -1) rotate(-90.000000) translate(-2.631073, -7.461604) ">
                            <use fill="black" fill-opacity="1" filter="url(#filter-17)" xlink:href="#path-16"></use>
                            <use fill-opacity="0.699999988" fill="#FFFFFF" xlink:href="#path-16"></use>
                        </g>
                        <g id="Rectangle-43" transform="translate(17.570982, 7.461604) scale(1, -1) rotate(-90.000000) translate(-17.570982, -7.461604) ">
                            <use fill="black" fill-opacity="1" filter="url(#filter-19)" xlink:href="#path-18"></use>
                            <use fill-opacity="0.699999988" fill="#FFFFFF" xlink:href="#path-18"></use>
                        </g>
                        <g id="Rectangle-42" transform="translate(10.099808, 7.448310) scale(1, -1) rotate(-90.000000) translate(-10.099808, -7.448310) ">
                            <use fill="black" fill-opacity="1" filter="url(#filter-21)" xlink:href="#path-20"></use>
                            <use fill="#FFFFFF" xlink:href="#path-20"></use>
                        </g>
                    </g>
                </g>
            </g>
        </g>
    </g>
</svg>