<?xml version="1.0" encoding="UTF-8"?>
<svg width="46px" height="41px" viewBox="0 0 46 41" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <title>pic_dn</title>
    <defs>
        <filter x="-44.4%" y="-52.2%" width="188.9%" height="204.3%" filterUnits="objectBoundingBox" id="filter-1">
            <feOffset dx="0" dy="2" in="SourceAlpha" result="shadowOffsetOuter1"></feOffset>
            <feGaussianBlur stdDeviation="2" in="shadowOffsetOuter1" result="shadowBlurOuter1"></feGaussianBlur>
            <feColorMatrix values="0 0 0 0 0.0862745098   0 0 0 0 0.466666667   0 0 0 0 1  0 0 0 1 0" type="matrix" in="shadowBlurOuter1" result="shadowMatrixOuter1"></feColorMatrix>
            <feMerge>
                <feMergeNode in="shadowMatrixOuter1"></feMergeNode>
                <feMergeNode in="SourceGraphic"></feMergeNode>
            </feMerge>
        </filter>
        <linearGradient x1="30.4709792%" y1="16.3902803%" x2="-0.868364051%" y2="84.5989154%" id="linearGradient-2">
            <stop stop-color="#277EFA" offset="0%"></stop>
            <stop stop-color="#277EFA" offset="23.8922924%"></stop>
            <stop stop-color="#3D8DFF" stop-opacity="0" offset="100%"></stop>
        </linearGradient>
        <path d="M5.38288198,0 C5.38288198,0 7.57958738,0 7.57958738,0 C7.57958738,0 12.9624694,11.359613 12.9624694,11.359613 C12.9624694,11.359613 0,11.359613 0,11.359613 C0,11.359613 5.38288198,0 5.38288198,0 Z" id="path-3"></path>
        <filter x="-3.9%" y="-4.4%" width="107.7%" height="108.8%" filterUnits="objectBoundingBox" id="filter-4">
            <feGaussianBlur stdDeviation="0.5" in="SourceAlpha" result="shadowBlurInner1"></feGaussianBlur>
            <feOffset dx="0" dy="0" in="shadowBlurInner1" result="shadowOffsetInner1"></feOffset>
            <feComposite in="shadowOffsetInner1" in2="SourceAlpha" operator="arithmetic" k2="-1" k3="1" result="shadowInnerInner1"></feComposite>
            <feColorMatrix values="0 0 0 0 0.966666639   0 0 0 0 0.992777765   0 0 0 0 1  0 0 0 0.75999999 0" type="matrix" in="shadowInnerInner1"></feColorMatrix>
        </filter>
        <linearGradient x1="50%" y1="-3.06161713e-15%" x2="50%" y2="100%" id="linearGradient-5">
            <stop stop-color="#FFFFFF" stop-opacity="0.670000017" offset="0%"></stop>
            <stop stop-color="#FFFFFF" stop-opacity="0.769999981" offset="56.4999998%"></stop>
            <stop stop-color="#FFFFFF" stop-opacity="0.600000024" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="37.0242178%" y1="31.5795976%" x2="69.5231557%" y2="74.5188425%" id="linearGradient-6">
            <stop stop-color="#99D1FF" offset="0%"></stop>
            <stop stop-color="#D1EAFF" stop-opacity="0.560000002" offset="100%"></stop>
        </linearGradient>
        <rect id="path-7" x="0" y="0" width="27" height="17.6658395"></rect>
        <filter x="-46.1%" y="-58.4%" width="192.2%" height="216.7%" filterUnits="objectBoundingBox" id="filter-9">
            <feGaussianBlur stdDeviation="4.09095719" in="SourceGraphic"></feGaussianBlur>
        </filter>
        <filter x="-9.0%" y="-13.7%" width="117.9%" height="127.4%" filterUnits="objectBoundingBox" id="filter-10">
            <feGaussianBlur stdDeviation="0.608634444" in="SourceGraphic"></feGaussianBlur>
        </filter>
        <linearGradient x1="32.5637668%" y1="26.837001%" x2="70.0190663%" y2="73.9327999%" id="linearGradient-11">
            <stop stop-color="#FFFFFF" offset="0%"></stop>
            <stop stop-color="#FFFFFF" stop-opacity="0.600000024" offset="100%"></stop>
        </linearGradient>
        <filter x="-138.8%" y="-68.4%" width="377.5%" height="236.8%" filterUnits="objectBoundingBox" id="filter-12">
            <feGaussianBlur stdDeviation="0.588744444" in="SourceGraphic"></feGaussianBlur>
        </filter>
        <filter x="-124.5%" y="-61.4%" width="349.0%" height="222.7%" filterUnits="objectBoundingBox" id="filter-13">
            <feGaussianBlur stdDeviation="0.588744444" in="SourceGraphic"></feGaussianBlur>
        </filter>
        <linearGradient x1="50%" y1="-3.06161713e-15%" x2="50%" y2="100%" id="linearGradient-14">
            <stop stop-color="#FFFFFF" offset="0%"></stop>
            <stop stop-color="#FFFFFF" stop-opacity="0.730000019" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="50%" y1="-3.06161713e-15%" x2="50%" y2="100%" id="linearGradient-15">
            <stop stop-color="#FFFFFF" offset="0%"></stop>
            <stop stop-color="#FFFFFF" stop-opacity="0.920000017" offset="100%"></stop>
        </linearGradient>
    </defs>
    <g id="pc" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="1-4远程见证" transform="translate(-472.000000, -90.000000)">
            <g id="pic_dn" filter="url(#filter-1)" transform="translate(481.500000, 99.000000)">
                <g id="Group-2" transform="translate(7.101963, 11.640387)" fill-rule="nonzero" opacity="0.889999986">
                    <g id="Rectangle-6">
                        <use fill="url(#linearGradient-2)" xlink:href="#path-3"></use>
                        <use fill="black" fill-opacity="1" filter="url(#filter-4)" xlink:href="#path-3"></use>
                    </g>
                    <path d="M7.42140487,0.25 L12.5673562,11.109613 L0.395113206,11.109613 L2.62948171,6.39437654 L3.061483,5.48271482 L5.54106449,0.25 L7.42140487,0.25 Z" id="Rectangle-7" stroke="url(#linearGradient-5)" stroke-width="0.5"></path>
                </g>
                <rect id="Rectangle-12" fill="url(#linearGradient-6)" fill-rule="nonzero" x="0" y="0" width="27" height="17.6658395"></rect>
                <g id="Mask-group">
                    <mask id="mask-8" fill="white">
                        <use xlink:href="#path-7"></use>
                    </mask>
                    <use id="Rectangle-15" fill="url(#linearGradient-6)" fill-rule="nonzero" xlink:href="#path-7"></use>
                    <ellipse id="Ellipse-7" fill-opacity="0.790000021" fill="#48ADFF" fill-rule="nonzero" filter="url(#filter-9)" mask="url(#mask-8)" transform="translate(7.311085, -1.784320) rotate(6.443078) translate(-7.311085, 1.784320) " cx="7.31108543" cy="-1.7843195" rx="13.312806" ry="10.5147597"></ellipse>
                    <rect id="Rectangle-14" stroke-opacity="0.800000012" stroke="#BAF2FF" stroke-width="1.19021851" fill-rule="nonzero" filter="url(#filter-10)" mask="url(#mask-8)" x="1.56212097e-05" y="2.11289641e-05" width="26.9999999" height="17.6658397"></rect>
                </g>
                <rect id="Rectangle-15" stroke="url(#linearGradient-11)" stroke-width="0.5" fill-rule="nonzero" x="0.25" y="0.25" width="26.5" height="17.1658395"></rect>
                <g id="Group-4" transform="translate(9.344744, 6.811411)" fill-rule="nonzero" stroke-linecap="round" stroke-linejoin="round" stroke-width="1.36921326">
                    <path d="M2.52170137,0.654698062 C2.52170137,0.654698062 0.755482363,2.44678616 0.755482363,2.44678616 C0.755482363,2.44678616 2.52170137,4.23887425 2.52170137,4.23887425" id="Vector" stroke-opacity="0.219999999" stroke="#71A2CE" style="mix-blend-mode: multiply;" filter="url(#filter-12)"></path>
                    <path d="M7.07365539,0.1135558 C7.07365539,0.1135558 9.04213553,2.11086745 9.04213553,2.11086745 C9.04213553,2.11086745 7.07365539,4.10817911 7.07365539,4.10817911" id="Vector" stroke-opacity="0.219999999" stroke="#71A2CE" style="mix-blend-mode: multiply;" filter="url(#filter-13)"></path>
                    <path d="M1.96848013,0 C1.96848013,0 0,1.99731165 0,1.99731165 C0,1.99731165 1.96848013,3.99462331 1.96848013,3.99462331" id="Vector" stroke="url(#linearGradient-14)"></path>
                    <path d="M6.41626912,4.6300467e-06 C6.41626912,4.6300467e-06 8.38474925,1.99731628 8.38474925,1.99731628 C8.38474925,1.99731628 6.41626912,3.99462794 6.41626912,3.99462794" id="Vector" stroke="url(#linearGradient-15)"></path>
                </g>
            </g>
        </g>
    </g>
</svg>