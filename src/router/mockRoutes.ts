import { RouteVO } from "@/api/menu";

export const mockRoutes: RouteVO[] = [
  {
    path: "/witness-agent/#/remote-witness/wait",
    name: "remoteWitness",
    component: "remote-witness/wait",
    meta: {
      title: "远程见证",
      icon: "remote-witness",
      alwaysShow: true,
      target: "_blank",
    },
  },
  {
    path: "/witness-config",
    name: "witnessConfig",
    component: "BlankLayout",
    meta: {
      title: "见证配置",
      icon: "witness-config",
      alwaysShow: true,
    },
    children: [
      {
        path: "skill-group",
        name: "skillGroup",
        component: "witness-config/skill-group/index",
        meta: {
          title: "座席技能组",
          icon: "seat-skill-group",
          keepAlive: true,
        },
      },
      {
        path: "staff-manage",
        name: "staffManage",
        component: "witness-config/staff-manage",
        meta: {
          title: "见证人员管理",
          icon: "witness-staff-manage",
          keepAlive: true,
        },
      },
      {
        path: "seat-rules",
        name: "seatRules",
        component: "witness-config/seat-rules/index",
        meta: {
          title: "座席派单规则",
          icon: "seat-dispatch-rules",
          keepAlive: true,
        },
      },
      {
        path: "witness-script",
        name: "witnessScript",
        component: "witness-config/witness-script/index",
        meta: {
          title: "见证话术配置",
          icon: "witness-tech-config",
          keepAlive: true,
        },
      },
      {
        path: "reveal-info",
        name: "revealInfo",
        component: "witness-config/reveal-info/index",
        meta: {
          title: "揭示信息",
          icon: "witness-tech-config",
          keepAlive: true,
        },
      },
      {
        path: "witness-param",
        name: "witnessParam",
        component: "system/witness-param/index",
        meta: {
          title: "见证质检参数",
          icon: "setting",
          keepAlive: true,
        },
      },
    ],
  },
  {
    path: "/statistics",
    name: "statistics",
    component: "Layout",
    meta: {
      title: "查询统计",
      icon: "statistics",
      alwaysShow: true,
    },
    children: [
      {
        path: "seat-dispatch",
        name: "seatDispatch",
        component: "statistics/seat-dispatch/index",
        meta: {
          title: "座席调度管理",
          icon: "seat-dispatch-manage",
          keepAlive: true,
        },
      },
      {
        path: "task-query",
        name: "taskQuery",
        component: "statistics/task-query",
        meta: {
          title: "座席任务查询",
          icon: "seat-task-query",
          keepAlive: true,
        },
      },
      // {
      //   path: "business-stats",
      //   name: "座席业务量统计",
      //   component: "statistics/business-stats",
      //   meta: {
      //     title: "座席业务量统计",
      //     icon: "business-stats",
      //   },
      // },
      // {
      //   path: "work-stats",
      //   name: "座席工作量统计",
      //   component: "statistics/work-stats",
      //   meta: {
      //     title: "座席工作量统计",
      //     icon: "work-stats",
      //   },
      // },
    ],
  },
  {
    path: "/system",
    name: "system",
    component: "Layout",
    meta: {
      title: "系统管理",
      icon: "system-manage",
      alwaysShow: true,
    },
    children: [
      {
        path: "dict-manage",
        name: "dictManage",
        component: "system/dict-manage/index",
        meta: {
          title: "数据字典管理",
          icon: "setting",
          keepAlive: true,
        },
      },
      {
        path: "system-param",
        name: "systemParam",
        component: "system/system-param/index",
        meta: {
          title: "系统参数管理",
          icon: "setting",
          keepAlive: true,
        },
      },

      // {
      //   path: "operation-log",
      //   name: "操作日志管理",
      //   component: "system/operation-log",
      //   meta: {
      //     title: "操作日志管理",
      //     icon: "setting",
      //   },
      // },
    ],
  },
  // {
  //   path: "/sso",
  //   name: "单点登录",
  //   component: "Layout",
  //   meta: {
  //     title: "单点登录",
  //     icon: "sso2",
  //     alwaysShow: true,
  //   },
  // },
];
