import type { App } from "vue";
import { createRouter, createWebHashHistory, RouteRecordRaw } from "vue-router";

export const Layout = () => import("@/layout/index.vue");
export const RemoteWitnessLayout = () =>
  import("@/layout/remoteWitnessLayout.vue");
export const PadWitnessLayout = () => import("@/layout/PadWitnessLayout.vue");
export const BlankLayout = () => import("@/layout/blankLayout.vue");

// 静态路由
export const constantRoutes: RouteRecordRaw[] = [
  {
    path: "/redirect",
    component: Layout,
    meta: { hidden: true },
    children: [
      {
        path: "/redirect/:path(.*)",
        component: () => import("@/views/redirect/index.vue"),
      },
    ],
  },
  {
    path: "/login",
    component: () => import("@/views/login/index.vue"),
    meta: { hidden: true },
  },
  {
    path: "/",
    name: "/",
    component: Layout,
    redirect: "/dashboard",
    children: [
      {
        path: "dashboard",
        component: () => import("@/views/dashboard/index.vue"),
        // 用于 keep-alive 功能，需要与 SFC 中自动推导或显式声明的组件名称一致
        // 参考文档: https://cn.vuejs.org/guide/built-ins/keep-alive.html#include-exclude
        name: "Dashboard",
        meta: {
          title: "dashboard",
          icon: "homepage",
          affix: true,
          keepAlive: true,
        },
      },

      {
        path: "401",
        component: () => import("@/views/error-page/401.vue"),
        meta: { hidden: true },
      },
      {
        path: "404",
        component: () => import("@/views/error-page/404.vue"),
        meta: { hidden: true },
      },
    ],
  },

  {
    path: "/pad-witness",
    component: PadWitnessLayout,
    children: [
      {
        path: "pwait",
        component: () => import("@/views/pad-witness/pwait.vue"),
        meta: { hidden: true },
      },
      {
        path: "connecting",
        component: () => import("@/views/pad-witness/connecting.vue"),
        meta: { hidden: true },
      },
      {
        path: "client",
        component: () => import("@/views/pad-witness/client.vue"),
        meta: { hidden: true },
      },
      {
        path: "aisl",
        component: () => import("@/views/pad-witness/aisl.vue"),
        meta: { hidden: true },
      },
    ],
  },
  {
    path: "/remote-witness",
    component: RemoteWitnessLayout,
    children: [
      {
        path: "wait",
        component: () => import("@/views/remote-witness/wait.vue"),
        meta: { hidden: true, title: "座席端", icon: "document" },
      },
      {
        path: "user",
        component: () => import("@/views/remote-witness/user.vue"),
        name: "remoteWitness-user",
        meta: {
          title: "座席端",
          icon: "document",
          hidden: true,
          alwaysShow: false,
          params: null,
        },
      },
    ],
  },
  {
    path: "/video-preview",
    component: () => import("@/views/video-preview/preview.vue"),
    meta: { hidden: true },
  },
  // {
  //   path: "/remote-witness/customer",
  //   component: "remote-witness/customer",
  //   name: "remoteWitness-customer",
  //   meta: {
  //     title: "客户端",
  //     icon: "document",
  //     hidden: true,
  //     alwaysShow: false,
  //     params: null,
  //   },
  // },
  // 多级嵌套路由
];

/**
 * 创建路由
 */
const router = createRouter({
  history: createWebHashHistory(),
  routes: constantRoutes,
  // 刷新时，滚动条位置还原
  scrollBehavior: () => ({ left: 0, top: 0 }),
});

// 全局注册 router
export function setupRouter(app: App<Element>) {
  app.use(router);
}

/**
 * 重置路由
 */
export function resetRouter() {
  router.replace({ path: "/login" });
}

export default router;
