import setupPlugins from "@/plugins";
import mitt from "mitt";
import { createApp } from "vue";
import App from "./App.vue";

import {
  VueQueryPlugin,
  VueQueryPluginOptions,
  keepPreviousData,
} from "@tanstack/vue-query";

// 本地SVG图标
import "virtual:svg-icons-register";

// 样式
import "@/styles/index.scss";
import "animate.css";
import "element-plus/theme-chalk/dark/css-vars.css";
import TRTC from "trtc-sdk-v5";
import "uno.css";

localStorage.removeItem("layout"); //TODO:临时添加,后续删除
localStorage.removeItem("theme"); //TODO:临时添加,后续删除

const vueQueryPluginOptions: VueQueryPluginOptions = {
  queryClientConfig: {
    defaultOptions: {
      queries: {
        refetchOnWindowFocus: false,
        retry: false,
        placeholderData: keepPreviousData, // 全局配置 keepPreviousData
      },
    },
  },
};

TRTC.setLogLevel(2);
const bus = mitt();
const app = createApp(App);
app.use(setupPlugins);
app.use(VueQueryPlugin, vueQueryPluginOptions);

app.provide("$bus", bus);
app.config.globalProperties.$bus = bus;
app.mount("#app");
