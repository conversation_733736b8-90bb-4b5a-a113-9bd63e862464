/**
 * @name: websocket.ts
 * @author: fangzhenming
 * @date: 2024-12-12 15:07
 * @description: Enhanced WebSocket client with comprehensive debugging and error handling
 * @update: 2024-12-12 15:07
 * @email: <EMAIL>
 */
import SockJ<PERSON> from "sockjs-client";
import { Client, IFrame, IMessage } from "@stomp/stompjs";

interface WebSocketConfig {
  token: string;
  reconnectDelay?: number;
  debug?: boolean;
  onConnectCallback?: () => void;
  onErrorCallback?: (error: Error) => void;
}

export function createWebSocketClient({
  token,
  reconnectDelay = 5000,
  debug = true,
  onConnectCallback,
  onErrorCallback,
}: WebSocketConfig) {
  // Enhanced logging function
  const enhancedLogger = (
    message: string,
    level: "info" | "warn" | "error" = "info"
  ) => {
    if (debug) {
      const timestamp = new Date().toISOString();
      switch (level) {
        case "info":
          console.log(`[WebSocket:INFO] ${timestamp} - ${message}`);
          break;
        case "warn":
          console.warn(`[WebSocket:WARN] ${timestamp} - ${message}`);
          break;
        case "error":
          console.error(`[WebSocket:ERROR] ${timestamp} - ${message}`);
          break;
      }
    }
  };

  // Create SockJS socket
  const socket = new SockJS("/ws");

  // Create Stomp client
  const client = new Client({
    webSocketFactory: () => socket,
    connectHeaders: {
      token, // Pass token for authentication
    },

    // Detailed debug logging
    debug: (str) => {
      if (debug) {
        enhancedLogger(str);
      }
    },

    // Configurable reconnect delay
    reconnectDelay,

    // Connection success handler
    onConnect: (frame: IFrame) => {
      enhancedLogger(
        `WebSocket connected successfully. Frame: ${frame}`,
        "info"
      );

      // Call user-provided connect callback if provided
      if (onConnectCallback) {
        onConnectCallback();
      }
    },

    // Error handler
    onStompError: (frame: IFrame) => {
      enhancedLogger(
        `Broker reported error: ${frame.headers["message"]}`,
        "error"
      );
      enhancedLogger(`Additional details: ${frame.body}`, "error");

      // Call user-provided error callback if provided
      if (onErrorCallback) {
        onErrorCallback(new Error(`STOMP Error: ${frame.headers["message"]}`));
      }
    },

    // WebSocket error handler
    onWebSocketError: (error: Error) => {
      enhancedLogger(`WebSocket error occurred: ${error.message}`, "error");

      // Call user-provided error callback if provided
      if (onErrorCallback) {
        onErrorCallback(error);
      }
    },
  });

  return {
    client,
    connect: () => {
      try {
        enhancedLogger("Attempting to connect to WebSocket...", "info");
        client.activate();
      } catch (error) {
        enhancedLogger(
          `Connection activation failed: ${error instanceof Error ? error.message : error}`,
          "error"
        );
        throw error;
      }
    },
    disconnect: () => {
      try {
        enhancedLogger("Disconnecting WebSocket...", "info");
        client.deactivate();
      } catch (error) {
        enhancedLogger(
          `Disconnection failed: ${error instanceof Error ? error.message : error}`,
          "error"
        );
        throw error;
      }
    },
    subscribe: (destination: string, callback: (message: IMessage) => void) => {
      try {
        enhancedLogger(`Subscribing to destination: ${destination}`, "info");
        return client.subscribe(destination, (message: IMessage) => {
          enhancedLogger(
            `Received message from ${destination}: ${message.body}`,
            "info"
          );
          callback(message);
        });
      } catch (error) {
        enhancedLogger(
          `Subscription to ${destination} failed: ${error instanceof Error ? error.message : error}`,
          "error"
        );
        throw error;
      }
    },
  };
}

export function setupWebSocketConnection(token: string, onConnectCallback?) {
  const { client, connect, disconnect, subscribe } = createWebSocketClient({
    token,
    debug: true,
    onConnectCallback: () => {
      console.log("WebSocket connection established successfully");
      // Example of subscribing to a channel
      // subscription = subscribe("/queuemessages", (message) => {
      //   console.log("Received message:", message.body);
      // });
      onConnectCallback && onConnectCallback(subscribe);
    },
    onErrorCallback: (error) => {
      console.error("WebSocket connection error:", error);
      ElMessage({ message: error.message, type: "error" });
    },
  });

  // Connect to the WebSocket
  connect();

  // Return methods for further control
  return {
    client,
    connect,
    disconnect,
    subscribe,
  };
}
