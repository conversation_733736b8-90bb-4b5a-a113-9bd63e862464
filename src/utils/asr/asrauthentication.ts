/** 获取签名 start */

interface CryptoJSTest {
  HmacSHA1(signStr: string, secretKey: string): any;
}

declare global {
  interface Window {
    CryptoJSTest: CryptoJSTest;
    btoa(str: string): string;
  }
}

function toUint8Array(wordArray: {
  words: number[];
  sigBytes: number;
}): Uint8Array {
  // Shortcuts
  const words = wordArray.words;
  const sigBytes = wordArray.sigBytes;

  // Convert
  const u8 = new Uint8Array(sigBytes);
  for (let i = 0; i < sigBytes; i++) {
    u8[i] = (words[i >>> 2] >>> (24 - (i % 4) * 8)) & 0xff;
  }
  return u8;
}

function Uint8ArrayToString(fileData: Uint8Array): string {
  let dataString = "";
  for (let i = 0; i < fileData.length; i++) {
    dataString += String.fromCharCode(fileData[i]);
  }
  return dataString;
}

interface Config {
  secretKey: string;
}

declare const config: Config;

// 签名函数示例
export function signCallback(signStr: string): string {
  // const secretKey = config.secretKey;
  const secretKey = "pY6X3PaYJxb6hHlDcu5FHBuTBsUAvlS1"; // 直接使用密钥，不依赖config
  const hash = window.CryptoJSTest.HmacSHA1(signStr, secretKey);
  const bytes = Uint8ArrayToString(toUint8Array(hash));
  return window.btoa(bytes);
}

/** 获取签名 end */
