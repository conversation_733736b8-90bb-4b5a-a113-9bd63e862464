/**
 * 与本地Windows程序通信的工具类
 * 用于文件上传下载功能
 */

// ==================== 文件上传相关 ====================

/**
 * 文件上传进度信息
 */
export interface UploadProgress {
  ratio: number; // 上传进度百分比
  current: number; // 当前已上传字节数
  total: number; // 总字节数
  speed: number; // 实时速度 (Bytes/Sec)
  utime: number; // 已用时间 (秒)
  rtime: number; // 剩余时间 (秒)
}

/**
 * 文件上传结果
 */
export interface UploadResult {
  success: boolean;
  httpCode?: number;
  message?: string;
  error?: string;
}

/**
 * 文件上传配置
 */
export interface UploadConfig {
  url: string; // 上传地址
  filePath: string; // 本地文件路径
  isForm?: boolean; // 是否使用表单上传，默认false（流上传）
  formFields?: Array<{
    key: string;
    value: string;
    type: "file" | "content";
  }>; // 表单字段（仅在isForm=true时使用）
  onProgress?: (progress: UploadProgress) => void; // 进度回调
  onComplete?: (result: UploadResult) => void; // 完成回调
  onError?: (error: string) => void; // 错误回调
}

/**
 * DLL插件类，用于与本地Windows程序通信
 */
class DllPlugin {
  private ws: WebSocket | null = null;
  private url: string;
  private isConnected = false;
  private requestCounter = 0;
  private pendingRequests = new Map<number, any>();

  // 回调函数
  public onMessage: ((msg: string) => void) | null = null;
  public onAppConn: (() => void) | null = null;
  public onError: ((error: Event) => void) | null = null;
  public onClose: (() => void) | null = null;

  constructor(url: string) {
    this.url = url;
    this.connect();
  }

  /**
   * 连接到本地程序
   */
  private connect() {
    try {
      this.ws = new WebSocket(this.url);

      this.ws.onopen = () => {
        console.log("DllPlugin WebSocket连接成功");
        this.isConnected = true;
        if (this.onAppConn) {
          this.onAppConn();
        }
      };

      this.ws.onmessage = (event) => {
        if (this.onMessage) {
          this.onMessage(event.data);
        }
      };

      this.ws.onerror = (error) => {
        console.error("DllPlugin WebSocket连接错误:", error);
        this.isConnected = false;
        if (this.onError) {
          this.onError(error);
        }
      };

      this.ws.onclose = () => {
        console.log("DllPlugin WebSocket连接关闭");
        this.isConnected = false;
        if (this.onClose) {
          this.onClose();
        }
      };
    } catch (error) {
      console.error("创建WebSocket连接失败:", error);
      this.isConnected = false;
    }
  }

  /**
   * 发送消息到本地程序
   */
  public send(message: string): boolean {
    if (!this.ws || !this.isConnected) {
      console.error("WebSocket未连接，无法发送消息");
      return false;
    }

    try {
      this.ws.send(message);
      return true;
    } catch (error) {
      console.error("发送消息失败:", error);
      return false;
    }
  }

  /**
   * 生成随机请求ID
   */
  public getRandomId(): number {
    return ++this.requestCounter;
  }

  /**
   * 关闭连接
   */
  public close() {
    if (this.ws) {
      this.ws.close();
      this.ws = null;
    }
    this.isConnected = false;
  }

  /**
   * 检查连接状态
   */
  public isConnectedToApp(): boolean {
    return this.isConnected;
  }
}

// 全局DLL插件实例
let dllPluginInstance: DllPlugin | null = null;

/**
 * 获取或创建DLL插件实例
 */
function getDllPluginInstance(): Promise<DllPlugin> {
  return new Promise((resolve, reject) => {
    // 小程序请求 token
    const transToken =
      "sid=16&cid=apexsoft&tk=57499EE57BB81C13DD655E7B65CD7D1DF47C768125D66DB07E0EAE920F79EA462F3832B394CE88D27DC5F83011D2D59697FF890037692E193AA8977B6E4F485EF95776BB919AEADD04877B2D32C86A6B54C7C7DC662A775537D67EEE7A771F24FC2A51F5C8BF5B947578B14D6A7CF06CC235BF0D76C71A5A5D0B40D4E55C8DF61C495037C444F2D47B8647B5520B9FF3FA08B2726AFCC7B5C44A0659EDC705DBBBB9DDEFD25CD7ECF9BE1073FAB26A1D9A2C392CDB363B190CDD1AD9AE168BD32F06560142FF5007D579DD9893AFD98E4B157A8AF11742267EA8DA831DC3FB6BF2C116CFCA7FD467A713390E62795E1F614C411CAC5BA5EA28A49103923B61DE";

    // 文件上传下载小程序连接地址
    const transSvcUrl = `ws://localhost:20080?flag=1&pid=3c9cf3e3-b351-4397-adef-5985530c2c46&${transToken}`;

    if (dllPluginInstance && dllPluginInstance.isConnectedToApp()) {
      resolve(dllPluginInstance);
      return;
    }

    // 创建新实例
    dllPluginInstance = new DllPlugin(transSvcUrl);

    // 设置连接成功回调
    dllPluginInstance.onAppConn = () => {
      resolve(dllPluginInstance!);
    };

    // 设置错误回调
    dllPluginInstance.onError = (_error) => {
      reject(new Error("无法连接到文件传输小程序，请确保小程序已启动"));
    };

    // 设置关闭回调
    dllPluginInstance.onClose = () => {
      dllPluginInstance = null;
    };

    // 超时处理
    setTimeout(() => {
      if (!dllPluginInstance?.isConnectedToApp()) {
        reject(new Error("连接文件传输小程序超时，请检查小程序是否正常运行"));
      }
    }, 5000);
  });
}

/**
 * 上传文件到服务器
 * @param config 上传配置
 * @returns Promise<UploadResult>
 */
export async function uploadFile(config: UploadConfig): Promise<UploadResult> {
  try {
    // 获取DLL插件实例
    const plugin = await getDllPluginInstance();

    return new Promise<UploadResult>((resolve, reject) => {
      const requestId = plugin.getRandomId();
      let isCompleted = false;

      // 构建上传消息
      const uploadMessage: any = {
        req: "AxFile_Upload",
        rid: requestId,
        para: {
          id: Date.now(),
          url: config.url,
          file: config.filePath,
          is_form: config.isForm || false,
          user: "",
        },
      };

      // 如果是表单上传，添加表单字段
      if (config.isForm && config.formFields) {
        uploadMessage.para.form = config.formFields;
      }

      // 设置消息处理器
      const originalOnMessage = plugin.onMessage;
      plugin.onMessage = (msg: string) => {
        try {
          const data = JSON.parse(msg);

          // 处理上传响应
          if (data.rid === requestId) {
            if (data.ret !== 0 && data.ret !== undefined) {
              const errorMsg = data.err || "上传请求失败";
              if (config.onError) config.onError(errorMsg);
              if (!isCompleted) {
                isCompleted = true;
                resolve({ success: false, error: errorMsg });
              }
              return;
            }

            if (data.data.state) {
              console.log("开始文件上传");
            } else {
              const errorMsg = data.data.err || "文件上传失败";
              if (config.onError) config.onError(errorMsg);
              if (!isCompleted) {
                isCompleted = true;
                resolve({ success: false, error: errorMsg });
              }
            }
          }
          // 处理上传进度
          else if (data.event === "AxFile_UploadProg") {
            if (data.data.state && config.onProgress) {
              const progress: UploadProgress = {
                ratio: data.data.ratio,
                current: data.data.current,
                total: data.data.total,
                speed: data.data.speed,
                utime: data.data.utime,
                rtime: data.data.rtime,
              };
              config.onProgress(progress);
            }
          }
          // 处理上传完成
          else if (data.event === "AxFile_UploadFinished") {
            if (data.data.state) {
              const result: UploadResult = {
                success: true,
                httpCode: data.data.http_code,
                message: data.data.msg,
              };
              if (config.onComplete) config.onComplete(result);
              if (!isCompleted) {
                isCompleted = true;
                resolve(result);
              }
            } else {
              const errorMsg = "上传文件失败";
              if (config.onError) config.onError(errorMsg);
              if (!isCompleted) {
                isCompleted = true;
                resolve({ success: false, error: errorMsg });
              }
            }
          }
          // 处理上传错误
          else if (data.event === "AxFile_UploadError") {
            const errorMsg = data.data.error || "上传文件失败";
            if (config.onError) config.onError(errorMsg);
            if (!isCompleted) {
              isCompleted = true;
              resolve({ success: false, error: errorMsg });
            }
          }
        } catch (error) {
          console.error("解析上传响应消息失败:", error);
          if (!isCompleted) {
            isCompleted = true;
            resolve({ success: false, error: "解析响应消息失败" });
          }
        }

        // 调用原始消息处理器
        if (originalOnMessage) {
          originalOnMessage(msg);
        }
      };

      // 发送上传请求
      const success = plugin.send(JSON.stringify(uploadMessage));
      if (!success) {
        reject(new Error("发送上传请求失败"));
      }

      // 设置超时
      setTimeout(() => {
        if (!isCompleted) {
          isCompleted = true;
          resolve({ success: false, error: "上传超时" });
        }
      }, 300000); // 5分钟超时
    });
  } catch (error) {
    console.error("上传文件失败:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "未知错误",
    };
  }
}

/**
 * 打开文件传输小程序
 */
export async function openTransferApp(): Promise<boolean> {
  try {
    await getDllPluginInstance();
    return true;
  } catch (error) {
    console.error("打开文件传输小程序失败:", error);
    return false;
  }
}

/**
 * 关闭文件传输小程序连接
 */
export function closeTransferApp(): void {
  if (dllPluginInstance) {
    dllPluginInstance.close();
    dllPluginInstance = null;
  }
}
