// 定义话术项的接口
interface SpeechItem {
  id: string;
  nr: string;
  pagetime: number;
  khhd: string;
  waitTime: number;
  needAnswer: boolean;
  playMode: string;
  isSpecial: boolean;
}

// 定义anychat的ttsInfo格式接口
interface AnyChatTtsGroupItem {
  ruleName: string;
  type: number; // 0: 语言播报, 1: 一问一答, 2: 文字跟读, 3: 视频播报, 4: MP3话术
  broadcast: string;
  checkQuestion: string;
  expectAnswer: string;
  unExpectAnswer: string;
  customerAnswer: string;
  fixed?: number;
}

interface AnyChatTtsGroup {
  groupName: string;
  groupKey: string;
  groupList: AnyChatTtsGroupItem[];
}

/**
 * 将我们的话术格式转换为anychat的ttsInfo格式
 * @param speeches 话术列表
 * @param groupName 分组名称，默认为"见证话术"
 * @param groupKey 分组键，默认为"WITNESS"
 * @returns anychat格式的ttsInfo
 */
export function convertToAnyChatTtsInfo(
  speeches: SpeechItem[],
  groupName: string = "见证话术",
  groupKey: string = "WITNESS"
): AnyChatTtsGroup[] {
  const groupList: AnyChatTtsGroupItem[] = speeches.map((speech, index) => {
    // 根据是否需要回答和客户回答内容确定类型
    let type = 0; // 默认为语言播报
    let expectAnswer = "";
    let unExpectAnswer = "";

    let needAnswer = speech.khhd && speech.khhd !== "无需";

    if (needAnswer) {
      type = 1; // 一问一答
      // 将客户回答按分隔符分割成期望答案和非期望答案
      const answers = speech.khhd
        .split("|")
        .map((a) => a.trim())
        .filter(Boolean);
      if (answers.length > 0) {
        // 将前一半作为期望答案，后一半作为非期望答案
        const midPoint = Math.ceil(answers.length / 2);
        expectAnswer = answers.slice(0, midPoint).join(",");
        unExpectAnswer = answers.slice(midPoint).join(",");

        // 如果只有一组答案，默认都作为期望答案
        if (answers.length === 1) {
          expectAnswer = answers[0];
          unExpectAnswer = "否,不,不是,不是的";
        }
      }
    }

    return {
      ruleName: `话术${index + 1}${speech.isSpecial ? "(特殊)" : ""}`,
      type: type,
      broadcast: needAnswer ? "" : speech.nr,
      checkQuestion: needAnswer ? speech.nr : "",
      expectAnswer: expectAnswer,
      unExpectAnswer: unExpectAnswer,
      customerAnswer: "",
    };
  });

  return [
    {
      groupName: groupName,
      groupKey: groupKey,
      groupList: groupList,
    },
  ];
}

/**
 * 获取默认的anychat ttsInfo格式，用于预设数据
 * @param speeches 话术列表
 * @returns 默认的ttsInfo数组
 */
export function getDefaultTtsInfo(speeches: SpeechItem[]): AnyChatTtsGroup[] {
  return convertToAnyChatTtsInfo(speeches, "默认见证话术", "DEFAULT_WITNESS");
}

/**
 * 合并普通话术和特殊话术
 * @param normalSpeeches 普通话术
 * @param specialSpeeches 选中的特殊话术
 * @returns 合并后的话术列表
 */
export function mergeSpeechesWithSpecial(
  normalSpeeches: SpeechItem[],
  specialSpeeches: SpeechItem[]
): SpeechItem[] {
  // 按原始顺序合并，特殊话术插入到合适的位置
  // 这里简单地将特殊话术添加到末尾，可以根据业务需求调整插入位置
  return [...normalSpeeches, ...specialSpeeches];
}

/**
 * 验证话术数据格式
 * @param speeches 话术列表
 * @returns 验证结果
 */
export function validateSpeeches(speeches: SpeechItem[]): {
  isValid: boolean;
  errors: string[];
} {
  const errors: string[] = [];

  if (!Array.isArray(speeches) || speeches.length === 0) {
    errors.push("话术列表不能为空");
    return { isValid: false, errors };
  }

  speeches.forEach((speech, index) => {
    if (!speech.id) {
      errors.push(`第${index + 1}条话术缺少ID`);
    }
    if (!speech.nr || speech.nr.trim() === "") {
      errors.push(`第${index + 1}条话术内容为空`);
    }
    if (typeof speech.pagetime !== "number" || speech.pagetime <= 0) {
      errors.push(`第${index + 1}条话术页面时长无效`);
    }
    if (typeof speech.waitTime !== "number" || speech.waitTime < 0) {
      errors.push(`第${index + 1}条话术等待时长无效`);
    }
  });

  return {
    isValid: errors.length === 0,
    errors,
  };
}

/**
 * 话术变量转换函数
 * @param text 包含变量的话术文本
 * @param businessDataSupplement 补充业务数据（优先级高）
 * @param businessData 基础业务数据
 * @param userName 见证人员姓名
 * @returns 转换后的话术文本
 */
export function replaceScriptVariables(
  text: string,
  businessDataSupplement: Record<string, any> = {},
  businessData: Record<string, any> = {},
  userName: string = ""
): string {
  // 变量映射表，键为大写变量名，值为对应的数据字段名（小写）
  const variableMap: Record<string, string> = {
    "{KHMC}": "khmc", // 客户名称
    "{KHH}": "khh", // 客户号
    "{JZRYMC}": "jzrymc", // 见证人员名称（特殊处理）
    "{ZJBH}": "zjbh", // 证件编号
    "{CPDM}": "cpdm", // 产品代码
    "{ZJZRQ}": "zjzrq", // 证件截止日期
    "{FQRXM}": "fqrxm", // 发起人姓名
    "{FQR}": "fqr", // 发起人
    "{FXCSNLMC}": "fxcsnlmc", // 风险承受能力
    "{YYBMC}": "yybmc", // 营业部名称
    "{CPTZQXMC}": "cptzqxmc", // 产品投资期限
    "{KHTZPZMC}": "khtzpzmc", // 客户投资品种
    "{ZJLBMC}": "zjlbmc", // 证件类别
    "{SLYWLBMC}": "slywlbmc", // 双录业务类别
    "{ZJQSRQ}": "zjqsrq", // 证件起始日期
    "{KHTZQXMC}": "khtzqxmc", // 客户投资期限
    "{CPMC}": "cpmc", // 产品名称
    "{CPTZPZMC}": "cptzpzmc", // 产品投资品种
    "{FSYYB}": "fsyyb", // 发生营业部代码
    "{CPFXDJMC}": "cpfxdjmc", // 产品风险等级
  };

  let result = text;

  // 遍历所有变量进行替换
  Object.entries(variableMap).forEach(([variable, fieldName]) => {
    if (result.includes(variable)) {
      let value = "";

      // 特殊处理见证人员名称
      if (variable === "{JZRYMC}") {
        value = userName || "";
      } else {
        // 优先从businessDataSupplement获取，没有则从businessData获取
        value =
          businessDataSupplement[fieldName] || businessData[fieldName] || "";
      }

      // 替换变量
      result = result.replace(
        new RegExp(variable.replace(/[{}]/g, "\\$&"), "g"),
        value
      );
    }
  });

  return result;
}

/**
 * 批量转换话术列表中的变量
 * @param speeches 话术列表
 * @param businessDataSupplement 补充业务数据
 * @param businessData 基础业务数据
 * @param userName 见证人员姓名
 * @returns 转换后的话术列表
 */
export function replaceScriptVariablesInSpeeches(
  speeches: SpeechItem[],
  businessDataSupplement: Record<string, any> = {},
  businessData: Record<string, any> = {},
  userName: string = ""
): SpeechItem[] {
  return speeches.map((speech) => ({
    ...speech,
    nr: replaceScriptVariables(
      speech.nr,
      businessDataSupplement,
      businessData,
      userName
    ),
  }));
}
