# wrlMessage 文件上传工具

这是一个用于与本地Windows程序通信的文件上传工具，基于WebSocket协议实现。

## 功能特性

- 支持流上传和表单上传两种模式
- 实时上传进度回调
- 完整的错误处理机制
- 自动连接管理
- TypeScript 类型支持

## 基本用法

### 1. 导入工具

```typescript
import { uploadFile, openTransferApp, closeTransferApp, type UploadConfig } from '@/utils/wrlMessage'
```

### 2. 简单上传示例

```typescript
const uploadConfig: UploadConfig = {
  url: "/api/upload/video",
  filePath: "D:\\videos\\sample.mp4",
  isForm: true, // 使用表单上传
  formFields: [
    {
      key: "videoFile",
      value: "D:\\videos\\sample.mp4",
      type: "file"
    },
    {
      key: "roomId", 
      value: "room123",
      type: "content"
    }
  ],
  onProgress: (progress) => {
    console.log(`上传进度: ${progress.ratio.toFixed(2)}%`)
  },
  onComplete: (result) => {
    console.log('上传完成:', result)
  },
  onError: (error) => {
    console.error('上传错误:', error)
  }
}

const result = await uploadFile(uploadConfig)
if (result.success) {
  console.log('上传成功!')
} else {
  console.error('上传失败:', result.error)
}
```

### 3. 流上传示例

```typescript
const streamConfig: UploadConfig = {
  url: "/api/upload/stream",
  filePath: "D:\\videos\\sample.mp4",
  isForm: false, // 使用流上传
  onProgress: (progress) => {
    console.log(`上传进度: ${progress.ratio}%`)
    console.log(`上传速度: ${progress.speed} Bytes/Sec`)
    console.log(`剩余时间: ${progress.rtime} 秒`)
  }
}

const result = await uploadFile(streamConfig)
```

## API 参考

### UploadConfig 接口

```typescript
interface UploadConfig {
  url: string                    // 上传地址
  filePath: string              // 本地文件路径
  isForm?: boolean              // 是否使用表单上传，默认false（流上传）
  formFields?: Array<{          // 表单字段（仅在isForm=true时使用）
    key: string
    value: string
    type: 'file' | 'content'
  }>
  onProgress?: (progress: UploadProgress) => void  // 进度回调
  onComplete?: (result: UploadResult) => void      // 完成回调
  onError?: (error: string) => void               // 错误回调
}
```

### UploadProgress 接口

```typescript
interface UploadProgress {
  ratio: number      // 上传进度百分比
  current: number    // 当前已上传字节数
  total: number      // 总字节数
  speed: number      // 实时速度 (Bytes/Sec)
  utime: number      // 已用时间 (秒)
  rtime: number      // 剩余时间 (秒)
}
```

### UploadResult 接口

```typescript
interface UploadResult {
  success: boolean
  httpCode?: number
  message?: string
  error?: string
}
```

### 工具函数

#### uploadFile(config: UploadConfig): Promise<UploadResult>
上传文件到服务器

#### openTransferApp(): Promise<boolean>
打开文件传输小程序连接

#### closeTransferApp(): void
关闭文件传输小程序连接

## 注意事项

1. **小程序依赖**: 需要确保本地Windows文件传输小程序正在运行
2. **连接地址**: 默认连接地址为 `ws://localhost:20080`
3. **超时设置**: 上传超时时间为5分钟
4. **错误处理**: 建议在使用时添加适当的错误处理逻辑
5. **文件路径**: 文件路径必须是本地Windows系统的绝对路径

## 错误处理

常见错误及解决方案：

- **连接失败**: 检查文件传输小程序是否正常运行
- **上传超时**: 检查网络连接和文件大小
- **文件路径错误**: 确保文件路径正确且文件存在
- **权限问题**: 确保小程序有访问文件的权限

## 在组件中的使用

参考 `AuditControl.vue` 和 `VideoUploadDrawer.vue` 中的实际使用示例。

## 测试

运行测试：
```bash
npm run test src/utils/__tests__/wrlMessage.test.ts
```
