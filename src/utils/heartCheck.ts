const useHeartCheck = (
  websocket: WebSocket,
  ping: any,
  newWebSocket: Function
) => ({
  timeout: 10000, // 9分钟发一次心跳，比server端设置的连接时间稍微小一点，在接近断开的情况下以通信的方式去重置连接时间。
  serverTimeoutObj: -1,
  reset: function () {
    // clearTimeout(this.timeoutObj);
    clearTimeout(this.serverTimeoutObj);
    return this;
  },
  start: function () {
    this.serverTimeoutObj = setInterval(() => {
      if (websocket.readyState == 1) {
        console.log("连接状态，发送消息保持连接");
        websocket.send(ping);
        this.reset().start(); // 如果获取到消息，说明连接是正常的，重置心跳检测
      } else {
        console.log("断开状态，尝试重连");
        newWebSocket();
      }
    }, this.timeout);
  },
});
export default useHeartCheck;
