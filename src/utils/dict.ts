export type fldmDictItem = {
  fldm: string;
  note: string;
  flag: string;
  ibm: string;
  flmc: string;
  type: string;
  cbm: string;
};

/**
 * 根据字典值，和字典代码获取字典名称
 */
export function getDictName(
  dict: Record<string, fldmDictItem[]> | undefined,
  code: string,
  value: string
) {
  const item = dict?.[code]?.find((item) => String(item.ibm) === String(value));
  return item ? item.note : "";
}

export type DictNoteIbmItem = {
  dicCode: string;
  dicName: string;
  ibm: number;
  cbm: string;
  note: string;
  flag: number;
};

/**
 * 根据字典代码和IBM值获取字典备注
 * @param dict - 字典数组
 * @param code - 字典代码
 * @param ibm - IBM值
 * @returns 字典备注，如果未找到则返回空字符串
 */
export function getDictNoteByIbm(
  dict: DictNoteIbmItem[] | undefined,
  code: string,
  ibm: number
) {
  const item = dict?.find((item) => item.dicCode === code && item.ibm === ibm);
  return item ? item.note : "";
}

/**
 * 根据字典代码获取转换选项
 * @param dict - 字典数组
 * @param code - 字典代码
 * @returns 转换选项数组，包含标签和值
 */
export function getDictConvertOptionsByCode(
  dict: DictNoteIbmItem[] | undefined,
  code: string
) {
  return (dict || [])
    .filter((item) => item.dicCode === code)
    .map((item) => ({
      label: item.note,
      value: item.ibm,
    }));
}

/**
 * 业务代码列表项类型定义
 */
export type BizCodeItem = {
  bizCode: string;
  bizName: string;
  bizLevel: string;
  [key: string]: any;
};

/**
 * 根据业务代码获取业务信息的某个属性值
 * @param bizCodeList - 业务代码列表
 * @param code - 业务代码
 * @param property - 要返回的属性名
 * @returns 属性值，如果未找到则返回空字符串
 */
export function getBizInfoByCode(
  bizCodeList: BizCodeItem[] | undefined,
  code: string | undefined,
  property: string
) {
  if (!code || !bizCodeList) return "";
  const item = bizCodeList.find((item) => item.bizCode === code);
  return item ? item[property] : "";
}
