import mitt, { Emitter } from "mitt";

// 定义事件数据类型
export interface TTSSubtitlesUpdateData {
  subtitles: Array<{
    text: string;
    beginTime: number;
    endTime: number;
    beginIndex: number;
    endIndex: number;
  }>;
  tts: string;
  beginIndex: number;
  endIndex: number;
}

interface FaceDetectionUpdateData {
  result: {
    faceCount?: number;
    faces?: any[];
    compareScore?: number;
    detail: string;

    timestamp?: number;
  };
  forceUpdate?: boolean;
}

export interface BusinessTabChangeData {
  tabName: string;
  tabIndex: number;
}

// 定义事件映射类型
type Events = {
  "tts-subtitles-update": TTSSubtitlesUpdateData;
  "face-detection-update": FaceDetectionUpdateData;
  "business-tab-change": BusinessTabChangeData;
};

// 创建带类型的 emitter 实例
export const emitter: Emitter<Events> = mitt<Events>();

// 定义事件类型
export const EventTypes = {
  TTS_SUBTITLES_UPDATE: "tts-subtitles-update",
  FACE_DETECTION_UPDATE: "face-detection-update",
  BUSINESS_TAB_CHANGE: "business-tab-change",
} as const;
