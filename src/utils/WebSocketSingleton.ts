import useHeartCheck from "@/utils/heartCheck";

interface PublishParams {
  destination: string;
  body: any;
  contentType?: string; // 支持动态内容类型（如 application/json、binary）
  purpose?: "singleCapture"; // singleCapture就是拍照人脸比对，不传就是录制连续人脸检测
}
class WebSocketSingleton {
  private static instance: WebSocketSingleton;
  private socket: WebSocket | null = null;
  private isConnected = false;
  private connectPromise: Promise<WebSocket> | null = null;
  private subscriptions: Map<string, Set<(data: any) => void>> = new Map();
  private reconnectAttempts = 0;
  private readonly MAX_RECONNECT_ATTEMPTS = 5;
  private readonly RECONNECT_DELAY = 5000;
  private heartCheck: any;

  private constructor() {}

  public static getInstance(): WebSocketSingleton {
    if (!WebSocketSingleton.instance) {
      WebSocketSingleton.instance = new WebSocketSingleton();
    }
    return WebSocketSingleton.instance;
  }
  public static getNewWebsocket(): WebSocketSingleton {
    const ws = new WebSocketSingleton();

    return ws;
  }

  private createSocket(token: string, debug = false): WebSocket {
    const VITE_APP_BASE_API = import.meta.env.VITE_APP_BASE_API;
    const wsUrl = `${window.location.protocol === "https:" ? "wss://" : "ws://"}${window.location.host}${VITE_APP_BASE_API}/ws?token=${token}`;

    const socket = new WebSocket(wsUrl);

    const log = (
      message: string,
      level: "info" | "warn" | "error" = "info"
    ) => {
      if (debug) {
        const timestamp = new Date().toISOString();
        switch (level) {
          case "info":
            console.log(`[WebSocket:INFO] ${timestamp} - ${message}`);
            break;
          case "warn":
            console.warn(`[WebSocket:WARN] ${timestamp} - ${message}`);
            break;
          case "error":
            console.error(`[WebSocket:ERROR] ${timestamp} - ${message}`);
            break;
        }
      }
    };

    socket.onmessage = (event) => {
      try {
        const data = event.data;
        // 检查是否是二进制数据
        if (data instanceof Blob) {
          // 如果是音频数据，直接传递 Blob
          this.notifySubscribers("/audio", data);
          return;
        }

        // 处理文本消息
        const message = JSON.parse(data);
        const { type, data: messageData } = message;
        // 根据消息类型通知订阅者
        this.notifySubscribers(`/${type.toLowerCase()}`, messageData);
        this.heartCheck.reset().start();
      } catch (error) {
        log(`Error parsing message: ${error}`, "error");
      }
    };

    socket.onopen = () => {
      this.isConnected = true;
      this.reconnectAttempts = 0;
      log("WebSocket connected successfully", "info");
    };

    socket.onclose = () => {
      this.isConnected = false;
      log("WebSocket connection closed", "warn");
      this.handleReconnection(token, debug);
    };

    socket.onerror = (error) => {
      this.isConnected = false;
      log(`WebSocket error: ${error}`, "error");
    };

    return socket;
  }

  private handleReconnection(token: string, debug: boolean) {
    if (this.reconnectAttempts < this.MAX_RECONNECT_ATTEMPTS) {
      this.reconnectAttempts++;
      setTimeout(() => {
        this.connect(token, debug);
      }, this.RECONNECT_DELAY);
    }
  }

  private notifySubscribers(destination: string, data: any) {
    const subscribers = this.subscriptions.get(destination);
    if (subscribers) {
      subscribers.forEach((callback) => callback(data));
    }
  }

  public async connect(token: string, debug = false): Promise<WebSocket> {
    if (this.isConnected && this.socket) {
      return this.socket;
    }

    if (!this.connectPromise) {
      this.connectPromise = new Promise((resolve, reject) => {
        const socket = this.createSocket(token, debug);
        this.socket = socket;

        socket.onopen = () => {
          this.heartCheck.reset().start();
          this.isConnected = true;
          resolve(socket);
        };

        socket.onerror = (error) => {
          reject(error);
        };

        this.heartCheck = useHeartCheck(
          this.socket,
          JSON.stringify({
            type: "PING",
          }),
          () => {}
        );
      });
    }

    try {
      return await this.connectPromise;
    } finally {
      this.connectPromise = null;
    }
  }

  public disconnect(): void {
    if (this.socket && this.isConnected) {
      this.socket.close();
      this.isConnected = false;
      this.subscriptions.clear();
      console.log("[WebSocket] Disconnected");
    }
  }
  // 取消所有订阅
  public unsubscribeAll(): void {
    // 清空所有 destination 的订阅
    this.subscriptions.clear();

    console.log(
      "[WebSocket] All subscriptions and listeners have been unsubscribed."
    );
  }
  public subscribe(
    destination: string,
    callback: (data: any) => void
  ): () => void {
    if (!this.subscriptions.has(destination)) {
      this.subscriptions.set(destination, new Set());
    }
    this.subscriptions.get(destination)!.add(callback);
    return () => {
      this.unsubscribe(destination, callback);
    };
  }
  public async ensureConnected(): Promise<void> {
    if (this.isConnected && this.socket) {
      return;
    }

    // 如果正在连接中，等待连接完成
    if (this.connectPromise) {
      await this.connectPromise;
      return;
    }

    // 如果没有 token，抛出错误
    throw new Error("WebSocket not initialized. Please call connect() first");
  }
  public async publish(params: PublishParams): Promise<boolean> {
    // 确保连接已建立
    await this.ensureConnected();

    try {
      const { destination, body, contentType } = params;
      const messageType =
        destination.split("/").pop()?.toUpperCase() || "UNKNOWN";

      if (body instanceof Uint8Array || body instanceof ArrayBuffer) {
        // 发送二进制数据的头部信息
        const binaryHeader = {
          type: "BINARY_HEADER",
          contentType: contentType || "application/octet-stream",
          data: messageType, // 传递消息类型
          purpose: params.purpose, // 传递目的
        };
        this.socket!.send(JSON.stringify(binaryHeader));

        // 发送二进制数据
        this.socket!.send(body);
      } else {
        // 发送 JSON 数据
        const message = {
          type: messageType,
          data: body,
          contentType: contentType || "application/json",
        };
        this.socket!.send(JSON.stringify(message));
      }

      return true;
    } catch (error) {
      console.error("Error sending message:", error);
      return false;
    }
  }

  // 取消订阅方法
  public unsubscribe(destination: string, callback: (data: any) => void): void {
    const subscribers = this.subscriptions.get(destination);
    if (subscribers) {
      subscribers.delete(callback);
      if (subscribers.size === 0) {
        this.subscriptions.delete(destination);
      }
    }
  }

  public isSocketConnected(): boolean {
    return this.isConnected && this.socket !== null;
  }
}

export default WebSocketSingleton;
