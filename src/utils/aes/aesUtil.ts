/**
 * @name: aesUtil.js
 * @author: fangzhen<PERSON>
 * @date: 2021/5/19 下午4:54
 * @description：aesUtil.js
 * @update: 2021/5/19 下午4:54
 * @email:<EMAIL>
 */
import AES from "./aesIns";

import defaultSettings from "@/settings.js";
const AESIns = AES;
AESIns.setSecret(defaultSettings.title);
export default class AesUtil {
  static encrypt(text) {
    const signature = AESIns.encryptBase64(text);
    return signature.toString();
  }
}
