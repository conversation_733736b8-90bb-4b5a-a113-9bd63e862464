/**
 * wrlMessage 工具类测试
 */

import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest'
import { uploadFile, openTransferApp, closeTransferApp, type UploadConfig } from '../wrlMessage'

// Mock WebSocket
class MockWebSocket {
  public onopen: ((event: Event) => void) | null = null
  public onmessage: ((event: MessageEvent) => void) | null = null
  public onerror: ((event: Event) => void) | null = null
  public onclose: (() => void) | null = null
  public readyState = WebSocket.CONNECTING

  constructor(public url: string) {
    // 模拟异步连接
    setTimeout(() => {
      this.readyState = WebSocket.OPEN
      if (this.onopen) {
        this.onopen(new Event('open'))
      }
    }, 100)
  }

  send(data: string) {
    console.log('Mock WebSocket send:', data)
    
    // 模拟服务器响应
    setTimeout(() => {
      const request = JSON.parse(data)
      
      // 模拟上传开始响应
      if (request.req === 'AxFile_Upload') {
        const startResponse = {
          rid: request.rid,
          ret: 0,
          data: { state: true }
        }
        
        if (this.onmessage) {
          this.onmessage(new MessageEvent('message', { 
            data: JSON.stringify(startResponse) 
          }))
        }

        // 模拟上传进度
        setTimeout(() => {
          const progressResponse = {
            event: 'AxFile_UploadProg',
            data: {
              state: true,
              ratio: 50,
              current: 500,
              total: 1000,
              speed: 100,
              utime: 5,
              rtime: 5
            }
          }
          
          if (this.onmessage) {
            this.onmessage(new MessageEvent('message', { 
              data: JSON.stringify(progressResponse) 
            }))
          }
        }, 200)

        // 模拟上传完成
        setTimeout(() => {
          const finishResponse = {
            event: 'AxFile_UploadFinished',
            data: {
              state: true,
              http_code: 200,
              msg: '上传成功'
            }
          }
          
          if (this.onmessage) {
            this.onmessage(new MessageEvent('message', { 
              data: JSON.stringify(finishResponse) 
            }))
          }
        }, 400)
      }
    }, 50)
  }

  close() {
    this.readyState = WebSocket.CLOSED
    if (this.onclose) {
      this.onclose()
    }
  }
}

// Mock global WebSocket
global.WebSocket = MockWebSocket as any

describe('wrlMessage 工具类', () => {
  beforeEach(() => {
    vi.clearAllMocks()
  })

  afterEach(() => {
    closeTransferApp()
  })

  describe('openTransferApp', () => {
    it('应该能够成功连接到文件传输小程序', async () => {
      const result = await openTransferApp()
      expect(result).toBe(true)
    })
  })

  describe('uploadFile', () => {
    it('应该能够成功上传文件', async () => {
      const mockProgress = vi.fn()
      const mockComplete = vi.fn()
      const mockError = vi.fn()

      const config: UploadConfig = {
        url: 'http://localhost:3000/upload',
        filePath: 'D:\\test\\video.mp4',
        isForm: true,
        formFields: [
          {
            key: 'videoFile',
            value: 'D:\\test\\video.mp4',
            type: 'file'
          },
          {
            key: 'roomId',
            value: 'room123',
            type: 'content'
          }
        ],
        onProgress: mockProgress,
        onComplete: mockComplete,
        onError: mockError
      }

      const result = await uploadFile(config)

      expect(result.success).toBe(true)
      expect(result.httpCode).toBe(200)
      expect(result.message).toBe('上传成功')
      expect(mockProgress).toHaveBeenCalled()
      expect(mockComplete).toHaveBeenCalled()
      expect(mockError).not.toHaveBeenCalled()
    })

    it('应该能够处理上传失败的情况', async () => {
      // Mock 失败的 WebSocket
      const FailingWebSocket = class extends MockWebSocket {
        send(data: string) {
          setTimeout(() => {
            const request = JSON.parse(data)
            
            if (request.req === 'AxFile_Upload') {
              const errorResponse = {
                event: 'AxFile_UploadError',
                data: {
                  error: '网络连接失败'
                }
              }
              
              if (this.onmessage) {
                this.onmessage(new MessageEvent('message', { 
                  data: JSON.stringify(errorResponse) 
                }))
              }
            }
          }, 50)
        }
      }

      global.WebSocket = FailingWebSocket as any

      const mockError = vi.fn()
      const config: UploadConfig = {
        url: 'http://localhost:3000/upload',
        filePath: 'D:\\test\\video.mp4',
        onError: mockError
      }

      const result = await uploadFile(config)

      expect(result.success).toBe(false)
      expect(result.error).toBe('网络连接失败')
      expect(mockError).toHaveBeenCalledWith('网络连接失败')
    })

    it('应该能够处理流上传模式', async () => {
      const config: UploadConfig = {
        url: 'http://localhost:3000/upload',
        filePath: 'D:\\test\\video.mp4',
        isForm: false // 流上传
      }

      const result = await uploadFile(config)

      expect(result.success).toBe(true)
    })
  })

  describe('closeTransferApp', () => {
    it('应该能够正确关闭连接', () => {
      expect(() => closeTransferApp()).not.toThrow()
    })
  })
})
