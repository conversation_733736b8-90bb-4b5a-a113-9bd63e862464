import { DBSchema, IDBPDatabase, openDB } from "idb";

// 定义数据库模式
interface WitnessClientDB extends DBSchema {
  failed_uploads: {
    key: number;
    value: {
      id: number;
      roomId: string;
      fileName: string;
      failureReason: string;
      timestamp: string;
      retryCount: number;
    };
    indexes: {
      by_roomId: string;
      by_timestamp: string;
    };
  };
  pending_uploads: {
    key: number;
    value: {
      id?: number;
      roomId: string;
      fileName: string;
      filePath: string;
      fileSize: number;
      timestamp: string;
      uploadStatus: "pending" | "uploading" | "completed" | "failed";
    };
    indexes: {
      by_roomId: string;
      by_status: string;
      by_timestamp: string;
    };
  };
}

// 数据库名称和版本
const DB_NAME = "witness_client_db";
const DB_VERSION = 2;

// 打开数据库连接
let dbPromise: Promise<IDBPDatabase<WitnessClientDB>> | null = null;

const getDB = () => {
  if (!dbPromise) {
    dbPromise = openDB<WitnessClientDB>(DB_NAME, DB_VERSION, {
      upgrade(db, oldVersion) {
        // 创建失败上传记录表
        if (!db.objectStoreNames.contains("failed_uploads")) {
          const failedUploadsStore = db.createObjectStore("failed_uploads", {
            keyPath: "id",
            autoIncrement: true,
          });

          // 创建索引
          failedUploadsStore.createIndex("by_roomId", "roomId", {
            unique: false,
          });
          failedUploadsStore.createIndex("by_timestamp", "timestamp", {
            unique: false,
          });
        }

        // 创建待上传记录表（版本2新增）
        if (
          oldVersion < 2 &&
          !db.objectStoreNames.contains("pending_uploads")
        ) {
          const pendingUploadsStore = db.createObjectStore("pending_uploads", {
            keyPath: "id",
            autoIncrement: true,
          });

          // 创建索引
          pendingUploadsStore.createIndex("by_roomId", "roomId", {
            unique: false,
          });
          pendingUploadsStore.createIndex("by_status", "uploadStatus", {
            unique: false,
          });
          pendingUploadsStore.createIndex("by_timestamp", "timestamp", {
            unique: false,
          });
        }
      },
    });
  }
  return dbPromise;
};

// 失败上传记录的类型定义
export interface FailedUploadRecord {
  id?: number;
  roomId: string;
  fileName: string;
  failureReason: string;
  timestamp: string;
  retryCount: number;
}

// 待上传记录的类型定义
export interface PendingUploadRecord {
  id?: number;
  roomId: string;
  fileName: string;
  filePath: string;
  fileSize: number;
  timestamp: string;
  uploadStatus: "pending" | "uploading" | "completed" | "failed";
}

// 添加失败的上传记录
export const addFailedUpload = async (
  record: Omit<FailedUploadRecord, "id" | "retryCount">
) => {
  const db = await getDB();
  const recordWithRetry: FailedUploadRecord = {
    ...record,
    retryCount: 0,
  };
  return db.add("failed_uploads", recordWithRetry);
};

// 获取所有失败的上传记录
export const getAllFailedUploads = async (): Promise<FailedUploadRecord[]> => {
  const db = await getDB();
  return db.getAll("failed_uploads");
};

// 根据房间ID获取失败的上传记录
export const getFailedUploadsByRoomId = async (
  roomId: string
): Promise<FailedUploadRecord[]> => {
  const db = await getDB();
  return db.getAllFromIndex("failed_uploads", "by_roomId", roomId);
};

// 删除失败的上传记录
export const deleteFailedUpload = async (id: number) => {
  const db = await getDB();
  return db.delete("failed_uploads", id);
};

// 根据房间ID和文件名删除失败的上传记录
export const deleteFailedUploadByRoomAndFile = async (
  roomId: string,
  fileName: string
) => {
  const db = await getDB();
  const records = await getFailedUploadsByRoomId(roomId);
  const recordToDelete = records.find((record) => record.fileName === fileName);

  if (recordToDelete && recordToDelete.id) {
    return db.delete("failed_uploads", recordToDelete.id);
  }

  return false;
};

// 更新失败的上传记录（增加重试次数）
export const updateFailedUploadRetry = async (id: number) => {
  const db = await getDB();
  const record = await db.get("failed_uploads", id);

  if (record) {
    record.retryCount += 1;
    return db.put("failed_uploads", record);
  }

  return false;
};

// 清空所有失败的上传记录
export const clearAllFailedUploads = async () => {
  const db = await getDB();
  return db.clear("failed_uploads");
};

// 获取失败上传记录的数量
export const getFailedUploadsCount = async (): Promise<number> => {
  const db = await getDB();
  return db.count("failed_uploads");
};

// ==================== 待上传记录相关函数 ====================

// 添加待上传记录
export const addPendingUpload = async (
  record: Omit<PendingUploadRecord, "id" | "uploadStatus">
) => {
  const db = await getDB();
  const recordWithStatus = {
    ...record,
    uploadStatus: "pending" as const,
  };
  return db.add("pending_uploads", recordWithStatus);
};

// 获取所有待上传记录
export const getAllPendingUploads = async (): Promise<
  PendingUploadRecord[]
> => {
  const db = await getDB();
  return db.getAll("pending_uploads");
};

// 根据状态获取待上传记录
export const getPendingUploadsByStatus = async (
  status: PendingUploadRecord["uploadStatus"]
): Promise<PendingUploadRecord[]> => {
  const db = await getDB();
  return db.getAllFromIndex("pending_uploads", "by_status", status);
};

// 根据房间ID获取待上传记录
export const getPendingUploadsByRoomId = async (
  roomId: string
): Promise<PendingUploadRecord[]> => {
  const db = await getDB();
  return db.getAllFromIndex("pending_uploads", "by_roomId", roomId);
};

// 更新待上传记录状态
export const updatePendingUploadStatus = async (
  id: number,
  status: PendingUploadRecord["uploadStatus"]
) => {
  const db = await getDB();
  const record = await db.get("pending_uploads", id);

  if (record) {
    record.uploadStatus = status;
    return db.put("pending_uploads", record);
  }

  return false;
};

// 根据房间ID和文件名更新待上传记录状态
export const updatePendingUploadStatusByRoomAndFile = async (
  roomId: string,
  fileName: string,
  status: PendingUploadRecord["uploadStatus"]
) => {
  const db = await getDB();
  const records = await getPendingUploadsByRoomId(roomId);
  const recordToUpdate = records.find((record) => record.fileName === fileName);

  if (recordToUpdate && recordToUpdate.id) {
    recordToUpdate.uploadStatus = status;
    return db.put("pending_uploads", recordToUpdate);
  }

  return false;
};

// 删除待上传记录
export const deletePendingUpload = async (id: number) => {
  const db = await getDB();
  return db.delete("pending_uploads", id);
};

// 根据房间ID和文件名删除待上传记录
export const deletePendingUploadByRoomAndFile = async (
  roomId: string,
  fileName: string
) => {
  const db = await getDB();
  const records = await getPendingUploadsByRoomId(roomId);
  const recordToDelete = records.find((record) => record.fileName === fileName);

  if (recordToDelete && recordToDelete.id) {
    return db.delete("pending_uploads", recordToDelete.id);
  }

  return false;
};

// 获取待上传记录的数量
export const getPendingUploadsCount = async (): Promise<number> => {
  const db = await getDB();
  return db.count("pending_uploads");
};

// 关闭数据库连接
export const closeDB = async () => {
  if (dbPromise) {
    const db = await dbPromise;
    db.close();
    dbPromise = null;
  }
};
