import CryptoJS from "crypto-js";
import { Howl } from "howler";

const TTS_BASE_URL = "wss://tts.cloud.tencent.com/stream_ws";

interface TtsHelper {
  appId: string;
  secretId: string;
  secretKey: string;
  text: string;
  voiceType?: number;
  volume?: number;
  speed?: number;
  sampleRate?: number;
}

class TTSHelper {
  private appId: string;
  private secretId: string;
  private secretKey: string;

  constructor(appId: string, secretId: string, secretKey: string) {
    this.appId = appId;
    this.secretId = secretId;
    this.secretKey = secretKey;
  }

  private generateSignature(params: Record<string, any>): string {
    const sortedKeys = Object.keys(params).sort();
    const queryString = sortedKeys
      .map((key) => `${key}=${params[key]}`)
      .join("&");
    const stringToSign = `GETtts.cloud.tencent.com/stream_ws?${queryString}`;
    const hash = CryptoJS.HmacSHA1(stringToSign, this.secretKey);
    return encodeURIComponent(CryptoJS.enc.Base64.stringify(hash));
  }

  public generateUrl(params: TtsHelper): string {
    const timestamp = Math.floor(Date.now() / 1000);
    const sessionId = crypto.randomUUID();
    const queryParams = {
      Action: "TextToStreamAudioWS",
      AppId: this.appId,
      SecretId: this.secretId,
      Timestamp: timestamp,
      Expired: timestamp + 3600,
      SessionId: sessionId,
      Text: encodeURIComponent(params.text),
      VoiceType: params.voiceType || 101001,
      Volume: params.volume || 0,
      Speed: params.speed || 0,
      SampleRate: params.sampleRate || 16000,
    };
    const signature = this.generateSignature(queryParams);
    queryParams["Signature"] = signature;
    return `${TTS_BASE_URL}?${new URLSearchParams(queryParams).toString()}`;
  }

  public playTTS(url: string): void {
    const ws = new WebSocket(url);

    const audioChunks: Uint8Array[] = [];
    ws.onmessage = (event: MessageEvent) => {
      if (event.data instanceof Blob) {
        event.data
          .arrayBuffer()
          .then((buffer) => audioChunks.push(new Uint8Array(buffer)));
      }
    };

    ws.onclose = () => {
      const blob = new Blob(audioChunks, { type: "audio/pcm" });
      const arrayBuffer = blob.arrayBuffer();
      const sound = new Howl({
        src: [URL.createObjectURL(new Blob([arrayBuffer]))],
        format: ["pcm"],
      });
      sound.play();
    };
  }
}

export default TTSHelper;
