<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link href="https://cdn.bootcdn.net/ajax/libs/twitter-bootstrap/4.5.3/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.bootcdn.net/ajax/libs/bootstrap-select/1.13.18/css/bootstrap-select.min.css"
        rel="stylesheet">
    <link href="https://cdn.bootcdn.net/ajax/libs/toastr.js/latest/css/toastr.min.css" rel="stylesheet">
    <title>文件传输小程序测试</title>
</head>

<body style="text-align: center;">
    <div style="margin: 0 auto; width: 80%;">
        <form role="form">
            <h2 style="padding: 20px 0;">文件传输小程序</h2>
            <div class="btn-group" style="padding: 20px 0;">
                <button id="conn-svc" type="button" class="btn btn-primary">打开文件传输小程序</button>
            </div>
        </form>
        <form role="form">
            <h2 style="padding: 20px 0;">网络文件传输</h2>
            <div class="row">
                <div class="col-md-6">
                    <div class="input-group">
                        <div class="input-group-prepend">
                            <span class="input-group-text">文件路径</span>
                        </div>
                        <input id="file-path" class="form-control">
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="input-group">
                        <div class="input-group-prepend">
                            <span class="input-group-text">网址</span>
                        </div>
                        <input id="web-url" class="form-control">
                    </div>
                </div>
            </div>
            <br>
            <div class="row">
                <div class="col-md-4">
                    <div class="input-group">
                        <div class="input-group-prepend">
                            <span class="input-group-text">传输进度</span>
                        </div>
                        <input id="trans-ratio" class="form-control">
                        <div class="input-group-prepend">
                            <span class="input-group-text">%</span>
                        </div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="input-group">
                        <div class="input-group-prepend">
                            <span class="input-group-text">当前数据量</span>
                        </div>
                        <input id="curr-size" class="form-control">
                        <div class="input-group-prepend">
                            <span class="input-group-text">Bytes</span>
                        </div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="input-group">
                        <div class="input-group-prepend">
                            <span class="input-group-text">总数据量</span>
                        </div>
                        <input id="total-size" class="form-control">
                        <div class="input-group-prepend">
                            <span class="input-group-text">Bytes</span>
                        </div>
                    </div>
                </div>
            </div>
            <br>
            <div class="row">
                <div class="col-md-4">
                    <div class="input-group">
                        <div class="input-group-prepend">
                            <span class="input-group-text">实时速度</span>
                        </div>
                        <input id="real-speed" class="form-control">
                        <div class="input-group-append">
                            <span class="input-group-text">Bytes/Sec</span>
                        </div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="input-group">
                        <div class="input-group-prepend">
                            <span class="input-group-text">已用时间</span>
                        </div>
                        <input id="spend-time" class="form-control">
                        <div class="input-group-prepend">
                            <span class="input-group-text">Sec</span>
                        </div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="input-group">
                        <div class="input-group-prepend">
                            <span class="input-group-text">剩余时间</span>
                        </div>
                        <input id="remain-time" class="form-control">
                        <div class="input-group-prepend">
                            <span class="input-group-text">Sec</span>
                        </div>
                    </div>
                </div>
            </div>
            <br>
            <div class="row">
                <div class="col-md-12">
                    <div class="progress">
                        <div id="trans-prog" class="progress-bar"></div>
                    </div>
                </div>
            </div>
            <br>
            <div class="btn-group" style="padding: 20px 0;">
                <button id="upload-stream" type="button" class="btn btn-primary">文件流上传</button>
                <button id="upload-form" type="button" class="btn btn-primary">文件表单上传</button>
                <button id="download-file" type="button" class="btn btn-primary">文件下载</button>
            </div>
        </form>
    </div>
    <script src="https://cdn.bootcdn.net/ajax/libs/jquery/3.5.1/jquery.min.js"></script>
    <script src="https://cdn.bootcdn.net/ajax/libs/twitter-bootstrap/4.5.3/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.bootcdn.net/ajax/libs/bootstrap-select/1.13.18/js/bootstrap-select.min.js"></script>
    <script src="https://cdn.bootcdn.net/ajax/libs/bootstrap-select/1.13.18/js/i18n/defaults-zh_CN.min.js"></script>
    <script src="https://cdn.bootcdn.net/ajax/libs/toastr.js/latest/js/toastr.min.js"></script>
    <script src="./js/app_plugin.js"></script>
    <script>
        // 小程序请求 token
        let transToken = "sid=16&cid=apexsoft&tk=57499EE57BB81C13DD655E7B65CD7D1DF47C768125D66DB07E0EAE920F79EA462F3832B394CE88D27DC5F83011D2D59697FF890037692E193AA8977B6E4F485EF95776BB919AEADD04877B2D32C86A6B54C7C7DC662A775537D67EEE7A771F24FC2A51F5C8BF5B947578B14D6A7CF06CC235BF0D76C71A5A5D0B40D4E55C8DF61C495037C444F2D47B8647B5520B9FF3FA08B2726AFCC7B5C44A0659EDC705DBBBB9DDEFD25CD7ECF9BE1073FAB26A1D9A2C392CDB363B190CDD1AD9AE168BD32F06560142FF5007D579DD9893AFD98E4B157A8AF11742267EA8DA831DC3FB6BF2C116CFCA7FD467A713390E62795E1F614C411CAC5BA5EA28A49103923B61DE";

        // 文件上传下载小程序连接地址
        let transSvcUrl = "ws://localhost:20080?flag=1&pid=3c9cf3e3-b351-4397-adef-5985530c2c46&" + transToken;

        // 请求 ID 用来区分不同的请求操作
        let uploadStreamReqId = 0;
        let uploadFormReqId = 0;
        let downloadFileReqId = 0;

        let counter = 0;
        let transApp = null;

        $(document).ready(function () {
            setBtnInitState();

            toastr.options = {
                "closeButton": false,
                "debug": false,
                "newestOnTop": false,
                "progressBar": true,
                "positionClass": "toast-bottom-right",
                "preventDuplicates": false,
                "onclick": null,
                "showDuration": "300",
                "hideDuration": "1000",
                "timeOut": "5000",
                "extendedTimeOut": "1000",
                "showEasing": "swing",
                "hideEasing": "linear",
                "showMethod": "fadeIn",
                "hideMethod": "fadeOut"
            }

            $("#trans-ratio").attr("disabled", true);
            $("#curr-size").attr("disabled", true);
            $("#total-size").attr("disabled", true);
            $("#real-speed").attr("disabled", true);
            $("#spend-time").attr("disabled", true);
            $("#remain-time").attr("disabled", true);
        });

        $("#conn-svc").click(function () {
            $(this).attr("disabled", true);
            openTransApp();
        });

        $("#upload-stream").click(function () {
            streamUpload();
        });

        $("#upload-form").click(function () {
            formUpload();
        });

        $("#download-file").click(function () {
            fileDownload();
        });

        function setBtnInitState() {
            $("#conn-svc").attr("disabled", false);

            $("#upload-stream").attr("disabled", true);
            $("#upload-form").attr("disabled", true);
            $("#download-file").attr("disabled", true);
        }

        function setBtnOpenState() {
            $("#conn-svc").attr("disabled", true);

            $("#upload-stream").attr("disabled", false);
            $("#upload-form").attr("disabled", false);
            $("#download-file").attr("disabled", false);
        }

        // 打开文件上传下载小程序
        function openTransApp() {
            transApp = new DllPlugin(transSvcUrl);
            transApp.onMessage = handleRecvTransMsg;

            transApp.onAppConn = function () {
                setBtnOpenState();
            }
        }

        function streamUpload() {
            uploadStreamReqId = transApp.getRandomId();
            let msg = {
                req: "AxFile_Upload",
                rid: uploadStreamReqId,
                para: {
                    id: counter++,
                    url: $("#web-url").val(),
                    file: $("#file-path").val(),
                    is_form: false,
                    user: "",
                }
            };
            transApp.send(JSON.stringify(msg));
        }

        function formUpload(data) {
            uploadFormReqId = transApp.getRandomId();
            let msg = {
                req: "AxFile_Upload",
                rid: uploadFormReqId,
                para: {
                    id: counter++,
                    url: $("#web-url").val(),
                    file: $("#file-path").val(),
                    is_form: true,
                    user: "",
                    form: [
                        {
                            key: "videoFile",
                            value: $("#file-path").val(),
                            type: "file"
                        },
                        {
                            key: "token",
                            value: "test",
                            type: "content"
                        }
                    ]
                }
            };
            transApp.send(JSON.stringify(msg));
        }

        function fileDownload(info) {
            downloadFileReqId = transApp.getRandomId();
            let msg = {
                req: "AxFile_Download",
                rid: downloadFileReqId,
                para: {
                    id: counter++,
                    url: $("#web-url").val(),
                    file: $("#file-path").val(),
                    is_form: false,
                    is_cache: true,
                    user: ""
                }
            };
            transApp.send(JSON.stringify(msg));
        }

        function showTransProg(ratio, curr, total, speed, utime, rtime) {
            $("#trans-ratio").val(ratio.toFixed(2));
            $("#curr-size").val(curr);
            $("#total-size").val(total);
            $("#real-speed").val(speed);
            $("#spend-time").val(utime);
            $("#remain-time").val(rtime);
            $("#trans-prog").css("width", ratio + "%");
        }

        function handleRecvTransMsg(msg) {
            let data = JSON.parse(msg);

            if (data.ret != 0 && data.ret != undefined) {
                if (data.err != undefined) {
                    toastr.error(data.err);
                } else {
                    toastr.error("请求参数失败!");
                }
                return;
            }

            if (data.rid == uploadStreamReqId) {
                if (data.data.state) {
                    toastr.info("开始文件流上传!");
                } else {
                    if (data.data.err != undefined) {
                        toastr.error(data.data.err);
                    } else {
                        toastr.error("文件流上传失败!");
                    }
                }
            } else if (data.rid == uploadFormReqId) {
                if (data.data.state) {
                    toastr.info("开始表单上传成功!");
                } else {
                    if (data.data.err != undefined) {
                        toastr.error(data.data.err);
                    } else {
                        toastr.error("表单上传失败!");
                    }
                }
            } else if (data.rid == downloadFileReqId) {
                if (data.data.state) {
                    toastr.info("开始文件下载!");
                } else {
                    if (data.data.err != undefined) {
                        toastr.error(data.data.err);
                    } else {
                        toastr.error("文件下载失败!");
                    }
                }
            } else {
                if (data.event == "AxFile_UploadProg") {
                    if (data.data.state) {
                        showTransProg(data.data.ratio, data.data.current, data.data.total, data.data.speed, data.data.utime, data.data.rtime);
                    } else {
                        toastr.error("获取上传进度失败!");
                    }
                } else if (data.event == "AxFile_UploadFinished") {
                    if (data.data.state) {
                        toastr.success("上传文件成功 (Http Code: " + data.data.http_code + " 响应结果: " + data.data.msg + ")");
                    } else {
                        toastr.error("上传文件失败!");
                    }
                } else if (data.event == "AxFile_UploadError") {
                    toastr.error("上传文件失败 (" + data.data.error + ")");
                } else if (data.event == "AxFile_DownloadProg") {
                    if (data.data.state) {
                        showTransProg(data.data.ratio, data.data.current, data.data.total, data.data.speed, data.data.utime, data.data.rtime);
                    } else {
                        toastr.error("获取下载进度失败!");
                    }
                } else if (data.event == "AxFile_DownloadFinished") {
                    if (data.data.state) {
                        toastr.success("下载文件成功!");
                    } else {
                        toastr.error("下载文件失败!");
                    }
                } else if (data.event == "AxFile_DownloadError") {
                    toastr.error("下载文件失败 (" + data.data.error + ")");
                }
            }
        }
    </script>
</body>

</html>