#user ygt;
user root;
worker_processes 8; #cpu
worker_cpu_affinity 00000001 00000010 00000100 00001000 00010000 00100000 01000000 10000000;

#error_log  logs/error.log;
#error_log  logs/error.log  notice;
#error_log  logs/error.log  info;

pid        logs/nginx.pid;
worker_rlimit_nofile 204800;

events {
  use epoll;
  worker_connections 204800;
}

    
http {

server_tokens off;
port_in_redirect off;
include mime.types;
default_type application/octet-stream;
charset utf-8;
server_names_hash_bucket_size 128;
client_header_buffer_size 32k;
large_client_header_buffers 4 64k; 

client_max_body_size 5120m;
client_body_buffer_size 128k;
proxy_connect_timeout 600;
proxy_read_timeout 600;
proxy_send_timeout 600;
proxy_buffer_size 64k;
proxy_buffers   4 32k;
proxy_busy_buffers_size 64k;
proxy_temp_file_write_size 64k;


sendfile on;
tcp_nopush on;
keepalive_timeout 2400;
send_timeout 2400; 
client_header_timeout 2400; 
client_body_timeout 2400;

open_file_cache max=204800 inactive=20s;
open_file_cache_min_uses 1;
open_file_cache_valid 30s;
tcp_nodelay on;
gzip on;
gzip_min_length 1k;
gzip_buffers 4 16k;
gzip_http_version 1.0;
gzip_comp_level 2;
gzip_types text/plain application/x-javascript text/css application/xml;
gzip_vary on;  
     
log_format  main  '$remote_addr - $remote_user [$time_local] "$request" '
                      '$upstream_addr '
                      '$status $body_bytes_sent "$http_referer" '
                      '"$http_user_agent" "$http_x_forwarded_for" '
                      ' $request_time $upstream_response_time $upstream_connect_time $upstream_header_time';
                      
                     
    #3.2.1
    upstream ygtdesk{ # 这个是 https://*************:1810/welcome.do
      ip_hash;
      server *************:8080 max_fails=5 fail_timeout=60s;
      #server ***************:8089 max_fails=5 fail_timeout=60s;
      #server ***************:18889 max_fails=5 fail_timeout=60s;
      keepalive_requests 10000;
      keepalive_timeout 600;
   }

    #3.2.2
    upstream ygtserver{
      server *************:9999 max_fails=5 fail_timeout=60s;
     # sticky;
    }
    
    



    # HTTPS server
    # 3.2.4(ssl)
    server {

        
     
       listen       8088 ssl http2;
       server_name  localhost;
       
         if ($time_iso8601 ~ '(\d{4}-\d{2}-\d{2})') {
               set $tttt $1;
        }
			
       access_log  logs/access-$tttt.log  main;

       ssl_certificate      apex1921683214server.crt;
       ssl_certificate_key  apex1921683214server.key;
       
        ssl_session_cache    shared:SSL:1m;
        ssl_session_timeout  1d;
        ssl_protocols TLSv1 TLSv1.1 TLSv1.2;
        ssl_ciphers 'ECDHE-RSA-AES128-GCM-SHA256:ECDHE-ECDSA-AES128-GCM-SHA256:ECDHE-RSA-AES256-GCM-SHA384:ECDHE-ECDSA-AES256-GCM-SHA384:DHE-RSA-AES128-GCM-SHA256:DHE-DSS-AES128-GCM-SHA256:kEDH+AESGCM:ECDHE-RSA-AES128-SHA256:ECDHE-ECDSA-AES128-SHA256:ECDHE-RSA-AES128-SHA:ECDHE-ECDSA-AES128-SHA:ECDHE-RSA-AES256-SHA384:ECDHE-ECDSA-AES256-SHA384:ECDHE-RSA-AES256-SHA:ECDHE-ECDSA-AES256-SHA:DHE-RSA-AES128-SHA256:DHE-RSA-AES128-SHA:DHE-DSS-AES128-SHA256:DHE-RSA-AES256-SHA256:DHE-DSS-AES256-SHA:DHE-RSA-AES256-SHA:AES128-GCM-SHA256:AES256-GCM-SHA384:AES128-SHA256:AES256-SHA256:AES128-SHA:AES256-SHA:AES:CAMELLIA:DES-CBC3-SHA:!aNULL:!eNULL:!EXPORT:!DES:!RC4:!MD5:!PSK:!aECDH:!EDH-DSS-DES-CBC3-SHA:!EDH-RSA-DES-CBC3-SHA:!KRB5-DES-CBC3-SHA';
        ssl_prefer_server_ciphers  on;
        #add_header Strict-Transport-Security max-age=15768000;
        #server_name_in_redirect off;         
        index index.html login.jsp;
        

        #*******
        location /{
           proxy_pass              http://ygtdesk/;    
	   #proxy_redirect          off;
            proxy_redirect          http://  https://;
            proxy_set_header        Host $host:$server_port;
            proxy_set_header        X-Real-IP $remote_addr;
            proxy_set_header        X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header        X-Forwarded-Proto $scheme;
           client_max_body_size    100m;
       }
	
       location ^~ /nginx_status {
       stub_status on;
       access_log   off;
       }

      # location /AiEngine {
          #  proxy_pass              http://*************:8150/AiEngine/qyxx/qyxxcx.sdo;
         #   proxy_set_header        Host $host:$server_port;
         #  proxy_set_header        X-Real-IP $remote_addr;
          #  proxy_set_header        X-Forwarded-For $proxy_add_x_forwarded_for;
          #  proxy_set_header        X-Forwarded-Proto $scheme;
           # client_max_body_size    100m;
 #     }

             location ^~ /AiEngine/{     
             proxy_cookie_path /AiEngine/ /;
             #proxy_pass              http://*************:8150;    
              proxy_pass              http://*************:8030;
              proxy_redirect          off;
              proxy_set_header        Host $host:8088;
              proxy_set_header        X-Real-IP $remote_addr;
              proxy_set_header        X-Forwarded-For $proxy_add_x_forwarded_for;
              proxy_set_header        X-Forwarded-Proto $scheme;
             client_max_body_size    100m;
       } 
      

	 





       location ^~ /oapush/{     
             proxy_cookie_path /oapush/ /;
	     proxy_pass              http://*************:8199/oapush/;    
            # proxy_redirect          off;
             proxy_redirect http://$host https://$host:$server_port;
             proxy_set_header        Host $host:1810;
             proxy_set_header        X-Real-IP $remote_addr;
             proxy_set_header        X-Forwarded-For $proxy_add_x_forwarded_for;
             proxy_set_header        X-Forwarded-Proto $scheme;
             client_max_body_size    100m;
      } 
      
     
		  
		
		  location   /servlet/ {
		    proxy_pass  http://*************:9090/servlet/;
		   
             proxy_set_header        X-Real-IP $remote_addr;
             proxy_set_header        X-Forwarded-For $proxy_add_x_forwarded_for;
             proxy_set_header        X-Forwarded-Proto $scheme;
             client_max_body_size    100m;
		  }
		  location ^~ /sdfxc/{     
		    proxy_pass  http://*************:9090/;
		     sub_filter '<a href="/'  '<a href="/sdfxc';        
	       sub_filter '<img src="/' '<img src="/sdfxc/';
	       sub_filter '<script src="/' '<script src="/sdfxc/';
	       sub_filter 'type="text/javascript" src="/' 'type="text/javascript" src="/sdfxc/';
	       sub_filter '<link href="/' '<link href="/sdfxc/';
	       sub_filter 'url(/' 'url(/sdfxc/';	      
	       sub_filter "@import url('/" "@import url('/sdfxc/";
         sub_filter_once off;  # 查找并替换多次         
		      proxy_set_header        X-Real-IP $remote_addr;
             proxy_set_header        X-Forwarded-For $proxy_add_x_forwarded_for;
             proxy_set_header        X-Forwarded-Proto $scheme;
		  }	
		  
		  location  /framework/ {
		    proxy_pass  http://*************:9090/framework/;
		  proxy_set_header        X-Real-IP $remote_addr;
             proxy_set_header        X-Forwarded-For $proxy_add_x_forwarded_for;
             proxy_set_header        X-Forwarded-Proto $scheme;
		  }
		  
		  location  /plugins/layer/ {
		    proxy_pass  http://*************:9090/plugins/layer/;
		   proxy_set_header        X-Real-IP $remote_addr;
             proxy_set_header        X-Forwarded-For $proxy_add_x_forwarded_for;
             proxy_set_header        X-Forwarded-Proto $scheme;
		  }  
		  location  /plugins/frame/ {
		    proxy_pass  http://*************:9090/plugins/frame/;
		    proxy_set_header        X-Real-IP $remote_addr;
             proxy_set_header        X-Forwarded-For $proxy_add_x_forwarded_for;
             proxy_set_header        X-Forwarded-Proto $scheme;
		  } 
		  location  /bootstrap/ {
		    proxy_pass  http://*************:9090/bootstrap/;
		    proxy_set_header        X-Real-IP $remote_addr;
             proxy_set_header        X-Forwarded-For $proxy_add_x_forwarded_for;
             proxy_set_header        X-Forwarded-Proto $scheme;
		  } 
		  location  /plugins/ueditor/ {
		    proxy_pass  http://*************:9090/plugins/ueditor/;
		    proxy_set_header        X-Real-IP $remote_addr;
             proxy_set_header        X-Forwarded-For $proxy_add_x_forwarded_for;
             proxy_set_header        X-Forwarded-Proto $scheme;
		  }
		  		 
		  location  /plugins/chart/ {
		    proxy_pass  http://*************:9090/plugins/chart/;
		   proxy_set_header        X-Real-IP $remote_addr;
             proxy_set_header        X-Forwarded-For $proxy_add_x_forwarded_for;
             proxy_set_header        X-Forwarded-Proto $scheme;
		  } 
		  location   /plugins/fxckh/ {
		    proxy_pass  http://*************:9090/plugins/fxckh/;
		   proxy_set_header        X-Real-IP $remote_addr;
             proxy_set_header        X-Forwarded-For $proxy_add_x_forwarded_for;
             proxy_set_header        X-Forwarded-Proto $scheme;
		  } 
		  location   /plugins/codemirror/ {
		    proxy_pass  http://*************:9090/plugins/codemirror/;
		   proxy_set_header        X-Real-IP $remote_addr;
             proxy_set_header        X-Forwarded-For $proxy_add_x_forwarded_for;
             proxy_set_header        X-Forwarded-Proto $scheme;
		  } 
		  location   /plugins/miniui/ {
		    proxy_pass  http://*************:9090/plugins/miniui/;
		    proxy_set_header        X-Real-IP $remote_addr;
             proxy_set_header        X-Forwarded-For $proxy_add_x_forwarded_for;
             proxy_set_header        X-Forwarded-Proto $scheme;
		  } 
		  
		  location ^~ /slxt/{     
             proxy_cookie_path /slxt/ /;
             proxy_pass              https://*************:8158;    
             proxy_redirect          off;
             proxy_set_header        Host $host:8088;
             proxy_set_header        X-Real-IP $remote_addr;
             proxy_set_header        X-Forwarded-For $proxy_add_x_forwarded_for;
             proxy_set_header        X-Forwarded-Proto $scheme;
             client_max_body_size    100m;
      }

        location ^~ /v1/newqueue/{     
             #proxy_cookie_path /pdfw/ /;
              proxy_pass              http://*************:8080;
              proxy_redirect          off;
              proxy_set_header        Host $host:8088;
              proxy_set_header        X-Real-IP $remote_addr;
              proxy_set_header        X-Forwarded-For $proxy_add_x_forwarded_for;
              proxy_set_header        X-Forwarded-Proto $scheme;
             client_max_body_size    100m;
       } 
	  location ^~ /a5report/{     
             proxy_cookie_path /a5report/ /;
             proxy_pass              http://*************:8020;    
             proxy_redirect          off;
             proxy_set_header        Host $host:$server_port;
             proxy_set_header        X-Real-IP $remote_addr;
             proxy_set_header        X-Forwarded-For $proxy_add_x_forwarded_for;
             proxy_set_header        X-Forwarded-Proto $scheme;
             client_max_body_size    100m;
       } 

      location ^~ /drivers_download/ {
	  proxy_set_header        Host $host:8081;

	    if ($request_filename ~* ^.*?\.(txt|png|doc|pdf|rar|gz|zip|docx|exe|xlsx|ppt|pptx)$){
            add_header Content-Disposition: 'attachment;';
      }
      
      
		  root /home/<USER>/app/;
     } 
    }
    
    
    server {

        
     
       listen       6088 ssl http2;
       server_name  localhost;
       
         if ($time_iso8601 ~ '(\d{4}-\d{2}-\d{2})') {
               set $tttt $1;
        }

       access_log  logs/access-$tttt.log  main;

       ssl_certificate      apex1921683214server.crt;
       ssl_certificate_key  apex1921683214server.key;
       
        ssl_session_cache    shared:SSL:1m;
        ssl_session_timeout  1d;
        ssl_protocols TLSv1 TLSv1.1 TLSv1.2;
        ssl_ciphers 'ECDHE-RSA-AES128-GCM-SHA256:ECDHE-ECDSA-AES128-GCM-SHA256:ECDHE-RSA-AES256-GCM-SHA384:ECDHE-ECDSA-AES256-GCM-SHA384:DHE-RSA-AES128-GCM-SHA256:DHE-DSS-AES128-GCM-SHA256:kEDH+AESGCM:ECDHE-RSA-AES128-SHA256:ECDHE-ECDSA-AES128-SHA256:ECDHE-RSA-AES128-SHA:ECDHE-ECDSA-AES128-SHA:ECDHE-RSA-AES256-SHA384:ECDHE-ECDSA-AES256-SHA384:ECDHE-RSA-AES256-SHA:ECDHE-ECDSA-AES256-SHA:DHE-RSA-AES128-SHA256:DHE-RSA-AES128-SHA:DHE-DSS-AES128-SHA256:DHE-RSA-AES256-SHA256:DHE-DSS-AES256-SHA:DHE-RSA-AES256-SHA:AES128-GCM-SHA256:AES256-GCM-SHA384:AES128-SHA256:AES256-SHA256:AES128-SHA:AES256-SHA:AES:CAMELLIA:DES-CBC3-SHA:!aNULL:!eNULL:!EXPORT:!DES:!RC4:!MD5:!PSK:!aECDH:!EDH-DSS-DES-CBC3-SHA:!EDH-RSA-DES-CBC3-SHA:!KRB5-DES-CBC3-SHA';
        ssl_prefer_server_ciphers  on;
        #add_header Strict-Transport-Security max-age=15768000;
        server_name_in_redirect off;         
        index index.html login.jsp;
        

        #*******
         location   / {
		    proxy_pass  http://*************:9090/;
		   
             proxy_set_header        X-Real-IP $remote_addr;
             proxy_set_header        X-Forwarded-For $proxy_add_x_forwarded_for;
             proxy_set_header        X-Forwarded-Proto $scheme;
             client_max_body_size    100m;
		  }
                         		  				 
    }


    # HTTP server
    # 3.2.5
    server {
        listen       9998;
        server_name  localhost;
        
        if ($time_iso8601 ~ '(\d{4}-\d{2}-\d{2})') {
                set $tttt $1;
        }

       access_log  logs/access-$tttt.log  main;  

       
        #
        location /{
            proxy_pass              http://ygtserver/;    
            proxy_set_header        Host $host:9998;
            proxy_set_header        X-Real-IP $remote_addr;
            proxy_set_header        X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header        X-Forwarded-Proto $scheme;
            client_max_body_size    100m;
       }
  
    }
    
    
    
    server {
        listen		8012;
	server_name  localhost;
	#close the outer ip port
	#listen       8099;
        #server_name  ygt.apexsoft.com.cn;
       
       if ($http_Host !~* ^ygt.apexsoft.com.cn$)
        {
        return 403;
        }
       if ($host != 'ygt.apexsoft.com.cn')
        {
        return 403;
        }
        
        if ($time_iso8601 ~ '(\d{4}-\d{2}-\d{2})') {
                set $tttt $1;
        }
   
       access_log  logs/access-$tttt.log  main;  
   
       
        #
        location /{
		#proxy_pass		http://**********:8082;	
            #proxy_redirect 		http://$host https://$host;   
            #proxy_set_header        Host $host;
            #proxy_set_header        X-Real-IP $remote_addr;
            #proxy_set_header        X-Forwarded-For $proxy_add_x_forwarded_for;
            #proxy_set_header        X-Forwarded-Proto $scheme;
            #client_max_body_size    100m;
		root   html;
            index  index.html index.htm;
       }
	   
	   
        location ^~ /app{     
	   				proxy_pass              http://*************:9091/app;
            proxy_redirect          off;
            proxy_set_header        Host $host:$server_port;
            proxy_set_header        X-Real-IP $remote_addr;
            proxy_set_header        X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header        X-Forwarded-Proto $scheme;
            client_max_body_size    100m;
        } 
	   location ^~ /wskh{     
	   				proxy_pass 		     	http://*************:9908/wskh;
						#proxy_pass 		     	http://*************:8089/wskh;
            #proxy_pass              http://*************:9909/wskh;    
            proxy_redirect          off;
            proxy_set_header        Host $host:$server_port;
            proxy_set_header        X-Real-IP $remote_addr;
            proxy_set_header        X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header        X-Forwarded-Proto $scheme;
            client_max_body_size    100m;
        } 
       
        location ^~ /anychath5/{     
            #proxy_cookie_path /anychath5/ /;
            proxy_pass              http://*************:8099/;    
            proxy_redirect          off;
            proxy_set_header        Host $host:$server_port;
            proxy_set_header        X-Real-IP $remote_addr;
            proxy_set_header        X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header        X-Forwarded-Proto $scheme;
            client_max_body_size    100m;
        } 
		
		location ^~ /cowork{     
            proxy_pass              http://*************:8083/cowork;    
            proxy_redirect          off;
            proxy_set_header        Host $host:$server_port;
            proxy_set_header        X-Real-IP $remote_addr;
            proxy_set_header        X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header        X-Forwarded-Proto $scheme;
            client_max_body_size    100m;
        } 
		
		location ^~ /ydcj{     
            proxy_pass              http://*************:9099/ydcj;    
            proxy_redirect          off;
            proxy_set_header        Host $host:$server_port;
            proxy_set_header        X-Real-IP $remote_addr;
            proxy_set_header        X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header        X-Forwarded-Proto $scheme;
            client_max_body_size    100m;
        } 
		
		location ^~ /fxcsl{     
            proxy_pass              http://*************:8089/fxcsl;    
            proxy_redirect          off;
            proxy_set_header        Host $host:$server_port;
            proxy_set_header        X-Real-IP $remote_addr;
            proxy_set_header        X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header        X-Forwarded-Proto $scheme;
            client_max_body_size    5120m;
            proxy_read_timeout 			240;
            proxy_send_timeout 3000;
		        proxy_connect_timeout 3000;
		        keepalive_timeout 2400;
						send_timeout 2400; 
        } 
		
		location ^~ /ipad{     
            proxy_pass              http://*************:9099/ipad;    
            proxy_redirect          off;
            proxy_set_header        Host $host:$server_port;
            proxy_set_header        X-Real-IP $remote_addr;
            proxy_set_header        X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header        X-Forwarded-Proto $scheme;
            client_max_body_size    100m;
        }

        location = /ipad/xyzq/cs/soft/app/XYSC.ipa {
            proxy_pass              https://**************:8078/soft/app/XYSC.ipa;
            proxy_redirect          off;
            proxy_set_header        Host $host:$server_port;
            proxy_set_header        X-Real-IP $remote_addr;
            proxy_set_header        X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header        X-Forwarded-Proto $scheme;
            client_max_body_size    100m;
        }
		
		location = /ipad/xyzq/fz/soft/app/XYSC.ipa {
            proxy_pass              https://**************:8078/soft/app/XYSC.ipa;
            proxy_redirect          off;
            proxy_set_header        Host $host:$server_port;
            proxy_set_header        X-Real-IP $remote_addr;
            proxy_set_header        X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header        X-Forwarded-Proto $scheme;
            client_max_body_size    100m;
        }
 
		
		location ^~ /slxt/h5sl{     
            proxy_pass              http://*************:8150/slxt/h5sl;    
            proxy_redirect          off;
            proxy_set_header        Host $host:$server_port;
            proxy_set_header        X-Real-IP $remote_addr;
            proxy_set_header        X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header        X-Forwarded-Proto $scheme;
            client_max_body_size    100m;
        } 
		
		location /ws {
            proxy_pass              http://*************:7070/ws;
            proxy_set_header        Host $host:$server_port;
            proxy_http_version 1.1;
            proxy_set_header 		Upgrade $http_upgrade;
            proxy_set_header 		Connection "Upgrade";
            proxy_set_header 		X-Real-IP $remote_addr;
        }
		
		location /queueDemo {
            proxy_pass              http://*************:8081/queueDemo;
            proxy_set_header        Host $host:$server_port;
            proxy_set_header        X-Real-IP $remote_addr;
            proxy_set_header        X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header        X-Forwarded-Proto $scheme;
            client_max_body_size    100m;
        }
		
		location ^~ /audioDemo{
            proxy_pass              http://*************:8081/audioDemo;
	proxy_redirect          off;
            proxy_set_header        Host $host:$server_port;
            proxy_set_header        X-Real-IP $remote_addr;
            proxy_set_header        X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header        X-Forwarded-Proto $scheme;
            client_max_body_size    100m;
        }

		location ^~ /uploadVideo{
            proxy_pass              http://*************:9091/uploadVideo;
	proxy_redirect          off;
            proxy_set_header        Host $host:$server_port;
            proxy_set_header        X-Real-IP $remote_addr;
            proxy_set_header        X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header        X-Forwarded-Proto $scheme;
            client_max_body_size    100m;
        }
		
		location /openaccount {
            proxy_pass              http://*************:8091/openaccount;
            proxy_set_header        Host $host:$server_port;
            proxy_set_header        X-Real-IP $remote_addr;
            proxy_set_header        X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header        X-Forwarded-Proto $scheme;
            client_max_body_size    100m;
        }

	location /jgyslmobile {
            proxy_pass              http://*************:6080/jgyslmobile;
            proxy_set_header        Host $host:$server_port;
            proxy_set_header        X-Real-IP $remote_addr;
            proxy_set_header        X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header        X-Forwarded-Proto $scheme;
            client_max_body_size    100m;
        }
	location /jgyslserver {
            proxy_pass              http://*************:6080/jgyslserver;
            proxy_set_header        Host $host:$server_port;
            proxy_set_header        X-Real-IP $remote_addr;
            proxy_set_header        X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header        X-Forwarded-Proto $scheme;
            client_max_body_size    100m;
        }
		
		location ^~ /ydywbl{     
            proxy_pass              http://*************:8081/ydywbl;    
			proxy_redirect 			http://$host https://$host;   
            proxy_set_header        Host $host:$server_port;
            proxy_set_header        X-Real-IP $remote_addr;
            proxy_set_header        X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header        X-Forwarded-Proto $scheme;
            client_max_body_size    100m;
        } 

	location ^~ /oapush/{     
             proxy_cookie_path /oapush/ /;
	     proxy_pass              http://*************:9080/oapush/;    
            # proxy_redirect          off;
             proxy_redirect http://$host https://$host:$server_port;
             proxy_set_header        Host $host:1810;
             proxy_set_header        X-Real-IP $remote_addr;
             proxy_set_header        X-Forwarded-For $proxy_add_x_forwarded_for;
             proxy_set_header        X-Forwarded-Proto $scheme;
             client_max_body_size    100m;
      } 

    }
    
    
        
   # server {
   #     listen       8940;
   #     server_name  localhost;
   #     
   #     if ($time_iso8601 ~ '(\d{4}-\d{2}-\d{2})') {
   #             set $tttt $1;
   #     }
   #
   #    access_log  logs/access-$tttt.log  main;  
   #
   #    
   #     #
   #     location /{
   #         proxy_pass              http://*************:8940/;    
   #         proxy_set_header        Host $host:8940;
   #         proxy_set_header        X-Real-IP $remote_addr;
   #         proxy_set_header        X-Forwarded-For $proxy_add_x_forwarded_for;
   #         proxy_set_header        X-Forwarded-Proto $scheme;
   #         client_max_body_size    100m;
   #    }
   #     
   #    # location ^~ /anychath5/{     
   #    #     #proxy_cookie_path /anychath5/ /;
   #    #     proxy_pass              http://*************:8099/;    
   #    #     proxy_redirect          off;
   #    #     proxy_set_header        Host $host:$server_port;
   #    #     proxy_set_header        X-Real-IP $remote_addr;
   #    #     proxy_set_header        X-Forwarded-For $proxy_add_x_forwarded_for;
   #    #     proxy_set_header        X-Forwarded-Proto $scheme;
   #    #     client_max_body_size    100m;
   #    # } 
   # }  
   
   
   #upstream ygt.apexsoft.com.cn.8940 {
   #     server *************:8080 max_fails=0 weight=1;
   # }
   
   #server {
   #     listen 8940 ssl;
   #     server_name ygt1.apexsoft.com.cn; #
   #     #
   #     charset utf-8;
   #     #access_log
   #     access_log  logs/ygt.apexsoft.com.cn.8940.access.log  main;
   #     #ssl on;
   #     ssl_certificate 1_ygt.apexsoft.com.cn_bundle.crt;
   #     ssl_certificate_key 2_ygt.apexsoft.com.cn.key;
   #     ssl_session_timeout 5m;
   #     ssl_protocols TLSv1.2; #
   #     ssl_ciphers ECDHE-RSA-AES128-GCM-SHA256:HIGH:!aNULL:!MD5:!RC4:!DHE;#
   #     ssl_prefer_server_ciphers on;
   #     location / {
   #         proxy_pass     http://ygt.apexsoft.com.cn.8940;
   #         proxy_redirect          http://  https://;
   #         proxy_set_header        Host $host:8940;
   #         client_max_body_size  400m;
   #         proxy_set_header   X-Real-IP $remote_addr;
   #     }
   #     error_page   500 502 503 504  /50x.html;
   #     location = /50x.html {
   #         root   html;
   #     }
   # }
    #https
   #server { 
   #      listen   8940; 
   #      server_name  ygt1.apexsoft.com.cn; 
   #      location / { 
   #        rewrite (.*) https://ygt1.apexsoft.com.cn$1 permanent; 
   #     } 
   # }
   
    
    # HTTPS server
    # 3.2.4(ssl)
    server {
       listen       1810 ssl http2;
       server_name  localhost;

       access_log  logs/access-$tttt.log  main;

       ssl_certificate      apex1921683214server.crt;
       ssl_certificate_key  apex1921683214server.key;
       
        ssl_session_cache    shared:SSL:1m;
        ssl_session_timeout  1d;
        ssl_protocols TLSv1 TLSv1.1 TLSv1.2;
        ssl_ciphers 'ECDHE-RSA-AES128-GCM-SHA256:ECDHE-ECDSA-AES128-GCM-SHA256:ECDHE-RSA-AES256-GCM-SHA384:ECDHE-ECDSA-AES256-GCM-SHA384:DHE-RSA-AES128-GCM-SHA256:DHE-DSS-AES128-GCM-SHA256:kEDH+AESGCM:ECDHE-RSA-AES128-SHA256:ECDHE-ECDSA-AES128-SHA256:ECDHE-RSA-AES128-SHA:ECDHE-ECDSA-AES128-SHA:ECDHE-RSA-AES256-SHA384:ECDHE-ECDSA-AES256-SHA384:ECDHE-RSA-AES256-SHA:ECDHE-ECDSA-AES256-SHA:DHE-RSA-AES128-SHA256:DHE-RSA-AES128-SHA:DHE-DSS-AES128-SHA256:DHE-RSA-AES256-SHA256:DHE-DSS-AES256-SHA:DHE-RSA-AES256-SHA:AES128-GCM-SHA256:AES256-GCM-SHA384:AES128-SHA256:AES256-SHA256:AES128-SHA:AES256-SHA:AES:CAMELLIA:DES-CBC3-SHA:!aNULL:!eNULL:!EXPORT:!DES:!RC4:!MD5:!PSK:!aECDH:!EDH-DSS-DES-CBC3-SHA:!EDH-RSA-DES-CBC3-SHA:!KRB5-DES-CBC3-SHA';
        ssl_prefer_server_ciphers  on;
        #add_header Strict-Transport-Security max-age=15768000;
        server_name_in_redirect off;         
        index index.html login.jsp;
         # 设置一个变量，用于判断是否增加SameSite=None属性
				set $cookiePathMagicFlag '';
				# 00~69 之间Chrome, 设置为-evil'
				if ($http_user_agent ~ "Chrome/([0-6][0-9]\.)"){

				    set $cookiePathMagicFlag '-evil';
				}

        #*******
        location /{
           proxy_pass              http://ygtdesk/; 
          # proxy_redirect          off;#http://  https://; 
         proxy_redirect http://$host https://$host:$server_port;
            # proxy_redirect          http:// https://;#$scheme://;                        
           proxy_set_header        Host $host:1810; 
           #proxy_set_header Host $host;
           proxy_set_header X-Real-IP $remote_addr;
           proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
           proxy_set_header X-Forwarded-Proto "https";
           client_max_body_size    100m;
           proxy_cookie_path /$cookiePathMagicFlag "/; httponly; secure; SameSite=None";
       }
       
       
       location ^~ /nginx_status {
       stub_status on;
       access_log   off;
       }

      # location /AiEngine {
          #  proxy_pass              http://*************:8150/AiEngine/qyxx/qyxxcx.sdo;
         #   proxy_set_header        Host $host:$server_port;
         #  proxy_set_header        X-Real-IP $remote_addr;
          #  proxy_set_header        X-Forwarded-For $proxy_add_x_forwarded_for;
          #  proxy_set_header        X-Forwarded-Proto $scheme;
           # client_max_body_size    100m;
 #     }

             location ^~ /AiEngine/{     
             proxy_cookie_path /AiEngine/ /;
             #proxy_pass              http://*************:8150;    
              proxy_pass              http://*************:8030;
              proxy_redirect          off;
              proxy_set_header        Host $host:1810;
              proxy_set_header        X-Real-IP $remote_addr;
              proxy_set_header        X-Forwarded-For $proxy_add_x_forwarded_for;
              proxy_set_header        X-Forwarded-Proto $scheme;
             client_max_body_size    100m;
       } 
       
        location ^~ /slxt/{     
             proxy_cookie_path /slxt/ /;
             proxy_pass              https://*************:8158;    
             proxy_redirect          off;
             proxy_set_header        Host $host:1810;
             proxy_set_header        X-Real-IP $remote_addr;
             proxy_set_header        X-Forwarded-For $proxy_add_x_forwarded_for;
             proxy_set_header        X-Forwarded-Proto $scheme;
             client_max_body_size    100m;
      }
location ^~ /a5report/{     
             proxy_cookie_path /a5report/ /;
             proxy_pass              http://*************:8020;    
             proxy_redirect          off;
             proxy_set_header        Host $host:$server_port;
             proxy_set_header        X-Real-IP $remote_addr;
             proxy_set_header        X-Forwarded-For $proxy_add_x_forwarded_for;
             proxy_set_header        X-Forwarded-Proto $scheme;
             client_max_body_size    100m;
       } 

       # witness-agent
		location /witness-agent {
			alias /home/<USER>/shangzheng-wintess-server/client;  # 修改为实际的目录路径
			try_files $uri $uri/ /witness-agent/index.html;


			# 开启 gzip 压缩
			gzip on;
			gzip_min_length 1k;
			gzip_comp_level 6;
			gzip_types text/plain application/javascript application/x-javascript text/css application/xml text/javascript application/x-httpd-php image/jpeg image/gif image/png application/json;

			# 缓存配置
			expires 1d;
			add_header Cache-Control "no-cache";
		}

			 # 普通 API 请求
		location ^~ /ai-server/ {
			proxy_pass  http://*************:8066/;
				proxy_redirect off;

				   # 确保正确传递 SSL 信息
				proxy_set_header Host $host:$server_port;
				proxy_set_header X-Real-IP $remote_addr;
				proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
				proxy_set_header X-Forwarded-Proto https;  # 强制使用 HTTPS
				proxy_set_header X-Forwarded-Port 1896;    # 指定正确的端口

				# 确保只有包含 WebSocket 升级头的请求才会被处理为 WebSocket
				if ($http_upgrade != "websocket") {
					break;
				}

				# WebSocket over SSL 配置
				proxy_http_version 1.1;
				proxy_set_header Upgrade $http_upgrade;
				# proxy_set_header Connection $connection_upgrade;

				proxy_read_timeout 300s;     # 增加超时时间
				proxy_send_timeout 300s;
				proxy_connect_timeout 750s;
				proxy_buffering off;
				client_max_body_size 100m;

				# 允许跨域
				add_header 'Access-Control-Allow-Origin' '*' always;
				add_header 'Access-Control-Allow-Methods' 'GET, POST, OPTIONS' always;
				add_header 'Access-Control-Allow-Headers' 'DNT,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Range,Authorization' always;
				add_header 'Access-Control-Expose-Headers' 'Content-Length,Content-Range' always;

				if ($request_method = 'OPTIONS') {
					add_header 'Access-Control-Max-Age' 1728000;
					add_header 'Content-Type' 'text/plain; charset=utf-8';
					add_header 'Content-Length' 0;
					return 204;
				}
		   }
    }

# HTTP server
    # 3.2.4(ssl)
    server {

        
     
       listen       8099;
       server_name  localhost;
       
         if ($time_iso8601 ~ '(\d{4}-\d{2}-\d{2})') {
               set $tttt $1;
        }
			
       access_log  logs/access-$tttt.log  main;


        #add_header Strict-Transport-Security max-age=15768000;

        

        #*******
        location /{
           proxy_pass              http://*************:8080/;    
	proxy_redirect 		http://$host http://$host:8099;   
            proxy_set_header        Host $host:8099;
            proxy_set_header        X-Real-IP $remote_addr;
	proxy_set_header        RENOTE-HOST $remote_addr;
            proxy_set_header        X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header        X-Forwarded-Proto $scheme;
	#proxy_set_header        X-Forwarded-For $remote_addr;
           #client_max_body_size    100m;
       }

        location ^~ /v1/newqueue/{     
             #proxy_cookie_path /pdfw/ /;
              proxy_pass              http://*************:8080;
              proxy_redirect          off;
              proxy_set_header        Host $host:8099;
              proxy_set_header        X-Real-IP $remote_addr;
              proxy_set_header        X-Forwarded-For $proxy_add_x_forwarded_for;
              proxy_set_header        X-Forwarded-Proto $scheme;
             client_max_body_size    100m;
       }

	location ^~ /a5report/{     
             proxy_cookie_path /a5report/ /;
             proxy_pass              http://*************:8020;    
             proxy_redirect          off;
             proxy_set_header        Host $host:$server_port;
             proxy_set_header        X-Real-IP $remote_addr;
             proxy_set_header        X-Forwarded-For $proxy_add_x_forwarded_for;
             proxy_set_header        X-Forwarded-Proto $scheme;
             client_max_body_size    100m;
       }

	# witness-agent
    location /witness-agent {
        alias /home/<USER>/shangzheng-wintess-server/client;  # 修改为实际的目录路径
        try_files $uri $uri/ /witness-agent/index.html;


        # 开启 gzip 压缩
        gzip on;
        gzip_min_length 1k;
        gzip_comp_level 6;
        gzip_types text/plain application/javascript application/x-javascript text/css application/xml text/javascript application/x-httpd-php image/jpeg image/gif image/png application/json;

        # 缓存配置
        expires 1d;
        add_header Cache-Control "no-cache";
    }

         # 普通 API 请求
    location ^~ /ai-server/ {
        proxy_pass  http://*************:8066/;
            proxy_redirect off;

               # 确保正确传递 SSL 信息
            proxy_set_header Host $host:$server_port;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto https;  # 强制使用 HTTPS
            proxy_set_header X-Forwarded-Port 1896;    # 指定正确的端口

			# 确保只有包含 WebSocket 升级头的请求才会被处理为 WebSocket
			if ($http_upgrade != "websocket") {
				break;
			}

            # WebSocket over SSL 配置
            proxy_http_version 1.1;
            proxy_set_header Upgrade $http_upgrade;
            # proxy_set_header Connection $connection_upgrade;

            proxy_read_timeout 300s;     # 增加超时时间
            proxy_send_timeout 300s;
            proxy_connect_timeout 750s;
            proxy_buffering off;
            client_max_body_size 100m;

            # 允许跨域
            add_header 'Access-Control-Allow-Origin' '*' always;
            add_header 'Access-Control-Allow-Methods' 'GET, POST, OPTIONS' always;
            add_header 'Access-Control-Allow-Headers' 'DNT,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Range,Authorization' always;
            add_header 'Access-Control-Expose-Headers' 'Content-Length,Content-Range' always;

            if ($request_method = 'OPTIONS') {
                add_header 'Access-Control-Max-Age' 1728000;
                add_header 'Content-Type' 'text/plain; charset=utf-8';
                add_header 'Content-Length' 0;
                return 204;
            }
       }
 

	  location ^~ /nginx_status {
       stub_status on;
       access_log   off;
       }
    }


  

}
