// uno.config.ts
import {
  defineConfig,
  presetAttributify,
  presetIcons,
  presetTypography,
  presetUno,
  presetWebFonts,
  transformerDirectives,
  transformerVariantGroup,
} from "unocss";

export default defineConfig({
  shortcuts: {
    "flex-center": "flex justify-center items-center",
    "flex-x-center": "flex justify-center",
    "flex-y-center": "flex items-center",
    "wh-full": "w-full h-full",
    "flex-x-between": "flex items-center justify-between",
    "flex-x-end": "flex items-center justify-end",
    "absolute-lt": "absolute left-0 top-0",
    "absolute-rt": "absolute right-0 top-0 ",
    "fixed-lt": "fixed left-0 top-0",
  },
  theme: {
    breakpoints: {
      xxs: "0px",
      xs: "320px",
      sm: "480px",
      md: "768px",
      lg: "1024px",
      xl: "1280px",
      xxl: "1600px",
    },
    colors: {
      primary: "var(--el-color-primary)",
      primary_dark: "var(--el-color-primary-light-5)",
    },

    fontSize: {
      xs: "3rem", // 12px = 3rem（基于 4px 的根字体）
      sm: "3.5rem", // 14px = 3.5rem
      base: "4rem", // 16px = 4rem
      lg: "4.5rem", // 18px = 4.5rem
      xl: "5rem", // 20px = 5rem
    },
    lineHeight: {
      "1": "1",
      "1.25": "1.25rem",
      "1.75": "1.75rem",
    },
  },
  presets: [
    presetUno(),
    presetAttributify(),
    presetIcons(),
    presetTypography(),
    presetWebFonts({
      fonts: {
        // ...
      },
    }),
  ],
  rules: [
    // 动态生成 flex-{number}
    [/^flex-(\d+)$/, ([, d]) => ({ flex: `${d} ${d} 0%` })],
  ],
  transformers: [transformerDirectives(), transformerVariantGroup()],
});
