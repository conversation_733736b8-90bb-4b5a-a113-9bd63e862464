{"name": "ai-demo", "version": "2.13.0", "private": true, "type": "module", "scripts": {"dev": "vite", "build": "vite build", "build:test": "vite build --mode test", "build:uat": "vite build --mode uat", "preview": "vite preview", "preview:test": "vite preview --mode test", "preview:uat": "vite preview --mode uat", "build-only": "vite build", "type-check": "vue-tsc --noEmit", "lint:eslint": "eslint  --fix --ext .ts,.js,.vue ./src ", "lint:prettier": "prettier --write \"**/*.{js,cjs,ts,json,tsx,css,less,scss,vue,html,md}\"", "lint:stylelint": "stylelint  \"**/*.{css,scss,vue}\" --fix", "lint:lint-staged": "lint-staged", "preinstall": "npx only-allow pnpm", "prepare": "husky", "deploy": "deno run --allow-run --allow-read --allow-net scripts/deploy.ts"}, "lint-staged": {"*.{js,ts}": ["eslint --fix", "prettier --write"], "*.{cjs,json}": ["prettier --write"], "*.{vue,html}": ["eslint --fix", "prettier --write", "stylelint --fix"], "*.{scss,css}": ["stylelint --fix", "prettier --write"], "*.md": ["prettier --write"]}, "dependencies": {"@element-plus/icons-vue": "^2.3.1", "@tanstack/vue-query": "^5.67.3", "@tower1229/videojs-plugin-marker": "^0.0.9", "@types/crypto-js": "^4.2.2", "@videojs/http-streaming": "^3.15.0", "@vladmandic/face-api": "^1.7.14", "@vueuse/core": "^13.0.0", "agent-queue-ts-websdk": "^1.0.3", "animate.css": "^4.1.1", "axios": "^1.8.3", "crypto-js": "^4.2.0", "element-plus": "^2.9.6", "howler": "^2.2.4", "html2canvas": "^1.4.1", "idb": "^8.0.3", "lodash-es": "^4.17.21", "lottie-web": "^5.12.2", "mitt": "^3.0.1", "modern-screenshot": "^4.6.0", "nprogress": "^0.2.0", "path-browserify": "^1.0.1", "path-to-regexp": "^8.2.0", "pinia": "^3.0.1", "qs": "^6.14.0", "socket.io-client": "^4.8.1", "trtc-sdk-v5": "^5.8.3", "videojs-contrib-hls": "^5.15.0", "videojs-markers-plugin": "^1.0.2", "vue": "^3.5.13", "vue-i18n": "9.14.3", "vue-router": "^4.5.0", "vue3-clipboard": "^1.0.0", "witness-agent-sdk": "^1.0.7"}, "devDependencies": {"@eslint/js": "^9.22.0", "@iconify-json/ep": "^1.2.2", "@stomp/stompjs": "^7.0.1", "@types/lodash-es": "^4.17.12", "@types/node": "^20.17.24", "@types/nprogress": "^0.2.3", "@types/path-browserify": "^1.0.3", "@types/qs": "^6.9.18", "@types/sockjs-client": "^1.5.4", "@typescript-eslint/eslint-plugin": "^8.26.1", "@typescript-eslint/parser": "^8.26.1", "@vitejs/plugin-vue": "^5.2.1", "@vitejs/plugin-vue-jsx": "^4.1.1", "autoprefixer": "^10.4.21", "eslint": "^9.22.0", "eslint-config-prettier": "^10.1.1", "eslint-plugin-prettier": "^5.2.3", "eslint-plugin-vue": "^10.0.0", "fast-glob": "^3.3.3", "globals": "^16.0.0", "husky": "^9.1.7", "lint-staged": "^15.5.0", "postcss": "^8.5.3", "postcss-html": "^1.8.0", "postcss-scss": "^4.0.9", "prettier": "^3.5.3", "sass": "^1.85.1", "sockjs-client": "^1.6.1", "stylelint": "^16.15.0", "stylelint-config-html": "^1.1.0", "stylelint-config-recess-order": "^6.0.0", "stylelint-config-recommended": "^15.0.0", "stylelint-config-recommended-scss": "^14.1.0", "stylelint-config-recommended-vue": "^1.6.0", "stylelint-config-standard": "^37.0.0", "stylelint-prettier": "^5.0.3", "stylus": "^0.55.0", "stylus-loader": "^6.1.0", "terser": "^5.39.0", "typescript": "^5.8.2", "typescript-eslint": "^8.26.1", "unocss": "^0.58.9", "unplugin-auto-import": "^19.1.1", "unplugin-icons": "^22.1.0", "unplugin-vue-components": "^28.4.1", "vconsole": "^3.15.1", "video.js": "^8.22.0", "vite": "^6.2.1", "vite-plugin-mock-dev-server": "^1.8.4", "vite-plugin-svg-icons": "^2.0.1", "vite-plugin-vue-devtools": "^7.7.2", "vue-eslint-parser": "^10.1.1", "vue-tsc": "^2.2.8"}, "repository": "https://gitee.com/youlaiorg/vue3-element-admin.git", "author": "有来开源组织", "license": "MIT", "engines": {"node": ">=18.0.0"}}