# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## 项目概述

这是一个基于 Vue 3 + Vite + TypeScript 的远程见证系统前端项目，主要用于金融行业的远程客户身份验证和业务办理。项目集成了视频通话、人脸识别、语音识别、实时通信等功能。

## 核心技术栈

- **前端框架**: Vue 3 + TypeScript
- **构建工具**: Vite 6
- **UI 组件库**: Element Plus
- **状态管理**: Pinia
- **路由**: Vue Router 4
- **样式**: UnoCSS (原子化CSS，root font-size设置为4px，ml-4代表4px)
- **数据请求**: Axios + TanStack Vue Query
- **实时通信**: Socket.io-client
- **视频通话**: TRTC SDK v5
- **人脸识别**: @vladmandic/face-api
- **语音识别**: 集成ASR和TTS功能

## 常用开发命令

### 开发环境
```bash
# 安装依赖
pnpm install

# 启动开发服务器
pnpm dev

# 构建项目
pnpm build

# 构建测试环境
pnpm build:test

# 构建UAT环境
pnpm build:uat

# 预览构建结果
pnpm preview
pnpm preview:test
pnpm preview:uat
```

### 代码质量检查
```bash
# TypeScript 类型检查
pnpm type-check

# ESLint 检查并修复
pnpm lint:eslint

# Prettier 格式化
pnpm lint:prettier

# Stylelint 检查并修复
pnpm lint:stylelint

# Lint-staged 检查
pnpm lint:lint-staged
```

### 其他命令
```bash
# 提交代码（交互式）
pnpm commit

# 部署
pnpm deploy
```

## 项目架构

### 核心业务模块

#### 1. 远程见证系统 (`src/views/remote-witness/`)
- **座席端**: `user.vue` - 代理座席操作界面
- **客户端**: `customer.vue` - 客户操作界面
- **等待页面**: `wait.vue`, `wait-self.vue` - 等待连接页面
- **核心组件**:
  - `AgentApp/` - 座席应用主组件
  - `CustomerInfo.vue` - 客户信息管理
  - `AuditControl.vue` - 审核控制
  - `BusinessSummary.vue` - 业务摘要

#### 2. 平板见证系统 (`src/views/pad-witness/`)
- `pwait.vue` - 等待页面
- `connecting.vue` - 连接页面
- `client.vue` - 客户端
- `aisl.vue` - AI坐席页面

#### 3. 见证配置管理 (`src/views/witness-config/`)
- `reveal-info/` - 揭示信息配置
- `staff-manage.vue` - 员工管理
- `witness-script/` - 话术配置
- `seat-rules/` - 座席规则
- `skill-group/` - 技能组

### 状态管理

#### 主要 Store 模块
- **`remoteWitness.ts`** - 远程见证核心状态管理
  - 视频通话状态 (TRTC相关)
  - 设备管理 (摄像头、麦克风、扬声器)
  - 人脸检测结果
  - 客户业务信息
  - 话术管理
  - 影像助审数据

- **`app.ts`** - 应用全局状态
- **`user.ts`** - 用户认证状态
- **`settings.ts`** - 系统设置

### API 接口 (`src/api/`)

- **`remote-witness.ts`** - 远程见证相关API
  - 业务数据获取
  - 身份验证
  - 影像助审
  - 话术配置

- **`client.ts`** - 客户端API
- **`agent.ts`** - 座席端API
- **`witness-ai.ts`** - AI相关API

### 组件架构

#### 通用组件 (`src/components/`)
- `CommonForm/` - 通用表单组件
- `CommonQueryTable/` - 通用查询表格
- `remoteWitness/` - 远程见证专用组件
  - `DeviceDetection.vue` - 设备检测
  - `RealtimeDetection.vue` - 实时检测
  - `VideoPreview.vue` - 视频预览

#### 业务组件 (`src/views/*/components/`)
- 各业务模块的专用组件

### 工具函数 (`src/utils/`)

- **`request.ts`** - Axios 请求封装
- **`websocket.ts`** - WebSocket 封装
- **`aes/`** - AES 加密工具
- **`asr/`** - 语音识别相关
- **`ttsHelper.ts`** - TTS 语音合成助手

### 核心功能特性

#### 1. 视频通话系统
- 基于 TRTC SDK v5
- 支持多设备管理 (摄像头、麦克风、扬声器)
- 实时音视频通信
- 屏幕共享功能

#### 2. 人脸识别系统
- 集成 @vladmandic/face-api
- 实时人脸检测
- 活体检测
- 身份证照片比对
- 公安核查接口

#### 3. 语音系统
- ASR 语音识别
- TTS 语音合成
- 话术管理
- 实时字幕

#### 4. 业务流程
- 多业务类型支持
- 影像助审流程
- 电子签名
- 业务摘要生成

## 开发规范

### 代码风格
- 使用 TypeScript 严格模式
- 遵循 Vue 3 Composition API
- 使用 UnoCSS 原子化样式
- 字符串使用双引号
- 组件使用 PascalCase 命名

### 文件结构
- 单个文件不超过 500 行
- 组件保持单一职责
- 使用 setup 语法糖
- 类型定义放在 `src/types/` 目录

### 状态管理
- 使用 Pinia 进行状态管理
- 复杂状态使用深拷贝避免引用问题
- 本地存储用于设备设置持久化

### API 设计
- 统一的错误处理
- TypeScript 类型安全
- 支持多环境配置

## 构建配置

### 环境配置
- 支持 development/test/uat 环境
- 环境变量通过 `.env` 文件配置
- 代理配置支持后端接口转发

### 构建优化
- 代码分割和懒加载
- 预加载关键依赖
- 资源文件分类打包
- Terser 压缩优化

## 部署说明

### 静态资源部署
- 构建输出到 `dist/` 目录
- 支持相对路径部署 (base: "/witness-agent/")
- 包含完整的 nginx 配置示例

### 环境区分
- 不同环境使用不同的构建配置
- 支持环境特定的 API 地址配置
- 测试和UAT环境独立的预览命令

## 注意事项

### 设备兼容性
- 需要 Node.js >= 18.0.0
- 推荐使用现代浏览器
- 需要支持 WebRTC 和 WebSocket

### 安全考虑
- 敏感信息加密存储
- API 接口身份验证
- 文件上传安全检查

### 性能优化
- 大文件懒加载
- 组件按需导入
- 图片资源优化
- 缓存策略配置


# 沟通规范

- 所有内容必须使用 **中文** 交流 (包括代码注释)。
- 遇到不清楚的内容应立即向用户提问。
- 表达清晰、简洁、技术准确。
- 在代码中应添加必要的注释解释关键逻辑。
