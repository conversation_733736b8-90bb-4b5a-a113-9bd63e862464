/* css reset */
html, body, object, iframe, h1, h2, h3, h4, h5, h6,
p, a, img, small, strong, ol, ul, li, form, label,
table, aside, canvas, figure, footer, header, section,
audio, video {
  margin: 0;
  padding: 0;
  border: 0;
  vertical-align: baseline;
}
html, body {
  height: 100vh;
  overflow: hidden;
  line-height: 1;
  color: #222;
  font-size: 14px;
}
ol, ul {
  list-style: none;
}

#unInitTips {
  position: absolute;
  left: 50%;
  top: 50%;
  width: 200px;
  margin-left: -100px;
  font-size: 18px;
  color: #bbb;
}

.iframe-for-ie-position {
  position: absolute;
  z-index: -1;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  background-color: transparent;
  border: none;
  overflow: hidden;
}

*::-webkit-scrollbar {
  width: 6px;
  height: 0;
  background-color: #efefef;
  border-radius: 3px;
}
*::-webkit-scrollbar-thumb {
  background-color: #fff;
  border: 1px solid #efefef;
  border-radius: 3px;
}
* {
  scrollbar-color: #fff #efefef;
  scrollbar-width: thin;
}
