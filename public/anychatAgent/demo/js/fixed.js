// 以下为悬浮效果Js

(function() {
  // 记录悬浮窗口是否开启的状态
  window.isFixedVideoWindow = false;
  // 记录悬浮窗口是否隐藏的状态
  window.isFixedVideoWindowHide = false;
  var isDraging = false;

  function addClass(element, name) {
    element.className += " " + name;
  }

  function removeClass(element, name) {
    element.className = element.className.replace(
      new RegExp("(\\s|^)" + name + "(\\s|$)"),
      ""
    );
  }

  window.addClass = addClass;
  window.removeClass = removeClass;

  // 添加开关按钮的处理事件
  document
    .querySelector("#changeVideoWindow")
    .addEventListener("click", function() {
      var anychatAgent = document.querySelector(".anychat-agent");
      var anychatAgentIframe = anychatAgent.querySelector("iframe");

      if (window.isFixedVideoWindow) {
        removeClass(anychatAgent, "is-fixed-hide");
        removeClass(anychatAgent, "is-fixed");
        anychatAgentIframe.style.width = "";
        anychatAgentIframe.style.height = "";
        anychatAgent.style.top = "1%";
        anychatAgent.style.left = "";
        anychatAgent.style.right = "";
        anychatAgent.style.bottom = "";
        this.innerHTML = '<img src="./img/icon_popup.png">开启悬浮';
      } else {
        addClass(anychatAgent, "is-fixed");
        var windowInnerHeight = window.innerHeight;
        if (
          windowInnerHeight &&
          windowInnerHeight-70 < anychatAgentIframe.height
        ) {
          anychatAgentIframe.style.height = windowInnerHeight-70 + "px";
        } else{
          anychatAgentIframe.style.height = anychatAgentIframe.height + "px";
        }
        anychatAgentIframe.style.width = anychatAgentIframe.width + "px";
        this.innerHTML = '<img src="./img/icon_popup.png">退出悬浮';
      }

      window.isFixedVideoWindow = !window.isFixedVideoWindow;
    });

  // 缩小悬浮窗按钮处理事件
  document.querySelector(".hide-bar-arrow-close").addEventListener(
    "click",
    function(event) {
      event.stopPropagation();

      var anychatAgent = document.querySelector(".anychat-agent");
      addClass(anychatAgent, "is-fixed-hide");

      window.isFixedVideoWindowHide = true;
    },
    false
  );

  // 放大悬浮窗按钮处理事件
  document
    .querySelector(".fixed-hide-bar")
    .addEventListener("click", function() {
      if (isDraging) return;

      var anychatAgent = document.querySelector(".anychat-agent");
      var anychatAgentIframe = anychatAgent.querySelector("iframe");
      var boundingWidth = document.body.clientWidth - anychatAgentIframe.width;
      var boundingHeight =
        document.body.clientHeight - (+anychatAgentIframe.height + 50);

      if (targetElEndBottom >= boundingHeight) {
        anychatAgent.style.bottom = boundingHeight + "px";
      }
      if (targetElEndRight >= boundingWidth) {
        anychatAgent.style.right = boundingWidth + "px";
      }
      removeClass(anychatAgent, "is-fixed-hide");

      window.isFixedVideoWindowHide = false;
    });

  var targetEl,
    targetElStyle,
    targetElStyleBottom,
    targetElStyleRight,
    targetElStartPosX,
    targetElStartPosY,
    boundingWidth,
    boundingHeight,
    targetElEndBottom,
    targetElEndRight;

  document.querySelector(
    ".fixed-title-bar"
  ).onmousedown = document.querySelector(
    ".fixed-hide-bar"
  ).onmousedown = function(event) {
    targetEl = document.querySelector(".anychat-agent");
    targetElStyle = window.getComputedStyle(targetEl, null);
    targetElStyleBottom = +targetElStyle.bottom.replace(/\px/g, "");
    targetElStyleRight = +targetElStyle.right.replace(/\px/g, "");

    // 获取鼠标按下位置
    targetElStartPosX = event.clientX;
    targetElStartPosY = event.clientY;
    // 元素可移动宽高
    boundingWidth = document.body.clientWidth - targetEl.clientWidth;
    boundingHeight = document.body.clientHeight - targetEl.clientHeight;
    document.querySelector(".anychat-agent-iframe").style.pointerEvents =
      "none";
    document.querySelector(".working-page").style.pointerEvents = "none";
    document.querySelector(".serve-page").style.pointerEvents = "none";

    document.onmousemove = function(event) {
      if (
        Math.abs(event.clientX - targetElStartPosX) >= 3 ||
        Math.abs(event.clientY - targetElStartPosY) >= 3
      ) {
        isDraging = true;
      }
      // 通过事件委托，计算移动的距离
      var r = targetElStyleRight - (event.clientX - targetElStartPosX);
      var b = targetElStyleBottom - (event.clientY - targetElStartPosY);
      targetElEndRight = r <= 0 ? 0 : r >= boundingWidth ? boundingWidth : r;
      targetElEndBottom = b <= 0 ? 0 : b >= boundingHeight ? boundingHeight : b;
      // 移动当前元素
      targetEl.style.right = targetElEndRight + "px";
      targetEl.style.bottom = targetElEndBottom + "px";
      targetEl.style.left = "auto";
      // targetEl.style.top = 'auto';
    };

    document.onmouseup = function() {
      document.querySelector(".anychat-agent-iframe").style.pointerEvents =
        "initial";
      document.querySelector(".working-page").style.pointerEvents = "initial";
      document.querySelector(".serve-page").style.pointerEvents = "initial";
      document.onmousemove = null;
      document.onmouseup = null;
      setTimeout(function() {
        isDraging = false;
      });
    };
  };
})();
