<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>AnyChat坐席组件登录页</title>
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
    <link rel="stylesheet" href="./css/iview.css">
    <style>
        html,body {
            position: relative;
            width: 100%;
            height: 100%;
            padding: 0;
            margin: 0;
        }
        div{
            box-sizing: border-box;
        }
        #app {
            width: 100%;
            height: 100%;
        }
        .header {
            position: absolute;
            top: 0;
            left: 0;
            z-index: 2;
            box-sizing: border-box;
            width: 100%;
            padding: 12px 36px;
            display: flex;
            align-items: center;
        }
        .header img {
            display: block;
            margin-right: 16px;
            width: 48px;
            height: 48px;
        }
        .header .title {
            font-size: 20px;
            font-weight: bold;
            color: #333;
        }
        .warpper {
            flex: 1;
            position: relative;
            height: 100%;
            background: url('./img/login-bgimg.jpg') center center no-repeat;
            background-size: cover;
        }
        .illustration {
            position: absolute;
            left: 5%;
            top: 50%;
            transform: translateY(-55%);
            width: 48vw;
            height: 48vw;
            background: url('./img/illustration.png') center center no-repeat;
            background-size: contain;
        }
        .login-box {
            position: absolute;
            right: 16%;
            top: 50%;
            transform: translateY(-55%);
            width: 23vw;
            border-radius: 16px;
            padding: 3.125%;
            padding-bottom: 1%;
            text-align: center;
            font-size: 16px;
            background-color: #fff;
            box-shadow: 0 8px 16px rgba(35, 36, 63, 0.16);
            color: #333;
            letter-spacing: 1px;
        }
        .login-box > div {
            width: 100%;
        }
        .login-box .title {
            font-size: 22px;
            line-height: 1.5;
            font-weight: bold;
        }
        input {
            width: 100%;
            height: 48px;
            line-height: 48px;
            border: 1px solid transparent;
            outline: none;
            background: #f6f6f6;
            -webkit-border-radius: 4px;
            -moz-border-radius: 4px;
            border-radius: 4px;
            padding: 0 15px;
            font-size: 16px;
        }
        input.isError {
            border-color: #F5222D;
        }
        .isError-tips {
            color: #F5222D;
            text-align: left;
            font-size: 14px;
        }
        .mgb-24 {
            margin-bottom: 24px;
        }
        .mgb-50 {
            margin-bottom: 48px;
        }
        .tips span {
            color: #c71313;
            font-weight: bold;
            cursor: pointer;
        }
        .settings {
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 32px auto 16px;
            width: 60px!important;
            height: 36px;
            background-color: #f1f1f1;
            border-radius: 36px;
            vertical-align: middle;
            color: #777;
            cursor: pointer;
        }
        .settings:hover {
            background-color: #e0e0e0;
            color: #555;
        }
        .primary-btn {
            cursor: pointer;
            color: #fff;
            background-color: #c71313;
            border: none;
            outline: none;
        }
        .cancel-btn{
            color: #c71313;
            background-color: #fff;
            border: 1px solid #ccc;
        }
        .login-box .primary-btn{
            width: 100%;
            height: 48px;
            line-height: 48px;
            -webkit-border-radius: 24px;
            -moz-border-radius: 24px;
            border-radius: 24px;
            letter-spacing: 2px;
        }
        .login-box .primary-btn:hover,
        .login-box .primary-btn:active {
            background-color: #df3030;
        }
        
        .qrcode-popup {
            position: absolute;
            left: 0;
            top: 0;
            padding: 32px;
            width: 100%;
            height: 100%;
            background-color: #fff;
            border-radius: 16px;
        }
        .qrcode-popup > span {
            position: absolute;
            left: 32px;
            top: 32px;
            font-size: 14px;
            color: #666;
            cursor: pointer;
        }
        .qrcode-popup h3 {
            margin: 28px 0;
            font-size: 22px;
        }
        .vertical-center-modal .ivu-modal{
            text-align: center;
            top: 30vh;
        }
        .vertical-center-modal .ivu-modal-body{
            font-size: 18px;
        }
        .modal-title h2{
            height: 28px;
            line-height: 28px;
            font-size: 20px;
        }
        .modal-body{
            padding: 40px 20px;
        }
        .modal-body p{
            font-size: 14px;
            text-align: left;
        }
        .modal-footer .primary-btn{
            margin: 0 20px;
            width: 120px;
            height: 40px;
            line-height: 40px;
            -webkit-border-radius: 5px;
            -moz-border-radius: 5px;
            border-radius: 5px;
        }
        .demo-spin-icon-load{
            animation: ani-demo-spin 1s linear infinite;
        }
        @media screen and (max-width: 1280px) {
            .login-box{
                transform: translateY(-50%);
                width: 30vw;
            }
        }
    </style>
</head>
<body>
    <div id="app">
        <spin fix v-if="spinShow">
            <icon type="ios-loading" size=50 class="demo-spin-icon-load"></icon>
        </spin>
        <div class="header">
            <img src="./img/logo-login.png" alt="">
            <span class="title">AnyChat坐席组件 - Web坐席端</span>
        </div>
        <div class="warpper">
            <div class="illustration"></div>
            <div class="login-box">
                <div class="title mgb-50">欢迎使用</div>
                <div class="mgb-50">
                    <input type="text" ref="input" v-model.num="expCode"
                           @input="expCode = $event.target.value.replace(/\D/g,'')"
                           placeholder="请输入体验码"
                           maxlength="6"
                           :class="{ 'isError': isError }"
                           @keyup.enter="getConfigInfo">
                    <p class="isError-tips" v-if="isError">{{ tipText }}</p>
                </div>
                <div class="mgb-24">
                    <button type="button" class="primary-btn" @click="getConfigInfo">立即体验</button>
                </div>
                <div class="tips">
                    没有体验码？<span @click="modalVisible2 = true">立即咨询</span>
                </div>
                <div class="settings" title="前往前置页面">
                    <icon type="md-settings" size="18" style="cursor:pointer;" @click="forwardTo"></icon>
                </div>

                <div v-if="modalVisible2"
                    v-bloak
                    class="qrcode-popup"
                    width="360">
                    <span @click="modalVisible2 = false">&lt; 返回登录</span>
                    <h3>立即咨询</h3>
                    <div class="qrcode-body">
                        <img src="./img/br_code_ico_consult.png" alt="" width="180" height="180">
                        <p style="margin-top: 20px;">点击保存>打开微信>点击扫一扫>选择相册图片，添加微信后，联系我们开发人员。</p>
                    </div>
                </div>
            </div>
        </div>
        <modal v-model="modalVisible1"
            class-name="vertical-center-modal"
            :footer-hide="true"
               ref="modal1"
               style="display: none"
            width="600">
            <div class="modal-title">
                <h2><icon type="ios-information-circle-outline" size="28" color="#c71313"></icon>温馨提示</h2>
            </div>
            <div class="modal-body">
                <span>{{tipText}}</span>
            </div>
            <div class="modal-footer">
                <button type="button" class="primary-btn" @click="showModal2">立即咨询</button>
                <button type="button" class="primary-btn cancel-btn" @click="modalVisible1=false">取消</button>
            </div>
        </modal>
    </div>
</body>
<script src="./login.config.js"></script>
<!-- import polyfill -->
<script src="./js/polyfill.js"></script>
<!-- import Vue.js -->
<script src="./js/vue.min.js"></script>
<!-- import iView -->
<script src="./js/iview.min.js"></script>
<!-- import loadash -->
<script src="./js/lodash.min.js"></script>
<script src="./js/jquery-1.12.4.js"></script>
<script>
    var vm = new Vue({
      el:'#app',
      data:function(){
        return{
          tipText:'',
          expCode:'',//455449
          modalVisible1:false,
          modalVisible2:false,
          spinShow:false,
          isError: false
        }
      },
      mounted:function(){
        this.expCode = window.localStorage.getItem('expCode');
        this.$refs.input.focus();
      },
      methods:{
        forwardTo:function(){
          window.location.href = './preset.html';
        },
        getConfigInfo:function(){
          if(!this.expCode){
            this.tipText ='请输入体验码！';
            // this.modalVisible1 = true;
            this.isError = true;
            return
          }
          window.localStorage.setItem('expCode',this.expCode);
          // this.forwardTo({
          //   "queueId":"1",
          //   "businessCode":"001",
          //   "areaId":"1",
          //   "appId":"375F9A94-92A3-F845-8BC2-4A69F9F17D1B",
          //   "serverIp":"dev.bairuitech.cn",
          //   "serverPort":"19000",
          //   "serverAuthPass":""
          // });
          var that = this;
          this.spinShow = true;
          $.ajax({
            type: "POST",
            // url:'/api-test/product-center/externalApi/productConfig',
            // url:'/user/product-center/externalApi/productConfig',
            // url:'http://dev.bairuitech.cn:14609/api-test/product-center/externalApi/productConfig',
            // url:'http://product.bairuitech.cn:14605/user/product-center/externalApi/productConfig',
            url: window.loginApiUrl,
            data:JSON.stringify({
              productCode:"YYT-1210-0000",
              experienceCode:that.expCode,
              requestRoute:"web"
            }),
            dataType:'json',
            contentType:'application/json',
            success: function (res) {
              if(res.errorCode == 0){
                // var presetData = {
                //   config: res.content,
                //   agentInfo:{
                //     nickName: "体验客户",
                //     strUserId: "test"
                //   }
                // };
                // localStorage.setItem('AnyChatAgentPresetData', JSON.stringify(presetData));

                var result = res.content;

                setTimeout(function (){
                  // that.expCode = '';
                  window.location.href =
                    './preset.html?serverIp='+result.serverIp+'&&serverPort='+result.serverPort+'&&appId='+result.appId+'&&areaId='+result.areaId+'&&origin=login';
                },50)
              }else{
                // that.$refs.modal1.$el.style.display = '';
                that.tipText = res.msg;
                // that.modalVisible1 = true;
                that.isError = true;
              }
            },
            error: function () {
              that.tipText ='系统异常，请联系客服！';
            //   that.modalVisible1 = true;
                that.isError = true;
            },
            complete: function () {
              that.spinShow = false;
            }
          })
        },
        showModal2:function(){
          this.$refs.modal1.$el.style.display = 'none';
          this.$refs.modal2.$el.style.display = '';
          this.modalVisible1 = false;
          this.modalVisible2 = true
        }
      }
    })
</script>
</html>
