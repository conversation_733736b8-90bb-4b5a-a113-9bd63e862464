
/* 以下为悬浮效果相关样式 */
.anychat-agent.is-fixed {
  position: fixed;
  z-index: 99;
  bottom: 20px;
  right: 20px;
  margin-right: 0;
  width: auto;
  height: auto;
  box-shadow: 0 2px 6px rgba(0,0,0,.1);
  border-radius: 6px;
  background-color: #fff;
  overflow: hidden;
}
.anychat-agent.is-fixed .fixed-title-bar {
  display: flex;
}
.anychat-agent.is-fixed.is-fixed-hide {
  width: 240px;
  height: 50px;
}
.anychat-agent.is-fixed.is-fixed-hide .anychat-agent-iframe,
.anychat-agent.is-fixed.is-fixed-hide .fixed-title-bar {
  visibility: hidden;
}
.anychat-agent.is-fixed.is-fixed-hide .fixed-hide-bar {
  display: flex;
}
.fixed-title-bar {
  display: none;
  align-items: center;
  justify-content: space-between;
  box-sizing: border-box;
  padding: 10px 20px;
  height: 50px;
  background-color: #fff;
  border-bottom: 1px solid #f0f0f0;
  font-size: 16px;
  font-weight: bold;
  cursor: move;
}
.fixed-title-bar .hide-bar-arrow-close {
  position: relative;
  width: 28px;
  height: 28px;
  cursor: pointer;
}

.fixed-title-bar .hide-bar-arrow-close::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 60%;
  height: 4px;
  background-color: #2a6dd3;
  border-radius: 4px;
  transform: translate(-50%, -50%);
}

.fixed-hide-bar {
  position: relative;
  display: none;
  align-items: center;
  width: 240px;
  height: 50px;
  background-color: #2a6dd3;
  color: #fff;
  cursor: pointer;
}

.hide-bar-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 50px;
  height: 50px;
  background: #1358c1;
  color: #fff;
}

.hide-bar-icon svg.svg-icon-path-icon {
  width: 24px;
  height: 24px;
  vertical-align: top;
}
.hide-bar-icon svg.svg-icon-path-icon.fill [fill] {
  fill: #fff;
  stroke: none;
}

.hide-bar-text {
  flex: 1;
  font-size: 13px;
  text-align: center;
}

.hide-bar-arrow-open {
  position: relative;
  width: 40px;
  height: 40px;
}

.hide-bar-arrow-open::before,
.hide-bar-arrow-open::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 12px;
  height: 2px;
  background-color: #fff;
}
.hide-bar-arrow-open::before { transform: translate(-80%, -50%) rotate(-45deg); }
.hide-bar-arrow-open::after { transform: translate(-20%, -50%) rotate(45deg); }

.fixed-hide-bar.has-call .hide-bar-icon {
  box-shadow: 0 0 8px #B50005;
  animation: lingBg 1.2s linear infinite;
}

.fixed-hide-bar.has-call .hide-bar-icon svg {
  animation: lingling 1.2s linear infinite;
}

@keyframes lingBg {
  0% { background-color: #B50005; }
  50% { background-color: #ff4f54; }
  100% { background-color: #B50005; }
}

@keyframes lingling {
  0% { transform: rotate(0); }
  5% { transform: rotate(15deg); }
  10% { transform: rotate(0); }
  15% { transform: rotate(-15deg); }
  20% { transform: rotate(0); }
  25% { transform: rotate(15deg); }
  30% { transform: rotate(0); }
  35% { transform: rotate(-15deg); }
  40% { transform: rotate(0); }
  45% { transform: rotate(15deg); }
  50% { transform: rotate(0); }
  55% { transform: rotate(-10deg); }
  60% { transform: rotate(0); }
  100% { transform: rotate(0); }
}
