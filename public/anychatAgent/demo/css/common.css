* {
  margin: 0;
  padding: 0;
}

html, body {
  height: 100vh;
  overflow: hidden;
  user-select: none;
}

.divider-horizon {
  width: 100%;
  height: 8px;
  background-color: #f2f2f2;
}

.divider-vertical {
  width: 8px;
  height: 100%;
  background-color: #f2f2f2;
}

.wapper {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.header {
  position: relative;
  z-index: 2;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px 15px;
  box-sizing: border-box;
  width: 100%;
  background-color: #fff;
  box-shadow: 0 0 16px rgba(0, 0, 0, .1);
}

.logo {
  height: 30px;
}

.header .left {
  display: flex;
  align-items: center;
}

.btn-submit, .btn-cancel, .btn-common, .btn-common-black, .btn-outbound-calling {
  display: none;
  margin-right: 8px;
  width: 100px;
  height: 36px;
  background-color: #fff;
  border: 0;
  border-radius: 8px;
  cursor: pointer;
  vertical-align: middle;
}

.btn-submit, .btn-cancel {
  width: 120px;
  height: 40px;
  line-height: 40px;
  display: block;
  margin: 0 20px;
}

.btn-cancel:hover {
  background-color: #eee;
}

.btn-cancel {
  border: 1px solid #ccc;
  color: #666;
}

.btn-submit {
  background-color: #db1818;
  color: #fff;
  border: 1px solid #db1818;
}

.btn-submit:hover {
  background-color: #f13636;
  border-color: #f13636;
}

.btn-common {
  display: inline-flex;
  align-items: center;
  padding: 0 8px;
  /* width: 130px; */
  border: 1px solid #ff9e9e;
  color: #db1818;
  outline: none;
}

.btn-common img {
  margin-right: 4px;
  width: 24px;
  height: 24px;
  display: inline-block;
}

.btn-common:hover {
  background-color: #fff5f5;
}

.btn-common-black {
  display: inline-block;
  border: 1px solid #ccc;
  color: #000;
  outline: none;
  line-height: 24px;
}

.btn-common-black:hover {
  background-color: #eee;
}

.admin, .settings {
  display: inline-block;
  vertical-align: middle;
  margin-right: 8px;
  font-size: 14px;
  color: #333;
}

.admin img, .settings img {
  width: 24px;
  height: 24px;
  vertical-align: middle;
  margin-right: 10px;
}

.settings img {
  margin-right: 6px;
}

.menu {
  background-color: transparent;
  padding: 8px;
  border: 0;
  cursor: pointer;
  border-radius: 8px;
  height: 40px;
  vertical-align: middle;
}

.menu:hover {
  background-color: #eee;
}

.menu img {
  width: 24px;
  height: 24px;
}

.main {
  position: relative;
  display: flex;
  flex-direction: column;
  flex: 1;
}

.anychat-agent-iframe, .demo-iframe {
  display: block;
  width: 100%;
  height: 100%;
  background-color: transparent;
  border: none;
  overflow: hidden;
}
/* #agentApp{
  position: fixed;
  top: 1px;
  right: 100px;
} */
.layout-agent-wrap {
  flex: 1;
  height: 100%;
  border-radius: 4px;
  overflow: hidden;
  background-color: #fff;
  transition: all .16s;
  display: flex;
  flex-direction: column;
}

.anychat-agent {
  width: 100%;
  height: 100%;
  flex: 1;
  transition: all .16s;
}

.serve-page {
  width: 0;
  height: 100%;
  transition: all .16s;
}

.working-page {
  height: 100%;
  width: 0;
  transition: all .16s;
}

.business-content {
  display: flex;
  flex: 1;
  max-height: 100%;
  background-color: #f1f2f5;
}

.business-buttons {
  width: 100%;
  max-height: 0;
  flex: 1 0 auto;
  transition: all .16s;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
  background-color: #fff;
}

.btn-outbound-calling {
  display: inline-block;
  width: 60px;
  border: 1px solid #ccc;
  color: #000;
  outline: none;
  line-height: 24px;
}

.btn-outbound-calling:hover {
  background-color: #eee;
}

.drawer-mask {
  position: fixed;
  top: 64px;
  right: 0;
  width: 100vw;
  height: calc(100vh - 64px);
  background-color: rgba(0, 0, 0, .2);
  display: none;
}

.drawer-mask-show {
  display: block;
}

.drawer {
  position: fixed;
  top: 64px;
  right: 0;
  width: 100vw;
  height: calc(100vh - 64px);
  display: flex;
  transition: all .3s;
  transform: translateX(100%);
}

.drawer-show {
  transform: translateX(0);
}

.drawer-wrap {
  margin-left: auto;
  width: 30vw;
  height: 100%;
  display: flex;
  flex-direction: column;
  background-color: #fff;
  box-shadow: 0 0 16px rgba(0, 0, 0, .1);
}

.drawer-header {
  padding: 10px 15px;
  font-size: 18px;
  font-weight: 600;
  background-color: #f7f7f7;
}

.drawer-content {
  padding: 20px;
}

.drawer-footer {
  margin-top: auto;
  padding: 20px;
  text-align: right;
}

.c-form-item {
  display: flex;
  margin-bottom: 15px;
}

.c-form-item_label {
  margin-right: 10px;
  padding-top: 9px;
  width: 5em;
  font-size: 14px;
  color: #666;
}

.c-form-item_content {
  flex: 1;
}

.c-form-error {
  margin-top: 5px;
  display: none;
  font-size: 12px;
  color: #db1818;
}

.c-form-item_error .c-form-error {
  display: block;
}

.c-form-item_error .c-form-item_label {
  color: #db1818;
}

.c-form-item_error .c-input {
  border-color: #db1818;
}

.c-form-item_error .c-input:focus {
  border-color: #db1818;
}

.c-form-item_phone .c-form-item_content {
  display: flex;
  flex-direction: column;
}

.c-input {
  padding: 10px;
  border: 1px solid #ddd;
  border-radius: 4px;
  outline: none;
}

.c-input:focus {
  border-color: #333;
}

.c-button {
  padding: 10px 15px;
  background-color: #fff;
  border-radius: 4px;
  border: 1px solid #ddd;
  outline: none;
  cursor: pointer;
  color: #666;
}

.c-button:hover {
  opacity: .8;
}

.c-button-danger {
  background-color: #db1818;
  border-color: #db1818;
  color: #fff;
}

.func {
  display: flex;
  align-items: center;
}

.func-item {
  display: flex;
  align-items: center;
  cursor: pointer;
  line-height: 24px;
}

.func-item:not(:last-child) {
  margin-right: 35px;
}

.func-icon {
  display: inline-block;
  width: 16px;
  height: 16px;
  background-size: contain;
  background-repeat: no-repeat;
}

.func-icon-device {
  margin-right: 5px;
  background-image: url("../img/icon_device.png");
}

.func-icon-ready {
  display: none;
  margin-left: 5px;
  background-image: url("../img/icon_status_success.png");
}

.func-icon-setting {
  margin-right: 5px;
  background-image: url("../img/icon_settings.png");
}

.func-icon-admin {
  margin-right: 5px;
  width: 34px;
  height: 34px;
  background-image: url("../img/icon_admin.png");
}

.func-icon-logout {
  margin-right: 5px;
  background-image: url("../img/out.png");
}

.popup-wrapper {
  position: relative;

  &:hover {
    .popup-content {
      display: block;
    }
  }

  .popup-content {
    display: none;
    position: absolute;
    top: 20px;
    left: 0;
    background: white;
    padding: 10px;
    min-width: 100px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, .12), 0 0 6px rgba(0, 0, 0, .04);
    border-radius: 5px;

    &:hover {
      display: block;
    }
  }

  .popup-list-item {
    padding: 10px 0 5px 0;
    display: flex;
    align-items: center;
    justify-content: space-between;
  }

  .popup-list-item-checked {
    .popup-list-item-icon {
      visibility: visible;
    }
  }

  .popup-list-item-icon {
    visibility: hidden;
    width: 15px;
    height: 9px;
    border-left: 3px solid #C71413;
    border-bottom: 3px solid #C71413;
    transform: rotate(-45deg);
    box-sizing: border-box;
  }

  .popup-title {
    padding: 10px 0 5px 0;
    color: #C71413;
    font-weight: bold;
  }
}
