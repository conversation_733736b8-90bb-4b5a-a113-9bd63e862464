<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <title>客户信息</title>
  <style>
    * {
      margin: 0;
      padding: 0;
    }

    html, body {
      height: 100%;
    }

    body {
      padding: 0 20px;
      background-color: #fff;
    }

    .page-user-info {
      height: 100%;
    }

    .ui-icon {
      margin-right: 8px;
      width: 16px;
      height: 16px;
      display: inline-block;
    }

    .ui-icon_0 {
      background: url("./img/agentInfo/icon_0.png") no-repeat;
      background-size: contain;
    }

    .ui-icon_1 {
      background: url("./img/agentInfo/icon_1.png") no-repeat;
      background-size: contain;
    }

    .ui-box-header {
      display: flex;
      border-bottom: 1px solid #e4e7ed;
    }

    .ui-box-header_title {
      padding: 12px 0;
      position: relative;
      display: flex;
      align-items: center;
      font-size: 18px;
      color: #333;
    }

    .ui-box-header_title::after {
      content: "";
      position: absolute;
      bottom: 0;
      width: 100%;
      height: 0;
      border-bottom: 3px solid #c71313;
    }

    .ui-form {
      padding: 20px;
    }

    .ui-form-item {
      display: flex;
      align-items: center;
      font-size: 14px;
      margin-bottom: 20px;
    }

    .ui-form-item_label {
      width: 8em;
      margin-right: 8px;
      color: #606266;
      text-align: right;
    }

    .ui-form-item_value {
      color: #181818;
    }

    .ui-process {
      padding: 20px;
    }

    .ui-process-item:last-child .ui-process-item_content {
      border-left: none;
    }

    .ui-process-item_header {
      display: flex;
      align-items: center;
      color: #181818;
    }

    .ui-process-item_header::before {
      width: 8px;
      height: 8px;
      position: relative;
      left: -3px;
      margin-right: 16px;
      content: "";
      display: block;
      border: 2px solid #c71313;
      border-radius: 50%;
      box-sizing: border-box;
    }

    .ui-process-item_content {
      padding: 8px 20px 24px;
      color: #606266;
      border-left: 2px solid #d4d7de;
    }

    .ui-image-wrap {
      margin: 20px 0;
      display: flex;
      flex-wrap: wrap;
    }

    .ui-image-box {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      margin-bottom: 15px;
    }

    .ui-image {
      margin-bottom: 10px;
      margin-right: 20px;
      width: 240px;
      height: 180px;
      object-fit: contain;
    }

    .ui-image-box span {
      color: #666;
    }

    //.ui-image[url=""] {
    //  display: none;
    //}
  </style>
</head>
<body>
<div
  id="page-user-info"
  class="page-user-info">
  <div class="ui-box">
    <div class="ui-box-header">
      <div class="ui-box-header_title">
        <i class="ui-icon ui-icon_0"></i>
        <span>业务信息</span>
      </div>
    </div>
    <div class="ui-box-content">
      <div class="ui-form">
        <div class="ui-form-item">
          <label class="ui-form-item_label">业务类型：</label>
          <div class="ui-form-item_value">双向开户</div>
        </div>
        <div class="ui-form-item">
          <label class="ui-form-item_label">营业部：</label>
          <div class="ui-form-item_value">天河区营业部</div>
        </div>
        <div class="ui-form-item">
          <label class="ui-form-item_label">渠道来源：</label>
          <div class="ui-form-item_value">APP</div>
        </div>
        <div class="ui-form-item">
          <label class="ui-form-item_label">推荐人：</label>
          <div class="ui-form-item_value">无</div>
        </div>
      </div>
    </div>
  </div>

  <div class="ui-box">
    <div class="ui-box-header">
      <div class="ui-box-header_title">
        <i class="ui-icon ui-icon_1"></i>
        <span>影像资料</span>
      </div>
    </div>
    <div class="ui-box-content">
      <div class="ui-image-wrap">
        <div class="ui-image-box">
          <img
            id="frontImage"
            src="./img/agentInfo/IDcard-0.png"
            class="ui-image" alt="">
          <span>身份证人像面</span>
        </div>
        <div class="ui-image-box">
          <img
            id="backImage"
            src="./img/agentInfo/IDcard-1.png"
            class="ui-image" alt="">
          <span>身份证国徽面</span>
        </div>
        <div class="ui-image-box">
          <img
            src="./img/agentInfo/avatar-placeholder.png"
            id="headImage"
            class="ui-image" alt="">
          <span>免冠照</span>
        </div>
      </div>
    </div>
  </div>

  <div class="ui-box">
    <div class="ui-box-header">
      <div class="ui-box-header_title">
        <i class="ui-icon ui-icon_1"></i>
        <span>交易信息</span>
      </div>
    </div>
    <div class="ui-box-content">
      <div class="ui-process">
        <div class="ui-process-item">
          <div class="ui-process-item_header">
            手机银行密码重置
          </div>
          <div class="ui-process-item_content">
            <p>2023-05-22 18:00:00</p>
          </div>
        </div>
        <div class="ui-process-item">
          <div class="ui-process-item_header">
            客户信息维护
          </div>
          <div class="ui-process-item_content">
            <p>2023-05-22 18:00:00</p>
          </div>
        </div>
        <div class="ui-process-item">
          <div class="ui-process-item_header">
            账户激活
          </div>
          <div class="ui-process-item_content">
            <p>2023-05-22 18:00:00</p>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<script>
  function insertImage(base64, imgType) {
    console.log('%c log - imgType ---------------->', 'color: #67C23A', imgType)
    if (parseInt(imgType) === 1 && base64) {
      document.querySelector('#headImage').src = 'data:image/jpg;base64,' + base64
    } else {
      document.querySelector('#headImage').src = './img/agentInfo/avatar-placeholder.png'
    }
  }
  function clearImage() {
      document.querySelector('#headImage').src = './img/agentInfo/avatar-placeholder.png'
    }
</script>
</body>
</html>
