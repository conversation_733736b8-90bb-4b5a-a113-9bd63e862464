<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <title>服务信息</title>
  <link
    href="./css/iview.css"
    rel="stylesheet">
  <style>
    html, body, .page-serve-info {
      height: 100%;
    }

    body {
      background-color: #f0f2f5;
    }

    .page-serve-info {
      display: flex;
      flex-direction: column;
      padding: 30px;
      background-color: #fff;
      overflow-y: auto;
    }

    .si-box-title {
      margin-bottom: 20px;
      font-size: 20px;
    }

    .si-data {
      display: flex;
      justify-content: space-between;
      align-items: center;
      position: relative;
      padding: 30px 20px 40px 40px;
    }

    .si-data::before {
      position: absolute;
      top: 50%;
      transform: translateY(-50%);
      content: "";
      display: block;
      height: 70%;
      width: 4px;
      left: 0;
      border-radius: 8px;
    }

    .si-data-1 {
      background: url("./img/serveInfo/bg_yellow.png") no-repeat;
      background-size: 100% 100%;
    }

    .si-data-1::before {
      background-color: #ffca69;
    }

    .si-data-2 {
      background: url("./img/serveInfo/bg_blue.png") no-repeat;
      background-size: 100% 100%;
    }

    .si-data-2::before {
      background-color: #2e6cfa;
    }

    .si-data-3 {
      background: url("./img/serveInfo/bg_green.png") no-repeat;
      background-size: 100% 100%;
    }

    .si-data-3::before {
      background-color: #07d38f;
    }

    .si-data-label {
      margin-bottom: 15px;
      font-size: 18px;
      font-weight: 600;
    }

    .si-data-value {
      font-size: 36px;
      font-weight: bold;
    }

    .si-icon {
      width: 40px;
      height: 40px;
      display: block;
    }

    .si-icon_0 {
      background: url("./img/serveInfo/icon_0.png") no-repeat;
      background-size: contain;
    }

    .si-icon_1 {
      background: url("./img/serveInfo/icon_1.png") no-repeat;
      background-size: contain;
    }

    .si-icon_2 {
      background: url("./img/serveInfo/icon_2.png") no-repeat;
      background-size: contain;
    }

    .si-icon_3 {
      background: url("./img/serveInfo/icon_3.png") no-repeat;
      background-size: contain;
    }

    .si-icon_4 {
      background: url("./img/serveInfo/icon_4.png") no-repeat;
      background-size: contain;
    }

    .si-icon_5 {
      background: url("./img/serveInfo/icon_5.png") no-repeat;
      background-size: contain;
    }

    .si-icon_6 {
      background: url("./img/serveInfo/icon_6.png") no-repeat;
      background-size: contain;
    }

    .si-icon_7 {
      background: url("./img/serveInfo/icon_7.png") no-repeat;
      background-size: contain;
    }

    .si-box-info {
      display: flex;
      flex-direction: column;
    }

    .si-box-info .ivu-row {
      flex: 1;
    }

    .si-box-info .ivu-col-span-6 {
      height: 50%;
      padding-bottom: 20px;
    }

    .si-info {
      padding: 20px 0;
      height: 100%;
      display: flex;
      justify-content: center;
      align-items: center;
      flex-direction: column;
      background-color: #fdfdfd;
      border-radius: 8px;
      border: 1px solid #f1f2f5;
    }

    .si-info-value {
      font-size: 36px;
      font-weight: bold;
    }

    .si-info-label {
      margin-top: 20px;
      color: #333;
      font-size: 18px;
    }

  </style>
</head>
<body>
<div
  id="page-serve-info"
  class="page-serve-info">
  <div
    class="si-box"
    style="flex: 1 1 35%;">
    <h4 class="si-box-title">数据统计</h4>
    <row :gutter="18">
      <i-col :span="8">
        <div class="si-data si-data-1">
          <div>
            <p class="si-data-label">服务率</p>
            <p class="si-data-value">70%</p>
          </div>
          <i-circle
            :percent="70"
            size="80"
            stroke-color="#FFCA69"
            stroke-linecap="square"
            stroke-width="25"
            trail-width="25"></i-circle>
        </div>
      </i-col>
      <i-col :span="8">
        <div class="si-data si-data-2">
          <div>
            <p class="si-data-label">业务办理成功率</p>
            <p class="si-data-value">99.9%</p>
          </div>
          <i-circle
            :percent="99.9"
            size="80"
            stroke-color="#2E6CFA"
            stroke-linecap="square"
            stroke-width="25"
            trail-width="25"></i-circle>
        </div>
      </i-col>
      <i-col :span="8">
        <div class="si-data si-data-3">
          <div>
            <p class="si-data-label">近7日服务率</p>
            <p class="si-data-value">96.8%</p>
          </div>
          <i-circle
            :percent="96.8"
            size="80"
            stroke-color="#07D38F"
            stroke-linecap="square"
            stroke-width="25"
            trail-width="25"></i-circle>
        </div>
      </i-col>
    </row>
  </div>

  <div
    class="si-box si-box-info"
    style="flex: 1 1 65%;">
    <h4 class="si-box-title">服务信息</h4>
    <row :gutter="20">
      <i-col
        v-for="(item, index) in serveInfo"
        :key="item.label"
        :span="6">
        <div class="si-info">
          <i :class="'si-icon si-icon_' + index"></i>
          <p>
            <span class="si-info-value" v-text="item.value"></span>
            <span v-text="item.unit"></span>
          </p>
          <p class="si-info-label" v-text="item.label"></p>
        </div>
      </i-col>
    </row>
  </div>
</div>

<!-- import polyfill -->
<script src="./js/polyfill.js"></script>
<!-- import Vue.js -->
<script src="./js/vue.min.js"></script>
<!-- import iView -->
<script src="./js/iview.min.js"></script>
<!-- import loadash -->
<script src="./js/lodash.min.js"></script>
<script>
  new Vue({
    el: '#page-serve-info',
    data() {
      return {
        serveInfo: [
          {
            value: 320,
            unit: '秒',
            label: '平均交谈时间'
          },
          {
            value: 3,
            unit: '秒',
            label: '平均振铃时长'
          },
          {
            value: 22,
            unit: '秒',
            label: '平均事时长'
          },
          {
            value: 2240,
            unit: '秒',
            label: '服务总时长'
          },
          {
            value: 320,
            unit: '分钟',
            label: '空闲总时长'
          },
          {
            value: 7,
            unit: '笔',
            label: '业务接听量'
          },
          {
            value: 3,
            unit: '个',
            label: '呼叫丢失量'
          },
          {
            value: 66,
            unit: '个',
            label: '队列进线量'
          }
        ]
      }
    }
  })
</script>
</body>
</html>
