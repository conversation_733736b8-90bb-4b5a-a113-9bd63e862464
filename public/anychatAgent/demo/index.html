<!DOCTYPE html>
<html lang="zh">

<head>
  <meta charset="utf-8">
  <meta content="IE=11" http-equiv="X-UA-Compatible">
  <meta content="width=device-width,initial-scale=1.0" name="viewport">
  <link href="../favicon.ico" rel="icon">
  <title>坐席组件demo</title>
  <link href="./css/common.css" rel="stylesheet">
  <link href="./css/fixed.css" rel="stylesheet">
</head>

<body>
  <div class="wapper">
    <header class="header">
      <div class="left">
        <img alt="logo" class="logo" src="./img/logo.png" />
        <span style="margin-left: 20px;">
          <button id="changeVideoWindow" class="btn-common">
            <img src="./img/icon_popup.png" alt="">悬浮窗口</button>
        </span>
      </div>

      <div class="func">
        <!--<div class="settings">-->
        <!--  <button-->
        <!--    id="outbound-calling"-->
        <!--    class="btn-outbound-calling">外呼-->
        <!--  </button>-->
        <!--</div>-->
        <div class="func-item" id="queueUserInfo">
          <i class="func-icon func-icon-setting"></i>
          <span>获取排队用户信息</span>
          <i class="func-icon func-icon-ready"></i>
        </div>
        <div class="func-item popup-wrapper" id="updateTheme">
          <i class="func-icon func-icon-setting"></i>
          <span>主题</span>
          <i class="func-icon func-icon-ready"></i>
          <div class="popup-content">
            <div>
              <div class="popup-title">主题色</div>
              <div class="popup-list-item popup-list-item-checked" id="theme-1">
                <span>蓝色</span>
                <div class="popup-list-item-icon"></div>
              </div>
              <div class="popup-list-item" id="theme-2">
                <span>红色</span>
                <div class="popup-list-item-icon"></div>
              </div>
              <div class="popup-list-item" id="theme-3">
                <span>自定义</span>
                <div class="popup-list-item-icon"></div>
              </div>
            </div>
            <div>
              <div class="popup-title">按钮形状</div>
              <div class="popup-list-item popup-list-item-checked" id="btn-shape-1">
                <span>直角</span>
                <div class="popup-list-item-icon"></div>
              </div>
              <div class="popup-list-item" id="btn-shape-2">
                <span>圆角</span>
                <div class="popup-list-item-icon"></div>
              </div>
            </div>
            <div>
              <div class="popup-title">视图布局</div>
              <div class="popup-list-item popup-list-item-checked" id="view-mode-1">
                <span>标准模式</span>
                <div class="popup-list-item-icon"></div>
              </div>
              <div class="popup-list-item" id="view-mode-2">
                <span>垂直模式</span>
                <div class="popup-list-item-icon"></div>
              </div>
              <!--            <div class="popup-list-item" id="view-mode-3">-->
              <!--              <span>水平模式</span>-->
              <!--              <div class="popup-list-item-icon"></div>-->
              <!--            </div>-->
            </div>
          </div>
        </div>
        <!-- <div class="func-item" id="device-detect">
          <i class="func-icon func-icon-device"></i>
          <span>设备检测</span>
          <i class="func-icon func-icon-ready"></i>
        </div> -->
        <div class="func-item" id="goSettings">
          <i class="func-icon func-icon-setting"></i>
          <span>设置</span>
        </div>
        <div class="func-item">
          <i class="func-icon func-icon-admin"></i>
          <span>admin</span>
        </div>
        <div class="func-item" id="logout">
          <i class="func-icon func-icon-logout"></i>
        </div>
      </div>
    </header>
    <div class="divider-horizon"></div>
    <div class="main">
      <!--div
      id="workPage"
      class="working-page">
      <iframe
        class="demo-iframe"
        src="./agentInfo.html"
        title="业务办理中demo页面"></iframe>
    </div-->

      <div class="business-content">
        <div class="divider-vertical"></div>
        <div class="layout-agent-wrap">
          <div class="anychat-agent">
            <div class="fixed-hide-bar">
              <i class="hide-bar-icon">
                <svg class="svg-icon-path-icon fill" height="24" viewBox="0 0 100 63" width="24"
                  xmlns="http://www.w3.org/2000/svg">
                  <g id="图标" fill="none" fill-rule="evenodd" stroke="none" stroke-width="1">
                    <g id="播放器" fill="#000000" fill-rule="nonzero" transform="translate(-177.000000, -267. 000000)">
                      <path id="形状"
                        d="M240.25458,330 C240.25458,330 249.345524,330 249.345524,320.999972 L249.345524,276.000028 C249.345524,267 240.25458,267 240.25458,267 L186.090945,267 C186.090945,267 177,267 177,276.000028 L177,320.999972 C177,330 186.090945,330 186.090945,330 L240.25458,330 Z M254.64095,284.581528 L254.64095,312.580507 L277,330 L277,267.000097 L254.64095,284.581528 Z">
                      </path>
                    </g>
                  </g>
                </svg>
              </i>
              <span class="hide-bar-text">点击开启视频通话</span>
              <span class="hide-bar-arrow-open"></span>
            </div>

            <div class="fixed-title-bar">音视频通话
              <span class="hide-bar-arrow-close"></span>
            </div>

            <!--iframe
            id="agentApp"
            class="anychat-agent-iframe"
            height="640"
            src="../index.html"
            title="AnyChat坐席组件"
            width="480"></iframe-->
            <iframe id="agentApp" class="anychat-agent-iframe" src="../index.html" height="640" width="800"></iframe>
          </div>
        </div>
        <div class="divider-vertical"></div>
        <div id="workPage" class="working-page">
          <iframe id="agentInfoFrame" class="demo-iframe" src="./agentInfo.html" title="业务办理中demo页面"></iframe>
        </div>
        <div id="servePage" class="serve-page">
          <iframe class="demo-iframe" src="./serveInfo.html" title="服务信息demo页面"></iframe>
        </div>
        <div class="divider-vertical"></div>
      </div>
      <div class="divider-horizon"></div>
      <div id="business-buttons" class="business-buttons">
        <button id="btnSubmit" class="btn-submit">提交业务
        </button>
        <button id="btnCancel" class="btn-cancel">取消业务
        </button>
      </div>
    </div>

    <div class="drawer-mask"></div>
    <div class="drawer">
      <div class="drawer-wrap">
        <div class="drawer-header">外呼</div>
        <div class="drawer-content">
          <div id="phone-item" class="c-form-item c-form-item_phone">
            <label class="c-form-item_label" for="phone">手机号码：</label>
            <div class="c-form-item_content">
              <input id="phone" class="c-input" placeholder="请输入客户手机号码" type="text">
              <p class="c-form-error">请输入正确的手机号</p>
            </div>
          </div>
          <div class="c-form-item">
            <label class="c-form-item_label" for=""></label>
            <div class="c-form-item_content">
              <button id="video-calling" class="c-button c-button-danger c-button-calling">视频呼叫
              </button>
            </div>
          </div>
        </div>
        <!--      <div class="drawer-footer">-->
        <!--        <button class="c-button">取消</button>-->
        <!--      </div>-->
      </div>
    </div>
  </div>
  <script type="module">
    // import {ttsInfo} from "https://dev.bairuitech.cn:16016/h5/projects/shanghai/agent/predata.js";
    (function () {
      var iframe = document.getElementById('agentApp');
      var urlSearchParams = new URLSearchParams(window.location.search);
      if (urlSearchParams.get('device')) {
        iframe.src = '../index.html#/?device=1'
        iframe.contentWindow.location.reload(true);
      }
      // 模拟初始化输入数据
      var presetData = localStorage.getItem('AnyChatAgentPresetData')
      presetData = presetData && presetData !== ''
        ? JSON.parse(presetData)
        : Object.create(null)
      presetData.componentConfig = {
        playback: true
      }
      if (!presetData.queueInfo?.useQueue) {
        if (!presetData['recordParams']) {
          presetData['recordParams'] = {};
        }
        presetData['recordParams']['recordType'] = 1;
      }
      presetData.controlBar = {
        maxLength: 5,
        components: [
          'CONTROL_REVOLVE',
          'CONTROL_MUTE',
          'CONTROL_RECORD',
          'CONTROL_SNAPSHOT',
          'CONTROL_RESOLUTION',
          'CONTROL_MESSAGE',
          'CONTROL_VIRTUAL_BACKGROUND',
          "CONTROL_BEAUTY"
        ],
        // 文字聊天配置
        CONTROL_MESSAGE: {
          privateChat: false,
          useFileManager: false // 是否需要文件传输功能
        },
        showHangUp: true //恒生
      }
      // presetData.controlBar = {
      //   maxLength: 3,
      //   components: [
      //     'CONTROL_REVOLVE',
      //     'CONTROL_MUTE',
      //     'CONTROL_RECORD',
      //     'CONTROL_RESOLUTION',
      //   ],
      //   // 文字聊天配置
      //   CONTROL_MESSAGE: {
      //     privateChat: false,
      //     useFileManager: false // 是否需要文件传输功能
      //   },
      //   showHangUp: true //同花顺
      // }

      function getRandom() {
        const randomNum = Math.floor(Math.random() * 9000 + 1000)
          .toString()
        return randomNum
      }

      let businessData = {
        'busSerialNumber': '2083411918',
        'custID': '2083411918',
        'custName': '张美丽',
        "busType": "1",
        // 'itemCode': '02b489c5',
        // "prodCode": "5b5f29e3",
        "itemCode": '002',
        "prodCode": "h5test01",
        "isSeniorInvestors": "0",
        "isProfessionalInvestor": "0",
        'sysID': '1',
        'sysIDName': 'h5',
        "name": "邹家琦"
      }
      businessData['busSerialNumber'] = getRandom()

      if (presetData.userPics) {
        // userPics.idCardInfo
        businessData['userPics'] = presetData.userPics
      }

      //todo 坐席登录已经拿到用户信息
      presetData['businessData'] = businessData;
      presetData.theme = {
        color: 'blue',
        btnType: 'rect',
        videoViewMode: 'STANDARD',
      }
      var viewMode = 'STANDARD';

      // TODO: mock 座席名称和userid
      const nickName = 'yyf01' + Math.floor(Math.random() * 1000)
      const strUserId = 'yyf01' + Math.floor(Math.random() * 1000)
      let initData = {
        presetData: {
          business: {
            '001': {
              ttsInfo:
                // ttsInfo
                [
                  {
                    "groupName": "适当性视频",
                    "groupKey": "B3",
                    "groupList": [
                      // {
                      //   "ruleName": "测试适当性C1视频录制",
                      //   "type": 3,
                      //   "broadcast": '{"checkQuestion":"投资者风险告知mp4","keywordList":["","",""],"fileName":"202312282038226f3e.mp4","fullUrl":"https%3A%2F%2Fdev.bairuitech.cn%3A16016%2Ftemp%2FsysFile%2FmediaPlay%3FfileName%3D202312282038226f3e.mp4%26fileType%3D1","outerUrl":"","fileId":"16d9b"}',
                      //   "checkQuestion": "",
                      //   "expectAnswer": "",
                      //   "unExpectAnswer": "",
                      //   "customerAnswer": ""
                      // },
                      {
                        "ruleName": "MP3话术",
                        "type": 4,
                        "broadcast": '{"ruleName":"MP3话术","checkQuestion":"请问您是否已知晓期货市场风险，阅读并理解《期货开户协议条款》《期货交易风险说明书》《投资者风险须知》等相关内容，请回答是或不是。","keywordList":["","",""],"fileName":"1748577300831.mp3","fullUrl":"https://dev.bairuitech.cn:16016/h5/h5/static/images/1748577300831.mp3","outerUrl":"","fileId":"16d9b"}',
                        "checkQuestion": "请问您是否已知晓期货市场风险，阅读并理解《期货开户协议条款》《期货交易风险说明书》《投资者风险须知》等相关内容，请回答是或不是。",
                        "expectAnswer": "",
                        "unExpectAnswer": "",
                        "customerAnswer": "",
                        "fixed": 1,
                      },
                      {
                        "ruleName": "MP3话术22",
                        "type": 4,
                        "broadcast": '{"checkQuestion":"请问您是否已知晓期货市场风险，阅读并理解《期货开户协议条款》《期货交易风险说明书》《投资者风险须知》等相关内容，请回答是或不是。","keywordList":["","",""],"fileName":"202312282038226f3e.mp4","fullUrl":"https://dev.bairuitech.cn:16016/h5/h5/static/images/1748577300831.mp3","outerUrl":"","fileId":"16d9b"}',
                        "checkQuestion": "请问您是否已知晓期货市场风险，阅读并理解《期货开户协议条款》《期货交易风险说明书》《投资者风险须知》等相关内容，请回答是或不是。",
                        "expectAnswer": "",
                        "unExpectAnswer": "",
                        "customerAnswer": "",
                        "fixed": 1,
                      },
                      {
                        "ruleName": "语言播报1",
                        "type": 0,
                        "broadcast": "尊敬的李桂玲，你的评级为C4，接下去为您播放匹配告知，请认真聆听全程不要离框",
                        "checkQuestion": "",
                        "expectAnswer": "",
                        "unExpectAnswer": "",
                        "customerAnswer": "",
                        "fixed": 1,
                      },
                      {
                        "ruleName": "一问一答",
                        "type": 1,
                        "broadcast": "",
                        "checkQuestion": "请问您是否是李桂玲本人？",
                        "expectAnswer": "是,是是,是是是",
                        "unExpectAnswer": "否,否否,不,不是,不是的",
                        "customerAnswer": "",
                        "fixed": 1,
                      },
                      {
                        "ruleName": "文字跟读",
                        "type": 2,
                        "broadcast": "请大声朗读以下内容",
                        "checkQuestion": "",
                        "expectAnswer": "",
                        "unExpectAnswer": "",
                        "customerAnswer": "我愿意自动遵守期货公司投资交易风险规则！",
                        "fixed": 1,
                      },
                    ]
                  },
                  {
                    "groupName": "居间人视频",
                    "groupKey": "Eo",
                    "groupList": [
                      {
                        type: 1,
                        broadcast: '',
                        checkQuestion: '请问您是否已知晓期货市场风险，阅读并理解《期货开户协议条款》《期货交易风险说明书》《投资者风险须知》等相关内容，请回答是或不是。',
                        expectAnswer: '是，是的，四',
                        unExpectAnswer: '不，不是，不是的',
                        ruleName: '确认风险揭示',
                        "fixed": 1,
                      },
                      {
                        "ruleName": "测试适当性C4视频录制",
                        "type": 3,
                        "broadcast": '{"checkQuestion":"投资者风险告知mp4","keywordList":["","",""],"fileName":"202312282038226f3e.mp4","fullUrl":"https://dev.bairuitech.cn:16016/h5/mp4/qihuo.mp4","outerUrl":"","fileId":"16d9b"}',
                        "checkQuestion": "",
                        "expectAnswer": "",
                        "unExpectAnswer": "",
                        "customerAnswer": "",
                        "fixed": 1,
                      }
                    ]
                  }
                ]
            }
          }
        },
        businessData: {
          // javaConfig: {
          // 	baseUrl: location.protocol + '//' + location.host + '/gateway',
          // 	token: sessionStorage.getItem('token') || ''
          // },
          // agentInfo: this.agentInfo,
          // businessExtra: this.businessExtra,
          // mode: 1 // 双录模式，1--预约，2--排队
        }
      }

      //debugger
      //测试数据
      if (presetData?.business && presetData?.business['001']) {
        presetData.business['001'].ttsInfo = initData.presetData.business['001'].ttsInfo
      }

      // 发送消息方法
      function sendMessage(cmd, data) {
        var agentAppFrame = document.querySelector('#agentApp').contentWindow
        agentAppFrame.postMessage({
          cmd: cmd,
          msg: data
        }, '*')
      }

      //新增监听
      window.onresize = function () {
        // document.querySelector('.is-fixed').style.position = 'absolute'
        if (document.querySelector('.is-fixed')) document.querySelector('.is-fixed').style.top = '1%'
        // document.querySelector('.is-fixed').style.right = '0'

        //console.log(1111)
      }

      let currentStep = '';
      // 接收消息方法
      function receiveMessage() {
        console.log('收到收到', event.data.cmd)
        let hideBar
        const cmd = event.data.cmd
        const msg = event.data.msg
        console.log('外层收到', event)
        cmd && console.log('%c log - cmd ---------------->', 'color: #67C23A', cmd)
        // debugger
        if (cmd == "CMDAnyChatDeviceVoice") {
          if (!presetData['deviceSelected']) {
            presetData['deviceSelected'] = {};
          }
          if (!presetData['deviceSelected']['deviceInfo']) {
            presetData['deviceSelected']['deviceInfo'] = {};
          }
          presetData['deviceSelected']['deviceInfo']['ttsData'] = msg
          localStorage.setItem('AnyChatAgentPresetData', JSON.stringify(presetData))
          location.href = "../demo/preset.html"
        }
        // 组件准备好，可以开始初始化
        if (cmd === 'CMDAnyChatAgentReady') {
          console.log('CMDAnyChatAgentReady')
          // timeout用于解决火狐刷新报socket的问题
          setTimeout(function () {
            sendMessage('CMDAnyChatAgentInit', { presetData: presetData })
          }, 100)
        }
        // debugger
        //向顶点业务层获取免冠照
        if (cmd == 'CMDAnyChatGetUserPhoto') {
          sendMessage('CMDAnyChatSendUserPhoto', { headShot: '' })
        }
        // debugger
        // 初始化完成回调
        if (cmd === 'CMDAnyChatAgentInitDone' || cmd === 'CMDAnyChatAgentReady') {
          document.querySelector('#servePage').style.width = '45%'
          document.querySelector('#workPage').style.width = '0'
        }

        if (cmd === 'CMDAnyChatAgentCurrentStep') {
          // QUEUE: 'STEP_QUEUE', // 呼叫等待中
          // VIDEO: 'STEP_ON_CALL', // 视频通话中
          // PLAYBACK: 'STEP_PLAYBACK' // 查看回放
          currentStep = msg.currentStep;
          if (msg.currentStep === 'STEP_QUEUE') {
            document.querySelector('#servePage').style.width = '45%'
            document.querySelector('#workPage').style.width = '0'
            document.querySelector('#business-buttons').style.maxHeight = '0'
          } else if (msg.currentStep === 'STEP_ON_CALL') {
            document.querySelector('#servePage').style.width = '0'
            if (viewMode == 'VERTICAL') {
              document.querySelector('#workPage').style.width = '70%'
            } else {
              document.querySelector('#workPage').style.width = '45%'
            }
          } else if (msg.currentStep === 'STEP_PLAYBACK') {
            document.querySelector('#servePage').style.width = '0'
            if (viewMode == 'VERTICAL') {
              document.querySelector('#workPage').style.width = '70%'
            } else {
              document.querySelector('#workPage').style.width = '45%'
            }
          }

        }

        // 视频呼叫通知
        if (cmd === 'CMDAnyChatAgentVideoCall') {
          hideBar = document.querySelector('.fixed-hide-bar')
          window.addClass(hideBar, 'has-call')
        }

        // 视频通话开始回调
        if (cmd === 'CMDAnyChatAgentVideoStart') {
          //sendMessage('CMDAnyChatAgentBtnStatus', { btns: ['CONTROL_SNAPSHOT'], status:0 })
          // FIXME: 音视频开始节点的处理示例
          document.querySelector('#servePage').style.width = '0'

          // document.querySelector('#device-detect').style.display = 'none'
          if (viewMode == 'VERTICAL') {
            document.querySelector('#workPage').style.width = '70%'
          } else {
            document.querySelector('#workPage').style.width = '45%'
          }

          //debugger
          if (presetData.queueInfo.hasFlow === false) {
            sendMessage('CMDAnyChatUpdateBusiness', { key: '001' })
          }

          hideBar = document.querySelector('.fixed-hide-bar')
          if (hideBar.className.indexOf('has-call') >= 0) {
            window.removeClass(hideBar, 'has-call')
          }

          if (presetData.queueInfo.hasFlow === true) {
            if (presetData.queueInfo.useQueue === true) {
              //智能排队需要获取客户信息
              var userData = JSON.parse(msg.userStr || '{}')
              var businessExtra = JSON.parse(userData.businessExtra || '{}')
              var custId = businessExtra.custId
              var from = businessExtra.from

              businessData['busSerialNumber'] = getRandom()
              if (presetData.userPics) {
                businessData['userPics'] = presetData.userPics
              }
              businessData['sysIDName'] = from;
              if (custId) {
                businessData['custID'] = custId;
              }
              // debugger
              sendMessage('CMDAnyChatUpdateUserBusinessData', businessData)
            }
          }
        }

        if (cmd === 'CMDAnyChatAgentDetectionConfirmed') {
          document.querySelector('.func-icon-ready').style.display = 'block'
        }

        // 视频通话结束回调
        if (cmd === 'CMDAnyChatAgentVideoEnd') {
          // FIXME: 音视频结束节点的处理示例
          if (currentStep != '' && currentStep != 'STEP_QUEUE') {
            document.querySelector('#business-buttons').style.maxHeight = '70px'
          }
          document.querySelector('#device-detect').style.display = 'flex'
          document.querySelector('#servePage').style.width = '0'
          if (viewMode == 'VERTICAL') {
            document.querySelector('#workPage').style.width = '70%'
          } else {
            document.querySelector('#workPage').style.width = '45%'
          }
        }
        //清除免冠照
        if (cmd == 'CMDAnyChatClearUserPhoto') {
          document.querySelector('#agentInfoFrame')
            .contentWindow
            .clearImage()
        }
        // 拍照回调
        if (cmd === 'CMDAnyChatAgentSnapshot') {
          // FIXME: 模拟拍照照片展示
          if (msg.base64) {
            // base64格式输出照片
            // const newWindow = window.open('')
            // newWindow.document.write('<img src="data:image/jpg;base64,' + msg.base64 + '">')
            // newWindow.document.title = '截图拍照输出demo'
            sendMessage('CMDAnyChatUpdateBusiness', {
              key: 'userPics',
              value: {
                idCardInfo: msg.base64
              }
            })
            document.querySelector('#agentInfoFrame')
              .contentWindow
              .insertImage(msg.base64, 1)
          } else if (msg.filePath) {
            alert('拍照图片在服务器：' + msg.filePath)
          }
        }
        // 坐席收到来自客户端拍摄的照片处理
        if (cmd === 'CMDAnyChatReceiveSnapshotFromClient') {
          // FIXME: 模拟拍照照片展示
          if (msg.base64) {
            document.querySelector('#agentInfoFrame')
              .contentWindow
              .insertImage(msg.base64, msg.imgType)
          } else if (msg.filePath) {
            alert('拍照图片在服务器：' + msg.filePath)
          }
        }

        // 收到实时传输数据
        if (cmd === 'CMDAnyChatAgentMsgTransfer') {
          // TODO: something
        }

        // 发送实时传输数据的结果回调
        if (cmd === 'CMDAnyChatAgentMsgTransferResult') {
          // TODO: something
        }

        // 提交业务回调
        if (cmd === 'CMDAnyChatAgentSubmit') {
          // debugger
          if (!presetData.queueInfo.hasFlow) {
            if (!presetData.queueInfo.useQueue) {
              window.location.href = './preset.html'
            } else {
              document.querySelector('#business-buttons').style.maxHeight = '0'
              document.querySelector('#servePage').style.width = '45%'
              document.querySelector('#workPage').style.width = '0'
            }
          }
        }
        if (cmd === 'CMDAnyChatAgentRecordStart') {
          console.log('CMDAnyChatAgentRecordStart:' + JSON.stringify(msg))
        }
        // 服务器端分段录制结束回调
        if (cmd === 'CMDAnyChatAgentSectionRecordDone') {
          console.log('CMDAnyChatAgentSectionRecordDone:' + JSON.stringify(msg))
        }
        // 业务层实现回放页
        if (cmd === 'CMDAnyChatOnPlaybackStart') {
          console.log('CMDAnyChatOnPlaybackStart:' + JSON.stringify(msg))
        }
        //新增流水
        if (cmd === 'CMDAnyChatAgentOnBusinessData') {
          console.log('CMDAnyChatAgentOnBusinessData:' + JSON.stringify(msg))
        }
        //流水绑定视频成功
        if (cmd === 'CMDAnyChatAgentOnBusinessVideoDone') {
          console.log('CMDAnyChatAgentOnBusinessVideoDone:' + JSON.stringify(msg))
        }
        //已提交流水
        if (cmd === 'CMDAnyChatAgentFinish') {
          // FIXME: 控制台显示输出数据
          console.log('CMDAnyChatAgentFinish:' + JSON.stringify(msg))

          if (!presetData.queueInfo.useQueue) {
            window.location.href = './preset.html'
          }
        }
        // 组件异常消息
        if (cmd === 'CMDAnyChatAgentError') {
          // FIXME: 异常消息输出示例
          console.error('组件异常消息回调：========= start')
          console.error(msg)
          console.error('组件异常消息回调：========= end')

          if (
            msg.code === 100101 ||
            msg.code === 100102 ||
            msg.code === 100103 ||
            msg.code === 100104 ||
            msg.code === 100105 ||
            msg.code === 100106 ||
            msg.code === 100107 ||
            msg.code === 1012
          ) {
            hideBar = document.querySelector('.fixed-hide-bar')
            if (hideBar.className.indexOf('has-call') >= 0) {
              window.removeClass(hideBar, 'has-call')
            }
          }
        }

        if (cmd === 'CMDAnyChatAgentClientStateInfo') {
          // TODO: something
        }

        if (cmd === 'CMDAnyChatAgentTextMessage') {
          console.log('======TextMessage======', msg)
        }

        if (cmd === 'CMDAnyChatQueueUserInfo') {
          console.log('收到CMDAnyChatQueueUserInfo', event)
        }
      }

      // DOM加载完毕就监听回调
      document.addEventListener('DOMContentLoaded', function () {
        window.addEventListener('message', receiveMessage, false)
      })
      // 页面卸载前注销message事件
      window.onbeforeunload = function () {
        window.removeEventListener('message', receiveMessage)
      }

      // 请求提交业务
      document.querySelector('#btnSubmit')
        .addEventListener('click', function () {
          if (presetData.queueInfo.hasFlow === true) {
            sendMessage('CMDAnyChatAgentHandleSubmit', { 'businessStatus': 2 })
          } else {
            sendMessage('CMDAnyChatAgentRequestSubmit')
          }
        })

      // 请求设备检测
      // document.querySelector('#device-detect')
      //   .addEventListener('click', function () {
      //     sendMessage('CMDAnyChatAgentOpenDeviceDetection')
      //   })

      for (var i = 1; i < 4; i++) {
        var themeItem = document.querySelector('#theme-' + i);
        console.log('themeItem', themeItem);

        themeItem.addEventListener('click', function (e) {
          var clickThemeItem = e.currentTarget;

          if (clickThemeItem.classList.contains('popup-list-item-checked')) {
            return;
          }


          clickThemeItem.classList.add('popup-list-item-checked');
          var themeData;

          switch (clickThemeItem.id.substr(-1, 1)) {
            case '1': {
              themeData = 'blue';
              break;
            }
            case '2': {
              themeData = 'red';
              break;
            }
            case '3': {
              themeData = {
                '--theme-color-brand': '#037df3', // 主题色
                '--theme-color-brand-1': '#528AFB', // 按钮悬浮色
                '--theme-color-brand-2': '#9CC3FD',
                '--theme-color-brand-3': '#C2DCFE',
                '--theme-color-brand-4': '#E8F3FF',
              };
              break;
            }
          }

          sendMessage('CMDAnyChatAgentUpdateTheme', { color: themeData });

          for (var j = 1; j < 4; j++) {
            var otherItem = document.querySelector('#theme-' + j);

            if (otherItem !== clickThemeItem) {
              otherItem.classList.remove('popup-list-item-checked');
            }
          }
        })
      }

      for (var i = 1; i < 3; i++) {
        var btnItem = document.querySelector('#btn-shape-' + i);

        btnItem.addEventListener('click', function (e) {
          var clickBtnItem = e.currentTarget;

          if (clickBtnItem.classList.contains('popup-list-item-checked')) {
            return;
          }


          clickBtnItem.classList.add('popup-list-item-checked');
          var btnData;

          switch (clickBtnItem.id.substr(-1, 1)) {
            case '1': {
              btnData = 'rect';
              break;
            }
            case '2': {
              btnData = 'round';
              break;
            }
          }

          sendMessage('CMDAnyChatAgentUpdateTheme', { btnType: btnData });

          for (var j = 1; j < 4; j++) {
            var otherItem = document.querySelector('#btn-shape-' + j);

            if (otherItem !== clickBtnItem) {
              otherItem.classList.remove('popup-list-item-checked');
            }
          }
        })
      }

      for (var i = 1; i < 3; i++) {
        var modeItem = document.querySelector('#view-mode-' + i);

        modeItem.addEventListener('click', function (e) {
          var clickBtnItem = e.currentTarget;

          if (clickBtnItem.classList.contains('popup-list-item-checked')) {
            return;
          }


          clickBtnItem.classList.add('popup-list-item-checked');
          viewMode;

          switch (clickBtnItem.id.substr(-1, 1)) {
            case '1': {
              viewMode = 'STANDARD';
              break;
            }
            case '2': {
              viewMode = 'VERTICAL';
              break;
            }
            case '3': {
              viewMode = 'HORIZON';
              break;
            }
          }

          var currentWorkWidth = document.querySelector('#workPage').style.width;
          if (currentWorkWidth[0] !== '0') {
            if (viewMode == 'VERTICAL') {
              document.querySelector('#workPage').style.width = '70%'
            } else {
              document.querySelector('#workPage').style.width = '45%'
            }
          }

          sendMessage('CMDAnyChatAgentUpdateTheme', { videoViewMode: viewMode });

          for (var j = 1; j < 4; j++) {
            var otherItem = document.querySelector('#view-mode-' + j);

            if (otherItem !== clickBtnItem) {
              otherItem.classList.remove('popup-list-item-checked');
            }
          }
        })
      }


      /**
       * @description 打开外呼抽屉
       * @date 2023/7/24
       */

      // document.querySelector('#outbound-calling')
      //   .addEventListener('click', function () {
      //     const drawer = document.querySelector('.drawer')
      //     const mask = document.querySelector('.drawer-mask')
      //     if (drawer.classList.contains('drawer-show')) {
      //       drawer.classList.remove('drawer-show')
      //       mask.classList.remove('drawer-mask-show')
      //     } else {
      //       drawer.classList.add('drawer-show')
      //       mask.classList.add('drawer-mask-show')
      //     }
      //   })
      /**
       * @description 外呼
       * @date 2023/7/24
       */
      // document.querySelector('#video-calling')
      //   .addEventListener('click', function () {
      //     const phoneItem = document.querySelector('#phone-item')
      //     const phoneInput = document.querySelector('#phone').value
      //     const testRegx = /^(?:(?:\+|00)86)?1(?:(?:3[\d])|(?:4[5-79])|(?:5[0-35-9])|(?:6[5-7])|(?:7[0-8])|(?:8[\d])|(?:9[189]))\d{8}$/
      //     if (testRegx.test(phoneInput)) {
      //       if (phoneItem.classList.contains('c-form-item_error')) {
      //         phoneItem.classList.remove('c-form-item_error')
      //       }
      //       sendMessage('CMDAnyChatAgentOutboundCalling', { data: phoneInput })
      //     } else {
      //       if (!phoneItem.classList.contains('c-form-item_error')) {
      //         phoneItem.classList.add('c-form-item_error')
      //       }
      //     }
      //   })

      // 取消业务
      document.querySelector('#btnCancel')
        .addEventListener('click', function () {
          console.log('%c log - click ---------------->', 'color: #67C23A', 'click')
          if (presetData.queueInfo.hasFlow === true) {
            sendMessage('CMDAnyChatAgentHandleSubmit', { 'businessStatus': 1 })
          } else {
            sendMessage('CMDAnyChatAgentRequestSubmit', false)
          }
        }, false)

      //测试
      document.querySelector('#queueUserInfo').style = 'display:none'
      document.querySelector('#queueUserInfo')
        .addEventListener('click', function () {
          console.log('areaId', presetData?.queueInfo?.areaId)
          sendMessage('CMDAnyChatQueryQueueUserInfo', { 'areaId': presetData?.queueInfo?.areaId })
        }, false)
    }
    )()
  </script>
  <script src="./js/fixed.js"></script>
  <script>
    window.onload = function () {
      document.querySelector('#goSettings')
        .addEventListener('click', function () {
          window.location.href = './preset.html'
        }, false)
    }
    document.querySelector('#logout')
      .addEventListener('click', function () {
        window.location.href = './login.html'
      }, false)
  </script>
</body>

</html>