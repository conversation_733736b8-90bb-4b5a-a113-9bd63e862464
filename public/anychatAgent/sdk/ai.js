;(function () {
  var _global;

  // 统一处理AI能力
  function AIUtil(AnyChatAIRobot, version) {
    this.AnyChatAIRobot = AnyChatAIRobot; // ai对象
    this.ttsBase64 = ''; // 储存base64
    this.asrResult = ''; // 语音识别的内容
    this.asrTaskId = ''; // asr任务Id
    this.checkTaskId = ''; // 在框检测任务id
    this.checkTime = 0; // 防止在框检测多次返回
    this.strLength = 0; // 转化玩一定长度tts才开始流程
    this.asrTimer = null; // asr超时
    this.isAsrFinish = false;
    this.version = version || 'v3'; // AI能力版本号
  }

  AIUtil.prototype = {
    constructor: AIUtil,
    on: function (name, fn) {
      var obj = this;
      if (!obj[name]) {
        obj[name] = [];
      }
      obj[name].push(fn);
    },
    _emit: function (name, val) {
      var obj = this;
      if (obj[name]) {
        obj[name].forEach(function (fn) {
          fn(val);
        });
      }
    },
    _off: function (name, fn) {
      var obj = this;
      if (obj[name]) {
        if (fn) {
          var index = obj[name].indexOf(fn);
          if (index > -1) {
            obj[name].splice(index, 1);
          }
        } else {
          obj[name].length = 0;
        }
      }
    },

    // tts转换
    doTTS: function (str,speed=1,tts_type=0,fixed=1) {
      str = str.replace(/\s+/g, ' '); // 多个空格保留一个
      console.log(str);
      var len = str.length;
      this.ttsBase64 = '';
      this.strLength += len;
      var obj = {};

      if (this.version === 'v1') {
        obj = {
          contentStr: str,
          type: 2, // 1男声2女生
          mode: 1, // 模式 1本地2在线
          event: this._onAITTSPrepare.bind(this), // 能力准备回调
          errorevent: this._onAITTSError.bind(this), // 错误回调
          resultevent: this._onAITTSResult.bind(this), // 结果回调
          finishevent: this._onAITTSFinish.bind(this), // 结束回调
          speechrate: 50, // 速度
          audioformat: 3 // 音频格式
        };
        if (len > 300) {
          obj.longtxt = 1;
        } else {
          obj.longtxt = 0;
        }
        this.AnyChatAIRobot.doTTS(obj);
      } else if (this.version === 'v3') {
        obj = {
          mode: 1, // 模式 1本地2在线
          version: 'v3',
          timeOut: 10000,
          request: {
            text: str, // 待合成的文本信息，如：我要开户
            language: 1, // 语种，默认1 1:普通话，2:英语，3：粤语，4：四川话
            num_mode: 0, // 读数类型，默认0 0：自动判断，1：数值，2：数字
            tts_style: 0, // 语言停顿风格，默认0 0 – 正常停顿风格;1 – 稍作顿挫;2 – 抑扬顿挫;3 – 平稳连贯;4 – 一气呵成
            tts_type: tts_type, // 声音类型（男、女等）默认0 0->默认标准女声，1、温暖⼥声2、情感⼥声3、亲和⼥声4->默认标准男声，5、成熟男声6、活⼒男声7、情感男声
            fixed: fixed, // 常用话术，默认0 1-常用；0 - 非常用
            format: 'mp3', // 输出音频格式，默认mp3选项：mp3，wav，pcm
            samplespersec: 16000, // 输出音频采样率，默认16000 选项：8000，16000
            bitspersample: 16, // 采样精度，默认16 选项：16：16bits；32：32bits
            channels: 1, // 声道数，默认1 选项：1：单声道；2：双声道
            speed: speed, // 合成返回音频的语速，默认值：1取值范围:[0,2]小于1是慢速，大于1是快速，最快2倍速
            pitch: 50, // 合成返回音频的语调。默认值：50，取值范围:[0,100]。
            volume: 50, // 合成返回音频的音量。默认值：50，取值范围:[0,100]
            ext: '' // 透传参数
          },
          event: this._onAITTSPrepare.bind(this), // 能力准备回调
          errorevent: this._onAITTSError.bind(this), // 错误回调
          resultevent: this._onAITTSResult.bind(this), // 结果回调
          finishevent: this._onAITTSFinish.bind(this) // 结束回调
        };
        this.AnyChatAIRobot.startNewTTS(obj);
      }
    },

    // 开始语音识别
    startASR: function (aiAsrOpt, targetUserId) {
      this.asrTaskId = '';
      this.asrResult = '';
      this.isAsrFinish = false;

      if (this.version === 'v1') {
        this.AnyChatAIRobot.startASR({
          event: this._onAIVoicePrepare.bind(this),
          errorevent: this._onAIVoiceError.bind(this),
          resultevent: this._onAIVoiceResult.bind(this),
          aiAsrOpt: aiAsrOpt,
          targetUserId: targetUserId || 0,
          firm: aiAsrOpt.firm || 0
        });
      } else if (this.version === 'v3') {
        // 查询客户音频采样率
        const audioSamplerate = window.BRAC_GetUserStreamInfoInt(targetUserId || 0,
          0,
          window.BRAC_STREAMINFO_AUDIOSAMPLERATE);
        this.AnyChatAIRobot.startNewASR({
          event: this._onAIVoicePrepare.bind(this),
          errorevent: this._onAIVoiceError.bind(this),
          resultevent: this._onAIVoiceResult.bind(this),
          version: 'v3',
          request: {
            userid: targetUserId || 0, // 用户ID，默认操作用户ID
            timeout: 10000, // 超时时间，单位ms，默认0，不生效
            samplespersec: audioSamplerate, // 音频采样率，默认16000 选项：8000；16000
            bitspersample: 16, // 采样精度，默认16 选项：16：16bits；32：32bits
            channels: 1, // 声道数，默认1 1：单声道；2：双声道
            language: 1, // 语种，默认1 1:普通话，2:英语，3：粤语，4：四川话
            silence_time: 800, // 语音活动检测， 根据静音时长自动断句，默认800ms
            arabic_mode: 1, // 将返回结果的数字格式规则为阿拉伯数字格式，默认开启 0：关闭，1：开启
            symbol: 0, // 默认1，0：不要标点，1：要标点
            ext: '' // 透传参数
          }
        });
      }

      var _self = this;
      if (this.asrTimer) {
        clearTimeout(_self.asrTimer);
      }
      this.asrTimer = setTimeout(function () {
        clearTimeout(_self.asrTimer);
        _self.asrTimer = null;
        if (!_self.asrTaskId) {
          // eslint-disable-next-line no-undef
          _self.asrTaskId = instance.createGuid();
          _self._onAIVoicePrepare({ taskId: _self.asrTaskId });
          _self._onAIVoiceError({ taskId: _self.asrTaskId });
        } else {
          if (!_self.isAsrFinish) {
            _self._onAIVoiceError({ taskId: _self.asrTaskId });
          }
        }
      }, 15000);
    },

    // 结束语音识别
    stopASR: function () {
      if (this.version === 'v1') {
        this.AnyChatAIRobot.stopASR({
          asrTaskId: this.asrTaskId,
          event: this._onAIVoiceFinish.bind(this)
        });
      } else if (this.version === 'v3') {
        this.AnyChatAIRobot.stopNewASR({
          version: 'v3',
          asrTaskId: this.asrTaskId,
          event: this._onAIVoiceFinish.bind(this)
        });
      }
    },

    // 在框检测
    aiCheck: function (aiAfrOpt, targetUserId) {
      if (this.version === 'v1') {
        this.AnyChatAIRobot.startFaceDetect({
          mode: 2,
          event: this._onAICheckPrepare.bind(this),
          errorevent: this._onAICheckError.bind(this),
          resultevent: this._onAICheckResult.bind(this),
          aiAfrOpt: aiAfrOpt,
          targetUserId: targetUserId || 0,
          firm: 100
        });
      } else if (this.version === 'v3') {
        this.AnyChatAIRobot.startNewAFR({
          mode: 2,
          version: 'v3',
          timeOut: 10000,
          request: {
            userid: targetUserId || 0, // 用户ID，服务器默认操作用户ID
            interval: 0, // 图片抓拍间隔时间，单位ms，默认0 0仅抓拍一次；小于400ms采用1000ms
            timeout: 10000, // 超时时间，单位ms，默认10000ms
            ext: '' // 透传参数
          },
          event: this._onAICheckPrepare.bind(this),
          errorevent: this._onAICheckError.bind(this),
          resultevent: this._onAICheckResult.bind(this)
        });
      }
    },

    // 人脸识别结束
    stopFaceDetect: function (taskId) {
      if (this.version === 'v1') {
        this.AnyChatAIRobot.stopFaceDetect({
          facetaskid: taskId,
          finishevent: this._onAICheckFinish.bind(this)
        });
      } else if (this.version === 'v3') {
        this.AnyChatAIRobot.stopNewAFR({
          mode: 2,
          version: 'v3',
          facetaskid: taskId,
          finishevent: this._onAICheckFinish.bind(this)
        });
      }
    },

    // 人脸比对
    aiCompare: function (compareNum, aiAfrOpt, targetUserId) {
      if (compareNum === 1) {
        if (aiAfrOpt.image1 === '') {
          this._emit('error', '照片信息不全');
          return;
        }
        if (this.version === 'v1') {
          this.AnyChatAIRobot.startFaceDetect({
            targetUserId: targetUserId || 0,
            content: 1,
            mode: 4,
            aiAfrOpt: aiAfrOpt,
            event: this._onAIComparePrepare.bind(this),
            errorevent: this._onAICompareError.bind(this),
            resultevent: this._onAICompareResult.bind(this),
            finishevent: this._onAICompareFinish.bind(this)
          });
        } else if (this.version === 'v3') {
          this.AnyChatAIRobot.startNewAFR({
            mode: 4,
            version: 'v3',
            timeOut: 10000,
            request: {
              userid: targetUserId || 0, // 指定抓拍图片的目标用户ID，服务器默认操作用户ID
              src_image: aiAfrOpt.image1, // 源图片：Base64数据
              ext: '' // 透传参数
            },
            event: this._onAIComparePrepare.bind(this),
            errorevent: this._onAICompareError.bind(this),
            resultevent: this._onAICompareResult.bind(this),
            finishevent: this._onAICompareFinish.bind(this)
          });
        }
      } else {
        if (aiAfrOpt.image1 === '' || aiAfrOpt.image2 === '') {
          this._emit('error', '照片信息不全');
          return;
        }
        if (this.version === 'v1') {
          this.AnyChatAIRobot.startFaceDetect({
            targetUserId: targetUserId || 0,
            content: 1,
            mode: 8,
            aiAfrOpt: aiAfrOpt,
            event: this._onAIComparePrepare.bind(this),
            errorevent: this._onAICompareError.bind(this),
            resultevent: this._onAICompareResult.bind(this),
            finishevent: this._onAICompareFinish.bind(this)
          });
        } else if (this.version === 'v3') {
          this.AnyChatAIRobot.startNewAFR({
            mode: 8,
            version: 'v3',
            timeOut: 10000,
            request: {
              userid: targetUserId || 0, // 指定抓拍图片的目标用户ID，服务器默认操作用户ID
              usera_image: aiAfrOpt.image1, // 单人照A的Base64数据
              userb_image: aiAfrOpt.image2, // 单人照B的Base64数据
              ext: '' // 透传参数
            },
            event: this._onAIComparePrepare.bind(this),
            errorevent: this._onAICompareError.bind(this),
            resultevent: this._onAICompareResult.bind(this),
            finishevent: this._onAICompareFinish.bind(this)
          });
        }
      }
    },

    // tts开始回调
    _onAITTSPrepare: function (data) {
      console.log('_onAITTSPrepare',data)
      // logger('tts开始回调');
    },

    // tts结果回调
    _onAITTSResult: function (data) {
      console.log('%c log - _onAITTSResult ---------------->', 'color: #67C23A', data);
      // logger('tts结果回调');
      if (data.errorcode === 0) {
        let result = data.result;
        if (result) {
          result = JSON.parse(result);
          if (this.version === 'v1' && result.errno === 0) {
            this.ttsBase64 += result.audio;
          }
          if (this.version === 'v3' && result.airetcode === 0) {
            this.ttsBase64 += result.data;
          }
        }
      }
    },

    // tts结束回调
    _onAITTSFinish: function (data) {
      console.log('%c log - _onAITTSFinish ---------------->', 'color: #67C23A', data);
      // logger('tts结束回调');
      if (('errorcode' in data && data.errorcode !== 0) || this.ttsBase64 === '') {
        this._emit('ttserror', 'AI文字转语音失败');
      } else {
        this._emit('ttsResult', this.ttsBase64);
        this._emit('recordStart', true);
      }
    },

    // tts错误回调
    _onAITTSError: function (data) {
      console.log('tts错误错误',data)
      // logger('tts错误回调');
      this._emit('ttserror', 'AI文字转语音失败，尝试重新转换');
    },

    // 语音识别开始
    _onAIVoicePrepare: function (data) {
      // logger('语音识别开始');
      if (this.asrTimer != null) {
        this.asrTaskId = data.taskId;
        this._emit('aiVoicePrepare', data.taskId);
      }
    },

    // 语音识别结果
    _onAIVoiceResult: function (data) {
      // logger('语音识别结果');
      if (
        this.asrTimer != null &&
        data.result &&
        this.asrTaskId === data.taskId
      ) {
        var res = JSON.parse(data.result).data;
        if (this.version === 'v1') {
          if (res.length > 2 && res.indexOf('"') === 0 && res.lastIndexOf('。"') === res.length - 2) {
            res = res.slice(1, res.length - 2);
          }
          this.asrResult += res;
        } else if (this.version === 'v3') {
          this.asrResult = res;
        }
      }
    },

    // 语音识别错误
    _onAIVoiceError: function (data) {
      // logger('语音识别错误');
      // this._emit('_onAIVoiceError', {
      //   taskId: data.taskId,
      //   result: this.asrResult
      // });
    },

    // 语音识别结束
    _onAIVoiceFinish: function (data) {
      // logger('语音识别结束');
      if (this.asrTimer != null && this.asrTaskId === data.taskId) {
        this.isAsrFinish = true;
        this._emit('aiVoiceResult', {
          taskId: data.taskId,
          result: this.asrResult
        });
        this.asrResult = '';
      }
    },

    // 人脸比对开始
    _onAIComparePrepare: function (data) {
      // logger('人脸比对开始');
      this._emit('comparePrepare', data.taskId);
    },

    // 人脸比对结果
    _onAICompareResult: function (data) {
      // logger('人脸比对结果');
      this._emit('compareResult', data);
    },

    // 人脸对比错误
    _onAICompareError: function (data) {
      // logger('人脸比对错误');
      this._emit('error', '人脸比对错误');
    },

    // 人脸比对结束
    _onAICompareFinish: function (data) {
      // logger('人脸比对结束');
    },

    // 在框检测开始
    _onAICheckPrepare: function (data) {
      // logger('在框检测开始');
      this.checkTaskId = data.taskId;
      this.checkTime = 0;
      this._emit('checkPrepare', data.taskId);
    },
    // 在框检测结果
    _onAICheckResult: function (data) {
      // logger('在框检测结果');
      this.checkTime++;
      if (this.checkTime > 1 || this.checkTaskId !== data.taskId) {
        return;
      }
      this._emit('checkResult', data);
      this.stopFaceDetect(data.taskId);
    },
    // 在框检测错误
    _onAICheckError: function (data) {
      // logger('在框检测错误');
      this.checkTime = 0;
      // this.stopFaceDetect(data.taskId);
      this._emit('error', '在框检测错误');
    },
    // 在框检测结束
    _onAICheckFinish: function (data) {
      this.checkTime = 0;
      // logger('在框检测结束');
    }
  };

  // 将插件对象暴露给全局对象（考虑兼容性）
  // eslint-disable-next-line no-eval
  _global = (function () {
    return this || (0, eval)('this');
  }());
  !('AIUtil' in _global) && (_global.AIUtil = AIUtil);
})();
