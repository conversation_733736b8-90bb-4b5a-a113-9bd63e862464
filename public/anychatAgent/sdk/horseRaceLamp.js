;
(function () {
  var _global
  if (typeof Object.assign != 'function') {
    Object.assign = function (target) {
      'use strict'
      if (target == null) {
        throw new TypeError('Cannot convert undefined or null to object')
      }

      target = Object(target)
      for (var index = 1; index < arguments.length; index++) {
        var source = arguments[index]
        if (source != null) {
          for (var key in source) {
            if (Object.prototype.hasOwnProperty.call(source, key)) {
              target[key] = source[key]
            }
          }
        }
      }
      return target
    }
  }
  var regDot = /[\u3002|\uff1f|\uff01|\uff0c|\u3001|\uff1b|\uff1a|\u201c|\u201d|\u2018|\u2019|\uff08|\uff09|\u300a|\u300b|\u3008|\u3009|\u3010|\u3011|\u300e|\u300f|\u300c|\u300d|\ufe43|\ufe44|\u3014|\u3015|\u2026|\u2014|\uff5e|\ufe4f|\uffe5]/

  function HorseRaceLamp(opt) {
    this._init(opt)
    this.canvas = document.getElementById(this.id)
    // this.canvas.width = this.wScale(this.canvas.parentElement.offsetWidth);
    // this.canvas.height = this.hScale(this.canvas.parentElement.offsetHeight);
    console.log('HorseRaceLamp', this.canvas)
    if (!this.canvas) return
    this.canvas.style.display = 'none'
    var imgCanvas = document.getElementById(this.id + 'HorseRaceLamp')
    if (!imgCanvas) {
      imgCanvas = document.createElement('canvas')
    }

    var width =parseInt(this.canvas.width)
    var height = this.canvas.parentElement
      ? this.hScale(this.canvas.parentElement.offsetHeight)
      : parseInt(this.canvas.height)

    while (width % 4 != 0) {
      width++
    }
    if (height > (width / 4) * 3) {
      height = (width / 4) * 3
    }
    while (height % 4 != 0) {
      height++
    }
    console.log('height ', height)
    imgCanvas.width = width
    imgCanvas.height = height
    imgCanvas.style.display = 'none'
    this.imgCanvasContext = imgCanvas.getContext('2d')
    this.imgCanvas = imgCanvas
    this.imgCanvasContext.fillStyle = 'white'
    this.imgCanvasContext.fillRect(0, 0, width, height)
    this.context = this.canvas.getContext('2d')
    this.inter = null
    this.textBaseline = 'top'
    this.scrollHeight = 0 //滚动高度
    this.index = 0//跑马灯下标
    this.arrIndex = 0//第几段
    this.imageType = 'image/jpeg'
    this.fontSize = this.wScale(parseInt(this.fontSize)) + 'px'
    this.height = this.hScale(this.height)
    this.top = this.hScale(this.top)
    // this.padding = this.wScale(this.padding)
    this.paddingLeft = this.wScale(this.paddingLeft);
    this.paddingRight = this.wScale(this.paddingRight);

    this.maxHeight = this.top
  }

  HorseRaceLamp.prototype = {
    constructor: this,
    _init: function (opt) {
      var _self = this
      // 默认参数 和 初始化
      var defaultSettings = {
        id: '', //canvas id
        defaultColor: 'black',//默认颜色
        color: 'blue', //画笔颜色
        bgColor: 'white', //文本背景色
        fontWeight: '500',
        fontSize: '16px',
        fontFamily: 'Microsoft YaHei',
        height: 24, //行高
        top: 10, //距顶部高度
        // padding: 10, //左右边距
        paddingLeft: 10,//左边距
        paddingRight: 10,//右边距
        scrollLine: 3, //滚动时显示多少行
        canvas2Img: false,  // 是否需要插入图片到录像中
        startArryIndex: 0,//设置进行跑马灯开始的位置
        quality: 1,//图片质量
        sdkInstance: null,
        indent: 0, //首行缩进
        img: null, // 气泡框插入的图片
        rate:2 // 画布缩放比例，1x时画布模糊
      }
      _self = Object.assign(_self, defaultSettings, opt) //配置参数
    },
    _translateFont: function (obj) {
      var fontWeight = obj.fontWeight || this.fontWeight
      var fontSize = obj.fontSize || this.fontSize
      var fontFamily = obj.fontFamily || this.fontFamily
      return fontWeight + ' ' + fontSize + ' ' + fontFamily
    },
    drawText: function (text) {
      if (!this.context) return
      this.context.fillStyle = 'white'
      this.context.fillRect(0, 0, this.canvas.width, this.canvas.height)
      this._resetParam()//重置参数清空定时器
      this.textArry = text
      this.arr = []
      var t = this.top
      var j = 0
      var marginTop = 0
      var marginBottom = 0
      var boxArr = []
      for (var k = 0; k < text.length; k++) {
        var arr = []
        var obj = text[k]
        marginTop = this.hScale(parseInt(obj.marginTop)) || 0
        t += marginTop
        j = this._dealChr(arr, obj, t, j)
        marginBottom = this.hScale(parseInt(obj.marginBottom)) || 0
        t += marginBottom
        // arr 每一段
        this.arr.push(arr)
        j++
        // 每个盒子的高度
        boxArr.push(t - marginBottom + j * parseInt(this.height))
      }
      if (this.img) {
        var type = false // 是否绘制多个气泡框
        for (var i in text) {
          if (text[i].img) {
            type = true
          }
        }

        this.canvas.height = type
          ? this.maxHeight + this.hScale(12)
          : Math.min(this.canvas.height, this.maxHeight + this.hScale(12))
        this.context.fillStyle = 'white'
        this.context.fillRect(0, 0, this.canvas.width, this.canvas.height)
        this.context.textBaseline = this.textBaseline
        this.canvas.style.display = 'block'
        this.canvas.style.width = this.canvas.width / this.wScale(1) + 'px'
        this.canvas.style.height = this.canvas.height / this.hScale(1) + 'px'
        if (type) {
          // 每段绘制一个气泡框
          this.context.save()
          for (var i in boxArr) {
            var h = i > 0
              ? boxArr[i] - boxArr[i - 1] - this.hScale(12)
              : boxArr[i]
            this.drawBox(h, text[i].img || this.img)
            this.context.translate(0, h + this.hScale(12))
          }
          this.context.restore()
        } else {
          this.drawBox(this.maxHeight, this.img)
        }
      }
      for (var i = 0; i < this.arr.length; i++) {
        var arr = this.arr[i]
        for (var j = 0; j < arr.length; j++) {
          this.context.font = arr[j].font
          this.context.fillStyle = arr[j].color
          this.context.fillText(arr[j].text, arr[j].x, arr[j].y)
        }
      }
      // this.canvas.height=this.maxHeight+100
      console.log('this.maxHeight ', this.maxHeight)
    },
    _dealChr: function (arr, obj, t, j) {
      var context = this.context
      var font = this._translateFont(obj)
      // var x = parseInt(this.padding)
      var x = parseInt(this.paddingLeft);
      var right = parseInt(this.paddingRight);
      var h = parseInt(this.height)
      var width = this.canvas.width
      context.textBaseline = this.textBaseline
      context.font = font
      var indent = parseInt(this.indent) * context.measureText('我').width
      var temp = '' //临时字符串
      var b = 0//空格长度；
      var chr = obj.text.split('')
      var color = obj.color || this.defaultColor
      var left = indent //首行缩进距离
      for (var i = 0; i < chr.length; i++) {
        if (chr[i] == ' ') {
          b += Math.ceil(context.measureText(chr[i]).width)
        } else if (chr[i] == '\n') {
          b = 0
          temp = ''
          j++
          left = indent
          continue
        } else {
          temp += chr[i]
        }
        var tempWidth = Math.ceil(context.measureText(temp).width)
        var chrWidth = Math.ceil(context.measureText(chr[i]).width)
        if (tempWidth <= width - x - right - b - left) {
          tempWidth = tempWidth - chrWidth
        } else {
          b = 0
          temp = chr[i]
          tempWidth = 0
          left = 0
          j++
        }
        if (chr[i] == ' ') {
          continue
        } else {
          arr.push({
            text: chr[i], // 文字内容
            x: x + tempWidth + b + left, //x坐标
            y: t + j * h, //y坐标
            w: chrWidth, //宽度
            h: h, // 高度
            color: color,
            font: font
          })
          this.maxHeight = t + (j + 1) * h
        }
      }
      return j
    },

    // 画气泡框
    drawBox: function (h, img) {
      var width = this.canvas.width
      var ctx = this.context
      if (img) ctx.drawImage(img, this.wScale(0), this.hScale(10), this.wScale(img.width), this.hScale(img.height))
      ctx.strokeStyle = '#EBEBEB'
      ctx.beginPath()
      // ctx.moveTo(this.wScale(37), this.hScale(26))
      // ctx.lineTo(this.wScale(34), this.hScale(23))
      // ctx.lineTo(this.wScale(37), this.hScale(20))
      // ctx.lineTo(this.wScale(37), this.hScale(10))
      // ctx.moveTo(this.wScale(24), this.hScale(10))
      ctx.arc(this.wScale(32), this.hScale(6), this.wScale(4), Math.PI, Math.PI * 3 / 2)
      ctx.lineTo(width - this.wScale(4), this.hScale(0))
      ctx.arc(width - this.wScale(4), this.hScale(6), this.wScale(4), Math.PI * 3 / 2, 0)
      ctx.lineTo(width - this.wScale(0), h - this.wScale(4))
      ctx.arc(width - this.wScale(4), h - this.wScale(4), this.wScale(4), 0, Math.PI / 2)
      ctx.lineTo(this.wScale(32), h + this.wScale(0))
      ctx.arc(this.wScale(32), h - this.wScale(4), this.wScale(4), Math.PI / 2, Math.PI)
      ctx.lineTo(this.wScale(28), this.hScale(4))
      ctx.stroke()
      ctx.fillStyle = this.bgColor
      ctx.fill()
    },

    // 宽比例
    wScale: function (w) {
      return Math.floor(w * this.rate)
    },
    // 高比例
    hScale: function (h) {
      return Math.floor(h * this.rate)
    },

    //插入字符
    appendText: function (obj) {
      var arr = []
      var j = 0
      var t = this.maxHeight
      j = this._dealChr(arr, obj, t, j)
      this.arr.push(arr)
      for (var j = 0; j < arr.length; j++) {
        this.context.font = arr[j].font
        this.context.fillStyle = arr[j].color
        this.context.fillText(arr[j].text, arr[j].x, arr[j].y)
      }

    },
    //canvas 转图片内部使用
    _translateCanvas: function (position) {
      if (this.sdkInstance) {
        var dy = this.canvas.height > this.imgCanvas.height
          ? this.canvas.height - this.imgCanvas.height
          : 0
        var dataImg = this.context.getImageData(0, position === 'bottom'
          ? dy
          : 0, this.canvas.width, this.canvas.height)
        this.imgCanvasContext.putImageData(dataImg, 0, 0, 0, 0, this.imgCanvas.width, this.imgCanvas.height)
        var data = this.imgCanvas.toDataURL(this.imageType, this.quality)
        var str = data.substring(data.indexOf(',') + 1)
        this._emit('base64Img', str)
      }
    },

    dataURItoBlob: function (base64Data) {
      var byteString
      if (base64Data.split(',')[0].indexOf('base64') >= 0)
        byteString = atob(base64Data.split(',')[1])//base64 解码
      else {
        byteString = unescape(base64Data.split(',')[1])
      }
      var mimeString = base64Data.split(',')[0].split(':')[1].split(';')[0]//mime类型 -- image/png
      var ia = new Uint8Array(byteString.length)//创建视图
      for (var i = 0; i < byteString.length; i++) {
        ia[i] = byteString.charCodeAt(i)
      }
      var blob = new Blob([ia], {
        type: mimeString
      })
      return blob
    },

    //通知外部
    translateCanvas: function () {
      var dataImg = this.context.getImageData(0, 0, this.canvas.width, this.canvas.height)
      this.imgCanvasContext.putImageData(dataImg, 0, 0, 0, 0, this.imgCanvas.width, this.imgCanvas.height)
      var img = this.imgCanvas.toDataURL(this.imageType, this.quality)

      console.log('translateCanvas')
      this._emit('base64Img', img)
    },

    //处理标点，连续标点跑马灯只跑1个
    _dealDot: function () {
      var context = this.context
      var _self = this
      var arr = _self.arr[_self.arrIndex]
      if (!arr[_self.index]) return
      if (!regDot.test(arr[_self.index].text)) {
        return
      }
      var index = -1
      for (var i = _self.index; i < arr.length; i++) {
        var obj = arr[i]
        var text = obj.text
        var flag = regDot.test(text)
        if (flag) {
          obj.color = _self.color
          context.clearRect(obj.x, obj.y - 1, obj.w, obj.h) //橡皮擦高度-1个像素，有些文字会超出基线
          context.fillStyle = _self.bgColor
          context.fillRect(obj.x, obj.y - 1, obj.w, obj.h)
          context.fillStyle = obj.color
          context.font = obj.font
          context.fillText(obj.text, obj.x, obj.y)
          index = i
        } else {
          index = i - 1
          break
        }
      }
      this.index = index
    },

    //换行重绘
    _reDraw: function () {
      this.context.clearRect(0, this.scrollHeight, this.canvas.width, this.canvas.height) //清空画布
      this.context.fillStyle = 'white'
      this.context.fillRect(0, this.scrollHeight, this.canvas.width, this.canvas.height)
      if (this.img) this.drawBox(this.maxHeight, this.img)
      for (var i = 0; i < this.arr.length; i++) {
        var arr = this.arr[i]
        for (var j = 0; j < arr.length; j++) {
          var obj = arr[j]
          if (obj.y + obj.h <= this.scrollHeight + this.top) {
            continue
          }
          this.context.font = obj.font
          this.context.fillStyle = obj.color
          this.context.fillText(obj.text, obj.x, obj.y)
        }
      }
    },

    //覆盖之前的
    _coverDraw: function (arrIndex, textIndex) {
      if (arrIndex > this.arrIndex) {
        arrIndex = arrIndex < this.arr.length
          ? arrIndex
          : this.arr.length
        for (var i = this.startArryIndex; i < arrIndex; i++) {
          var arr = this.arr[i]
          for (var j = 0; j < arr.length; j++) {
            arr[j].color = this.color
          }
        }
        var thisArr = this.arr[this.arrIndex]
        var lastArr = this.arr[arrIndex]
        var thisHeight = thisArr[thisArr.length - 1].y
        var lastHeight = 0
        if (lastArr) {
          lastHeight = lastArr[0].y
        } else {
          lastArr = this.arr[this.arr.length - 1]
          lastHeight = lastArr[lastArr.length - 1].y
        }
        var scrollHeight = this.scrollHeight
        while (thisHeight < lastHeight) {
          thisHeight += this.height
          if (this.maxHeight - thisHeight + this.scrollLine * this.height >= this.imgCanvas.height && thisHeight >= this.scrollLine * this.height) {
            scrollHeight += this.height
            this.context.translate(0, -this.height)
            this.prevHeight = lastHeight
          }
        }
        if (scrollHeight != this.scrollHeight) {
          this.scrollHeight = scrollHeight
        }
        this._reDraw()
        this.arrIndex = arrIndex
        this.index = 0
      }
    },

    //重置参数
    _resetParam: function () {
      this.scrollHeight = 0 //滚动高度
      this.index = 0//跑马灯下标
      this.arr = []
      this.arrIndex = 0
      this.prevHeight = null//当前字体高度
      if (this.inter) {
        clearInterval(this.inter)
      }
    },

    /**
     * @description 开始绘制tts跑马灯
     * @param arrIndex {number} tts任务下标
     * @param timer {number} tts绘制速度
     * @param textIndex {number} 当前任务绘制位置下标
     * @date 16/1/2024
     */
    play: function (arrIndex, timer, textIndex = 0) {
      console.log('tts还在动',timer)
      var _self = this
      var context = this.context
      var maxHeight = this.maxHeight
      var scrollLine = this.scrollLine
      var imgCanvasHeight = this.imgCanvas.height
      if (_self.index > textIndex) _self.index = textIndex
      if (this.inter) {
        clearInterval(this.inter)
      }
      this._coverDraw(arrIndex, textIndex)
      var arr = this.arr[this.arrIndex]
      if (!arr) return
      this.inter = setInterval(function () {
        if (_self.index == arr.length) {
          clearInterval(_self.inter)
        } else {
          _self._dealDot()
          var obj = arr[_self.index]
          if (!obj) {
            clearInterval(_self.inter)
            return
          }
          if (maxHeight - obj.y + scrollLine * obj.h >= imgCanvasHeight && obj.y >= scrollLine * obj.h) {
            if (_self.prevHeight && _self.prevHeight == obj.y) {

            } else {
              _self.scrollHeight += obj.h
              context.translate(0, -obj.h)
              _self.prevHeight = obj.y
              _self._reDraw()
            }
          }
          obj.color = _self.color
          context.clearRect(obj.x, obj.y - 1, obj.w, obj.h) //橡皮擦高度-1个像素，有些文字会超出基线
          context.fillStyle = _self.bgColor
          context.fillRect(obj.x, obj.y - 1, obj.w, obj.h)
          context.fillStyle = obj.color
          context.font = obj.font
          context.fillText(obj.text, obj.x, obj.y)
          if (_self.canvas2Img) {

            _self._translateCanvas()
          }
          _self.index++
        }
      }, timer)
      return this.returnTextIndex()
    },

    returnTextIndex: function () {
      return this.index
    },


    //跑马灯暂停
    pause: function () {
      if (this.inter) {
        clearInterval(this.inter)
      }
    },

    playCover: function () {
      this.time = new Date().getTime()
      this.inter && clearInterval(this.inter)
      // this._coverDraw(this.arrIndex + 1)
      if (this.canvas2Img) {
        this._translateCanvas()
      }
    },

    on: function (name, fn) {
      var obj = this
      if (!obj[name]) {
        obj[name] = []
      }
      obj[name].push(fn)
    },
    _emit: function (name, val) {
      var obj = this
      if (obj[name]) {
        obj[name].forEach(function (fn) {
          fn(val)
        })
      }
    },
    _off: function (name, fn) {
      var obj = this
      if (obj[name]) {
        if (fn) {
          var index = obj[name].indexOf(fn)
          if (index > -1) {
            obj[name].splice(index, 1)
          }
        } else {
          obj[name].length = 0
        }
      }
    },
    destroy: function () {
      if (this.inter) {
        clearInterval(this.inter)
      }
    }
  }
  // 将插件对象暴露给全局对象（考虑兼容性）
  _global = (function () {
    return this || (0, eval)('this')
  }())
  if (typeof module !== 'undefined' && module.exports) {
    module.exports = HorseRaceLamp
  } else if (typeof define === 'function' && define.amd) {
    define(function () {
      return HorseRaceLamp
    })
  } else {
    !('HorseRaceLamp' in _global) && (_global.HorseRaceLamp = HorseRaceLamp)
  }
}())
