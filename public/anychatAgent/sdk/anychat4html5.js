(function(){var __webpack_modules__={318:function(e){e.exports=function(e){return e&&e.__esModule?e:{default:e}},e.exports.default=e.exports,e.exports.__esModule=!0},8:function(e){function t(n){return"function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?(e.exports=t=function(e){return typeof e},e.exports.default=e.exports,e.exports.__esModule=!0):(e.exports=t=function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},e.exports.default=e.exports,e.exports.__esModule=!0),t(n)}e.exports=t,e.exports.default=e.exports,e.exports.__esModule=!0},872:function(e,t,n){"use strict";var r=n(171),o=n(470),s=n(631),a=n(682),i=n(974),_=n(762),R=n(37),c=n(445).AnyChatBase64,E={},d=null,A=[],u=n(760);function C(e){e.length>8192&&(e=e.slice(0,8192));var t={};if(t.optname=s.BRAC_SO_CORESDK_WRITELOG,t.optval="[web] "+e,d){for(var n=0;n<A.length;n++)d.logOptimizeFlags?d.socket_send("request",0,"setsdkoptionstring",A[n]):u.post_sync("request",0,"setsdkoptionstring",A[n],d.sessionid);A=[],d.logOptimizeFlags?d.socket_send("request",0,"setsdkoptionstring",t):u.post_sync("request",0,"setsdkoptionstring",t,d.sessionid)}else A.push(t);var r=new Date,o=(r.getDate(),r.getDate(),r.getMonth(),r.getMonth(),r.getFullYear(),r.getHours()>9?r.getHours():"0"+r.getHours()),a=r.getMinutes()>9?r.getMinutes():"0"+r.getMinutes(),i=r.getSeconds()>9?r.getSeconds():"0"+r.getSeconds();if(r.getMilliseconds(),e='<p><span class="text-info">time:'+o+":"+a+":"+i+"</span>&nbsp;&nbsp;&nbsp;&nbsp;"+e+"</p>",document.getElementById("log")){var _=document.getElementById("log").innerHTML;document.getElementById("log").innerHTML=e+_}}t.instance=function(e){if(E.Connect)return E;d=new o.instance,R.setWay(d);var t="",n=0,O={},l=[],f=[],I=[],S=0,p=0,T=_(d),h=d.support();if(1==h)return console.error("The browser does not support WebSocket!"),!1;if(2==h)return console.error("The browser does not support canvas!"),!1;var m={},N={};function y(){C("ResetSource: reset source!"),d.cleanUserControlInfo(),d.videoCodecConfig=null,d.videoCodecApply=null,d.roomid=0,d.userList=[],d.docid=[],N.SetMediaInfo=[],d.hasStreamPlay=!1,d.offers_cache={},O={},d.removeAllVideo(),d.placeholderInfo=[],l=[],f=[],I=[]}function g(){return C("Logout()=0"),d.logout_flag=!0,d.socket_send("request",0,"logout",{}),y(),d.logOptimizeFlags=!1,d.websocket_id="",d.reconnect_flag=!0,d.cleanConnectInfo(),i.stop(),d.socket_instance&&d.socket_instance.close(),t="",d.socket_instance=null,d.inLinkingFlag=!1,d.userid=-1,R.clear_all(),A=[],d.cmdSpliceInfo={},d.executeConnect=!1,S=0,p=0,0}function v(){for(var e=[],t="0123456789ABCDEF",n=0;n<36;n++)e[n]=t.substr(Math.floor(16*Math.random()),1);return e[14]="4",e[19]=t.substr(3&e[19]|8,1),e[8]=e[13]=e[18]=e[23]="-",e.join("")}function L(e,t){if(e==d.userid&&(e=-1),N.SetMediaInfo[e])N.SetMediaInfo[e][t]||(n={id:""},N.SetMediaInfo[e][t]=n);else{N.SetMediaInfo[e]=new Array;var n={id:""};N.SetMediaInfo[e][t]=n}}N.SetMediaInfo=[],N.SetSDKOptionStringArr=[],N.SetSDKOptionIntArr=[],N.setMyvideoposData=[];var D={connect:s.WM_GV_CONNECT,login:s.WM_GV_LOGINSYSTEM,loginex:s.WM_GV_LOGINSYSTEM,enterroom:s.WM_GV_ENTERROOM,onlineuser:s.WM_GV_ONLINEUSER,linkclose:s.WM_GV_LINKCLOSE,useratroom:s.WM_GV_USERATROOM};function b(e,t){if(d.userid==e&&(e=-1),!O[e]||!O[e][t]){var n={};n.userid=e,n.videodata="",n.videodataindex=0,O[e]||(O[e]=new Array),O[e][t]=n}}return window.onbeforeunload=function(){C("onbeforeunload!"),g()},window.onpagehide=function(){C("onpagehide!"),g()},document.addEventListener("visibilitychange",(function(){"hidden"===document.visibilityState&&(C("brower hidden!"),d.browerVisibility=!1),"visible"===document.visibilityState&&(C("brower visible!"),d.browerVisibility=!0,d.system)})),function(){function e(){y(),d.logOptimizeFlags=!1,d.websocket_id="",d.reconnect_flag=!0,d.cleanConnectInfo(),i.stop(),d.socket_instance&&d.socket_instance.close(),d.socket_instance=null,d.inLinkingFlag=!1,d.userid=-1,R.clear_all(),A=[],d.cmdSpliceInfo={},d.reconnect_num=0,d.sendData={},S=0,p=0}d.on("reply",(function(e){var t=e.data,n=t.cmdcode,r=t.errorcode;if(D[n])switch(n){case"connect":if(T.clear_timer(),0==r){t.versioninfo&&C("serverVersionInfo:"+t.versioninfo);var o=null;t.serverfunction&&(o=parseInt(t.serverfunction)),o&&d.handleFunctionFlag(o),d.inLinkingFlag=!0;var a=d.getConnectUuidByState();C("Message OnConnect(errorcode=0,addr="+d.websocket_info[a].origin+")"),N.loginData&&(e={},(e=N.loginData).username&&d.socket_send("request",0,"login",e),N.loginData=null),N.loginExData&&(e={},e=N.loginExData,d.socket_send("request",0,"loginex",e),N.loginExData=null),i.start(d)}D[n]&&m.OnNotifyMessage(D[n],"",r)}"heartbeat"===n&&(R.DelHeartbeatDate(),i.notify(r)),"queryuserstateint"===n&&0==t.errorcode&&(t.infoname==s.BRAC_USERSTATE_SPEAKVOLUME&&(l[t.userid]||(l[t.userid]=new Array),l[t.userid].push(t.infovalue)),t.infoname==s.BRAC_USERSTATE_VIDEOBITRATE&&(I[t.userid]||(I[t.userid]=new Array),I[t.userid].push(t.infovalue)),t.infoname==s.BRAC_USERSTATE_AUDIOBITRATE&&(f[t.userid]||(f[t.userid]=new Array),f[t.userid].push(t.infovalue)))})),d.on("request",(function(e){var t=e.data;"restrans"===t.cmdcode&&R.ReSendDataPackToServer(t.begin_reg)})),d.on("notify",(function(t){var r=t.data,o=r.cmdcode,a=r.errorcode;if("notifymessage"===o){switch(r.notifymsg){case s.WM_GV_LOGINSYSTEM:if(0!=a)return C("OnLoginSystem(errorcode="+a+")"),S=s.WM_GV_LOGINSYSTEM,p=r.lparam||a,e(),!1;d.userid=r.wparam,d.addUserControlInfo(-1),C("OnLoginSystem(userid="+r.wparam+",errorcode="+a+")"),C(window.AnyChatSDKVersion),C("userAgent:"+navigator.userAgent);break;case s.WM_GV_ENTERROOM:0==a&&(d.roomid=r.wparam,N.SetMediaInfo[-1]||(N.SetMediaInfo[-1]=new Array)),C("Message OnEnterRoom(roomid:"+r.wparam+",errorcode:"+a+")");break;case s.WM_GV_LINKCLOSE:C("Message OnLinkClose"),e()}r.notifymsg!=s.WM_GV_CONNECT||0==r.lparam&&0==a?r.notifymsg!=s.WM_GV_CONNECT&&r.notifymsg&&m.OnNotifyMessage(r.notifymsg,r.wparam,r.lparam||a):(r.notifymsg=s.WM_GV_LOGINSYSTEM,C("OnLoginSystem(errorcode= "+(r.lparam||a)+")"),S=s.WM_GV_LOGINSYSTEM,p=r.lparam||a,e(),r.notifymsg&&m.OnNotifyMessage(r.notifymsg,r.wparam,r.lparam||a))}if("videodata"===o&&0==a&&d.roomid){var i=r.userid;if(r.userid==d.userid&&(i=-1),r.streamindex)_=r.streamindex;else var _=0;if(O[i]&&!O[i][_]?b(i,_):O[i]||b(i,_),!(E=r.jsonbuf))return void C("Event videodata: jsonbuf is empty!");if(1==E.index&&(O[i][_].videodata="",O[i][_].videodataindex=0),O[i][_].videodataindex+1!=E.index)return void C("Event videodata: content is not intact! preindex:"+O[i][_].videodataindex+", recindex:"+E.index);if(O[i][_].videodata+=E.content,O[i][_].videodataindex=E.index,E.flag){if(0==i){if(d.placeholderInfo.length>0)for(var R=0;R<d.placeholderInfo.length;R++)d.placeholderInfo[R]&&d.placeholderInfo[R].id&&d.placeholderInfo[R].parentobj&&d.canvasVideo(i,O[i][_].videodata,d.placeholderInfo[R].id,d.placeholderInfo[R].parentobj,_)}else N.SetMediaInfo[i]&&N.SetMediaInfo[i][_]&&N.SetMediaInfo[i][_].id&&N.SetMediaInfo[i][_].parentobj&&d.canvasVideo(i,O[i][_].videodata,N.SetMediaInfo[i][_].id,N.SetMediaInfo[i][_].parentobj,_);O[i][_].videodata="",O[i][_].videodataindex=0}}if("textmessage"===o){var E=r.msgbuf;try{m.OnAnyChatTextMessage(r.fromuserid,r.touserid,r.bsecret,E,0)}catch(e){m.OnAnyChatTextMessage&&n<10&&(n++,C("OnAnyChatTextMessage catch:"+"".concat(e.name,": ").concat(e.message)),console.error(e))}}if("objectevent"===o)try{m.OnAnyChatObjectEvent(r.objecttype,r.objectid,r.eventtype,r.param1,r.param2,r.param3,r.param4,JSON.parse(r.jsonbuf).param1)}catch(e){m.OnAnyChatObjectEvent&&n<10&&(n++,C("OnAnyChatObjectEvent catch:"+"".concat(e.name,": ").concat(e.message)),console.error(e))}if("videocallevent"===o){C("Message OnAnyChatVideoCallEvent("+r.eventtype+","+r.userid+","+r.errorcode+","+r.flags+","+r.param+")");try{m.OnAnyChatVideoCallEvent(r.eventtype,r.userid,r.errorcode,r.flags,r.param,r.jsonbuf)}catch(e){m.OnAnyChatVideoCallEvent&&n<10&&(n++,C("OnAnyChatVideoCallEvent catch:"+"".concat(e.name,": ").concat(e.message)),console.error(e))}}if("transbuffer"===o)if(d.new_transbuf_flag){if(d.userControlInfo[r.userid]||d.addUserControlInfo(r.userid),!(E=JSON.parse(r.jsonbuf)))return void C("Event transbuffer: jsonbuf is empty!");if(1==E.index&&(d.userControlInfo[r.userid].transcontent="",d.userControlInfo[r.userid].transcontentindex=0),d.userControlInfo[r.userid].transcontentindex+1!=E.index)return void C("Event transbuffer: content is not intact! preindex:"+d.userControlInfo[r.userid].transcontentindex+", recindex:"+E.index);if(d.userControlInfo[r.userid].transcontent+=E.content,d.userControlInfo[r.userid].transcontentindex=E.index,E.flag){var A=c.decode(d.userControlInfo[r.userid].transcontent);d.userControlInfo[r.userid].transcontent="",d.userControlInfo[r.userid].transcontentindex=0;try{m.OnAnyChatTransBuffer(r.userid,A,A.length)}catch(e){m.OnAnyChatTransBuffer&&n<10&&(n++,C("OnAnyChatTransBuffer catch:"+"".concat(e.name,": ").concat(e.message)),console.error(e))}}}else try{m.OnAnyChatTransBuffer(r.userid,r.jsonbuf,r.jsonbuf.length)}catch(e){m.OnAnyChatTransBuffer&&n<10&&(n++,C("OnAnyChatTransBuffer catch:"+"".concat(e.name,": ").concat(e.message)),console.error(e))}if("transbufferex"===o)if(d.new_transbuf_flag){if(d.userControlInfo[r.userid]||d.addUserControlInfo(r.userid),!(E=JSON.parse(r.jsonbuf)))return void C("Event transbufferex: jsonbuf is empty!");if(1==E.index&&(d.userControlInfo[r.userid].transcontent="",d.userControlInfo[r.userid].transcontentindex=0),d.userControlInfo[r.userid].transcontentindex+1!=E.index)return void C("Event transbufferex: content is not intact! preindex:"+d.userControlInfo[r.userid].transcontentindex+", recindex:"+E.index);if(d.userControlInfo[r.userid].transcontent+=E.content,d.userControlInfo[r.userid].transcontentindex=E.index,E.flag){A=c.decode(d.userControlInfo[r.userid].transcontent),d.userControlInfo[r.userid].transcontent="",d.userControlInfo[r.userid].transcontentindex=0;try{m.OnAnyChatTransBufferex(r.userid,A,A.length,r.param1,r.param2,r.taskid)}catch(e){m.OnAnyChatTransBufferex&&n<10&&(n++,C("OnAnyChatTransBufferex catch:"+"".concat(e.name,": ").concat(e.message)),console.error(e))}}}else try{m.OnAnyChatTransBufferex(r.userid,r.jsonbuf,r.jsonbuf.length,r.param1,r.param2,r.taskid)}catch(e){m.OnAnyChatTransBufferex&&n<10&&(n++,C("OnAnyChatTransBufferex catch:"+"".concat(e.name,": ").concat(e.message)),console.error(e))}if("coresdkevent"===o){if((E=JSON.parse(r.jsonbuf))&&E.spliceflag){if(!d.cmdSpliceInfo[E.uuid]){var u={content:"",index:0};d.cmdSpliceInfo[E.uuid]=u}if(d.cmdSpliceInfo[E.uuid].index+1!=E.index)return void C("Event coresdkevent: content is not intact! uuid:"+E.uuid+", preindex:"+d.cmdSpliceInfo[E.uuid].index+", recindex:"+E.index);if(d.cmdSpliceInfo[E.uuid].content+=E.content,d.cmdSpliceInfo[E.uuid].index=E.index,!E.flag)return;r.jsonbuf=c.decode(d.cmdSpliceInfo[E.uuid].content),delete d.cmdSpliceInfo[E.uuid]}try{if(1==r.eventtype){if((E=JSON.parse(r.jsonbuf)).message==s.WM_GV_CONNECT&&0!=E.param2)E.message=s.WM_GV_LOGINSYSTEM;else if(E.message==s.WM_GV_CONNECT&&0==E.param2)return;r.jsonbuf=JSON.stringify(E)}}catch(e){}try{m.OnAnyChatCoreSDKEvent(r.eventtype,r.jsonbuf,r.errorcode)}catch(e){m.OnAnyChatCoreSDKEvent&&n<10&&(n++,C("OnAnyChatCoreSDKEvent catch:"+"".concat(e.name,": ").concat(e.message)),console.error(e))}}if("transfile"===o){C("Message OnAnyChatTransFile("+r.userid+","+r.filename+","+r.tempfilepath+","+r.filelength+","+r.wparam+","+r.lparam+","+r.taskid+")");try{m.OnAnyChatTransFile(r.userid,r.filename,r.tempfilepath,r.filelength,r.wparam,r.lparam,r.taskid)}catch(e){m.OnAnyChatTransFile&&n<10&&(n++,C("OnAnyChatTransFile catch:"+"".concat(e.name,": ").concat(e.message)),console.error(e))}}if("volumechange"===o){C("Message OnAnyChatVolumeChange("+r.device+","+r.currentvolume+")");try{m.OnAnyChatVolumeChange(r.device,r.currentvolume)}catch(e){m.OnAnyChatVolumeChange&&n<10&&(n++,C("OnAnyChatVolumeChange catch:"+"".concat(e.name,": ").concat(e.message)),console.error(e))}}if("sdkfilterdata"===o){C("Message OnAnyChatSDKFilterData("+r.buf+","+r.len+")");try{m.OnAnyChatSDKFilterData(r.buf,r.len)}catch(e){m.OnAnyChatSDKFilterData&&n<10&&(n++,C("OnAnyChatSDKFilterData catch:"+"".concat(e.name,": ").concat(e.message)),console.error(e))}}if("streamrecordctrlex"===o){C("Message OnAnyChatRecordSnapShotEx2("+r.userid+","+r.errorcode+","+r.filename+","+r.elapse+","+r.flags+","+r.param+")");try{var l=s.BRAC_RECORD_FLAGS_SNAPSHOT&r.flags?0:1;m.OnAnyChatRecordSnapShot(r.userid,r.filename,r.param,l)}catch(e){m.OnAnyChatRecordSnapShot&&n<10&&(n++,C("OnAnyChatRecordSnapShot catch:"+"".concat(e.name,": ").concat(e.message)),console.error(e))}try{m.OnAnyChatRecordSnapShotEx(r.userid,r.filename,r.elapse,r.flags,r.param,r.userstr)}catch(e){m.OnAnyChatRecordSnapShotEx&&n<10&&(n++,C("OnAnyChatRecordSnapShotEx catch:"+"".concat(e.name,": ").concat(e.message)),console.error(e))}try{m.OnAnyChatRecordSnapShotEx2(r.userid,r.errorcode,r.filename,r.elapse,r.flags,r.param,r.userstr)}catch(e){m.OnAnyChatRecordSnapShotEx2&&n<10&&(n++,C("OnAnyChatRecordSnapShotEx2 catch:"+"".concat(e.name,": ").concat(e.message)),console.error(e))}}if("filebase64control"===o){var f=JSON.parse(r.result);if(d.filebase64json[f.uuid]||(u={content:"",index:0},d.filebase64json[f.uuid]=u),d.filebase64json[f.uuid].index+1!=f.index)return void C("Event filebase64control: content is not intact! uuid:"+f.uuid+", preindex:"+d.filebase64json[f.uuid].index+", recindex:"+f.index);if(d.filebase64json[f.uuid].content+=f.content,d.filebase64json[f.uuid].index=f.index,!f.flag)return;r.result=d.filebase64json[f.uuid].content,delete d.filebase64json[f.uuid];try{m.OnAnyChatFileBase64ControlEvent(r.ctrlcode,r.result,r.inparam,r.errorcode)}catch(e){m.OnAnyChatFileBase64ControlEvent&&n<10&&(n++,C("OnAnyChatFileBase64ControlEvent catch:"+"".concat(e.name,": ").concat(e.message)),console.error(e))}}})),d.on("socket_error",(function(t,n,r){if(d.inLinkingFlag)var o=s.WM_GV_LINKCLOSE;else o=s.WM_GV_CONNECT;e(),m.OnNotifyMessage(o,0,a.AC_ERROR_CONNECT_SOCKETERR)})),d.on("close_websocket",(function(t){var n=0,r=0;0!=S&&0!=p?(n=S,r=p):d.inLinkingFlag?(n=s.WM_GV_LINKCLOSE,r=a.AC_ERROR_CONNECT_SOCKETERR):(n=s.WM_GV_CONNECT,r=a.AC_ERROR_CONNECT_SOCKETERR),e(),m.OnNotifyMessage(n,0,r)})),d.on("dblclick",(function(e,t,n){var r=document.getElementById(e).getAttribute("name");if(r){t=r.split("_")[0],n=r.split("_")[1],-1==t&&(t=d.userid);var o={};o.userid=t,o.streamindex=n,o.flags=1,u.post_sync("request",0,"dblclick",o,d.sessionid)}})),d.on("ack",(function(e){R.DelDataPack(e.seq)})),d.on("log",(function(e){C(e)}))}(),E.InitSDK=function(e){var t,n=-1,r={};if(void 0===e&&(e="tahcyna"),r.param=e,d.sessionid||(d.sessionid=v()),t=u.post_sync("request",0,"initsdk",r,d.sessionid),0===parseInt(t)){var o=setInterval((function(){u.post_sync("request",0,"httpheartbeat",{},d.sessionid)}),3e4);d.httpheartbeat=o,n=0}else if(""!==t&&void 0!==t){o=setInterval((function(){u.post_sync("request",0,"httpheartbeat",{},d.sessionid)}),3e4),d.httpheartbeat=o;try{var s=JSON.parse(t);if(void 0!==s.httpport&&""!==s.httpport){var a="http://127.0.0.1:"+s.httpport+"/sdk";u.resetparam("httpurl",a),d.workhttpurl=a}void 0!==s.wsport&&""!==s.wsport&&(d.wsport=s.wsport,d.urlprotocol_addr="127.0.0.1:"+s.wsport),n=0,void 0!==s.errorcode&&""!==s.errorcode&&(n=s.errorcode)}catch(e){C("initsdk e:"+JSON.stringify(e)),n=-1}}return n},E.SetServerAuthPass=function(e){return t=e,0},E.Connect=function(e,t){return d.socket_instance?a.AC_ERROR_ALREADY_LOGIN:(R.clear_all(),T.clear_timer(),d.reconnect_flag=!1,d.logout_flag=!1,d.handleConnectAddrInfo(d.urlprotocol_addr),d.lpHost=e,d.dwPort=t,C("Connect("+e+","+t+")=0"),0)},E.Login=function(e,n,r){var o={};return o.username=e,o.password=n,o.passenctype=r,o.jsonbuf={serverauthpass:t},d.inLinkingFlag?o.username&&d.socket_send("request",0,"login",o):N.loginData=o,C("Login(name:"+e+",password:"+n+",param:"+r+")=0"),0},E.LoginEx=function(e,n,r,o,s,a,i){var _={};return _.nickname=e,_.userid=n,_.struserid=r,_.appid=o,_.timestamp=s,_.sigstr=a,_.strparam=i,_.jsonbuf={serverauthpass:t},d.inLinkingFlag?d.socket_send("request",0,"loginex",_):N.loginExData=_,i.length>50?C("LoginEx(name:"+e+",userid:"+n+", SigStr:"+a+",param len:"+i.length+")=0"):C("LoginEx(name:"+e+",userid:"+n+", SigStr:"+a+",param:"+i+")=0"),0},E.EnterRoom=function(e,t,n){var r,o={};return o.roomid=e,o.roompass=t,o.passenctype=n,C("EnterRoom(roomid:"+e+",password:"+t+",param:"+n+")="+(r=u.post_sync("request",0,"enterroom",o,d.sessionid))),parseInt(r)},E.EnterRoomEx=function(e,t){var n,r={};return null==e&&(e=""),null==t&&(t=""),r.roomname=e+"",r.roompass=t+"",r.passenctype="",C("EnterRoomEx(roomname:"+e+",password:"+t+")="+(n=u.post_sync("request",0,"enterroomex",r,d.sessionid))),parseInt(n)},E.LeaveRoom=function(e){var t,n={};return d.roomid=0,n.roomid=e||-1,t=u.post_sync("request",0,"leaveroom",n,d.sessionid),y(),C("leaveroom(roomid:"+n.roomid+")="+t),parseInt(t)},E.Logout=g,E.on=function(e,t){switch(e){case"OnNotifyMessage":m.OnNotifyMessage=t;break;case"OnTextMessage":m.OnAnyChatTextMessage=t;break;case"OnTransBuffer":m.OnAnyChatTransBuffer=t;break;case"OnTransBufferex":m.OnAnyChatTransBufferex=t;break;case"OnTransFile":m.OnAnyChatTransFile=t;break;case"OnVolumeChange":m.OnAnyChatVolumeChange=t;break;case"OnSDKFilterData":m.OnAnyChatSDKFilterData=t;break;case"OnVideoCallEvent":m.OnAnyChatVideoCallEvent=t;break;case"OnRecordSnapShot":m.OnAnyChatRecordSnapShot=t;break;case"OnRecordSnapShotEx":m.OnAnyChatRecordSnapShotEx=t;break;case"OnRecordSnapShotEx2":m.OnAnyChatRecordSnapShotEx2=t;break;case"OnObjectEvent":m.OnAnyChatObjectEvent=t;break;case"OnAnyChatCoreSDKEvent":m.OnAnyChatCoreSDKEvent=t;break;case"OnAnyChatTransFileEx":m.OnAnyChatTransFileEx=t;break;case"OnAnyChatExeStatus":m.OnAnyChatExeStatus=t;break;case"OnAnyChatStreamPlayInit":m.OnAnyChatStreamPlayInit=t;break;case"OnAnyChatFileBase64ControlEvent":m.OnAnyChatFileBase64ControlEvent=t}},E.UserCameraControl=function(e,t){var n,r={};return r.userid=e,r.open=t,0==(n=u.post_sync("request",0,"usercameracontrol",r,d.sessionid))&&(0==t?-1==e||e==d.userid?(e=-1,N.SetMediaInfo[e]&&N.SetMediaInfo[e][0]&&N.SetMediaInfo[e][0].id):N.SetMediaInfo[e]&&N.SetMediaInfo[e][0]&&N.SetMediaInfo[e][0].id:1==t&&L(e,0)),C("UserCameraControl(userId:"+e+",open:"+t+")="+n),parseInt(n)},E.UserCameraControlEx=function(e,t,n,r,o){var s,a={};return a.userid=e,a.open=t,a.streamindex=n,a.flags=r,a.strparam=o,0==(s=u.post_sync("request",0,"usercameracontrolex",a,d.sessionid))&&(0==t?-1==e||e==d.userid?(e=-1,N.SetMediaInfo[e]&&N.SetMediaInfo[e][n]&&N.SetMediaInfo[e][n].id):N.SetMediaInfo[e]&&N.SetMediaInfo[e][n]&&N.SetMediaInfo[e][n].id:1==t&&L(e,n)),C("UserCameraControlEx(userId:"+e+",open:"+t+",streamIndex:"+n+",flag:"+r+",param:"+o+")="+s),parseInt(s)},E.UserSpeakControl=function(e,t){var n,r={};return r.userid=e,r.open=t,C("UserSpeakControl(userId:"+e+",open:"+t+")="+(n=u.post_sync("request",0,"userspeakcontrol",r,d.sessionid))),parseInt(n)},E.UserSpeakControlEx=function(e,t,n,r,o){var s,a={};return a.userid=e,a.open=t,a.streamindex=n,a.flags=r,a.strparam=o,C("UserSpeakControlEx(userId:"+e+",open:"+t+",streamIndex:"+n+",flag:"+r+",param:"+o+")="+(s=u.post_sync("request",0,"userspeakcontrolex",a,d.sessionid))),parseInt(s)},E.EnumDevices=function(e){var t=[],n=-1,r={};r.devicetype=e;try{t=u.post_sync("request",0,"enumdevices",r,d.sessionid),t=JSON.parse(t).array}catch(e){t=u.post_sync("request",0,"enumdevices",r,d.sessionid)}return"[]"!=typeof t&&(n=0),C("EnumDevices(DeviceType:"+e+")="+n),t},E.SetVideoPos=function(e,t,n){if(C("SetVideoPos(userId:"+e+",id:"+n+")"),document.getElementById(n)&&"canvas"!=document.getElementById(n).tagName.toLocaleLowerCase())return C("SetVideoPos: The same already exists id form this page"),-1;if(0==e){var r=!1;if(d.placeholderInfo.length>0)for(var o=0;o<d.placeholderInfo.length;o++)d.placeholderInfo[o]&&d.placeholderInfo[o].parentobj==t&&d.placeholderInfo[o].id==n&&(r=!0);if(r)return C("SetVideoPos: The same information for userid of 0"),-1;var s;if(s=u.post_sync("request",0,"setvideopos",{userid:0,streamindex:0},d.sessionid),0==parseInt(s)){var a={};a.parentobj=t,a.id=n,d.placeholderInfo.push(a)}return parseInt(s)}if(e==d.userid&&(e=-1),N.SetMediaInfo[e]&&N.SetMediaInfo[e][0]&&N.SetMediaInfo[e][0].id)if(N.SetMediaInfo[e][0].parentobj==t&&N.SetMediaInfo[e][0].id==n){if(document.getElementById(n))return C("SetVideoPos: Repeat invoked!"),-1;C("SetVideoPos: change video between the same parent node!"),delete d.docid[N.SetMediaInfo[e][0].id]}else document.getElementById(N.SetMediaInfo[e][0].id)?(C("SetVideoPos: remove the already exists video"),-1==e?d.removeLocalVideo(N.SetMediaInfo[e][0].id,!1):d.removeRemoteVideo(N.SetMediaInfo[e][0].id,!1)):(C("SetVideoPos: change video between the different parent node!"),delete d.docid[N.SetMediaInfo[e][0].id]);if(e==d.userid&&(e=-1),N.SetMediaInfo[e]&&N.SetMediaInfo[e][0])N.SetMediaInfo[e][0].parentobj=t,N.SetMediaInfo[e][0].id=n;else try{var i={};i.parentobj=t,i.id=n,N.SetMediaInfo[e]||(N.SetMediaInfo[e]=new Array),N.SetMediaInfo[e][0]=i}catch(e){C("SetVideoPos err:"+e)}if(d.placeholderInfo.length>0)for(o=0;o<d.placeholderInfo.length;o++)d.placeholderInfo[o]&&d.placeholderInfo[o].parentobj==t&&d.placeholderInfo[o].id==n&&delete d.placeholderInfo[o];return 0},E.SetVideoPosEx=function(e,t,n,r){if(C("SetVideoPosEx(userId:"+e+",id:"+n+",streamindex:"+r+")"),r||(r=0),document.getElementById(n)&&"canvas"!=document.getElementById(n).tagName.toLocaleLowerCase())return C("SetVideoPosEx: The same already exists id form this page"),-1;if(0==e){var o=!1;if(d.placeholderInfo.length>0)for(var s=0;s<d.placeholderInfo.length;s++)d.placeholderInfo[s]&&d.placeholderInfo[s].parentobj==t&&d.placeholderInfo[s].id==n&&(o=!0);if(o)return C("SetVideoPosEx: The same information for userid of 0"),-1;var a;if(a=u.post_sync("request",0,"setvideopos",{userid:0,streamindex:0},d.sessionid),0==parseInt(a)){var i={};i.parentobj=t,i.id=n,d.placeholderInfo.push(i)}return parseInt(a)}if(e==d.userid&&(e=-1),N.SetMediaInfo[e]&&N.SetMediaInfo[e][r]&&N.SetMediaInfo[e][r].id)if(N.SetMediaInfo[e][r].parentobj==t&&N.SetMediaInfo[e][r].id==n){if(document.getElementById(n))return C("SetVideoPosEx: Repeat invoked!"),-1;C("SetVideoPosEx: change video between the same parent node!"),delete d.docid[N.SetMediaInfo[e][r].id]}else document.getElementById(N.SetMediaInfo[e][r].id)?(C("SetVideoPosEx: remove the already exists video"),-1==e?d.removeLocalVideo(N.SetMediaInfo[e][r].id,!1):d.removeRemoteVideo(N.SetMediaInfo[e][r].id,!1)):(C("SetVideoPosEx: change video between the different parent node!"),delete d.docid[N.SetMediaInfo[e][r].id]);if(N.SetMediaInfo[e]&&N.SetMediaInfo[e][r])N.SetMediaInfo[e][r].parentobj=t,N.SetMediaInfo[e][r].id=n;else try{var _={};_.parentobj=t,_.id=n,N.SetMediaInfo[e]||(N.SetMediaInfo[e]=new Array),N.SetMediaInfo[e][r]=_}catch(e){C("SetVideoPosEx urlprotocolmode err:"+e)}if(d.placeholderInfo.length>0)for(s=0;s<d.placeholderInfo.length;s++)d.placeholderInfo[s]&&d.placeholderInfo[s].parentobj==t&&d.placeholderInfo[s].id==n&&delete d.placeholderInfo[s];return 0},E.SetUserStreamInfoString=function(e,t,n,r){var o;e==d.userid&&(e=-1);var s={};return s.userid=e,s.streamindex=t,s.infoname=n,s.value=r,C("SetUserStreamInfoString(userId:"+e+",streamIndex:"+t+",infoname:"+n+",value:"+r+")="+(o=u.post_sync("request",0,"setuserstreaminfostring",s,d.sessionid))),parseInt(o)},E.SetUserStreamInfoInt=function(e,t,n,r){var o,s={};return s.userid=e,s.streamindex=t,s.infoname=n,s.value=r,C("SetUserStreamInfoInt(userId:"+e+",streamIndex:"+t+",infoname:"+n+",value:"+r+")="+(o=u.post_sync("request",0,"setuserstreaminfoint",s,d.sessionid))),parseInt(o)},E.SetSDKOptionString=function(e,t){var n=-1,r={};if(r.optname=e,r.optval=t,e==s.BRAC_SO_CORESDK_WRITELOG)return C("SetSDKOptionString:"+t),0;if(e==s.BRAC_SO_CLOUD_APPGUID)d.appGuid=t;else if(e==s.BRAC_SO_CORESDK_CHROMEPARAMS)try{var o=JSON.parse(t);return 1==Object.keys(o).length?"boolean"==typeof o.dblclickfullscreen?(d.dblclickfullscreen=o.dblclickfullscreen,0):21:"boolean"==typeof o.dblclickfullscreen?(n=u.post_sync("request",0,"setsdkoptionstring",r,d.sessionid),0===parseInt(n)&&(d.dblclickfullscreen=o.dblclickfullscreen),parseInt(n)):21}catch(e){C("invoke BRAC_SO_CORESDK_CHROMEPARAMS error: param:"+t)}return e==s.BRAC_SO_RECORD_INSERTIMAGE?(u.post_async("request",0,"setsdkoptionstring",r,d.sessionid),n=0):n=u.post_sync("request",0,"setsdkoptionstring",r,d.sessionid),s.BRAC_SO_VIDEOSHOW_CLIPMODE==e&&0==parseInt(n)&&(d.remoteVideoClipMode=t),s.BRAC_SO_LOCALVIDEO_CLIPMODE==e&&0==parseInt(n)&&(d.localVideoClipMode=t),parseInt(n)},E.SetSDKOptionInt=function(e,t){var n,r={};return r.optname=e,r.optval=t,n=u.post_sync("request",0,"setsdkoptionint",r,d.sessionid),s.BRAC_SO_CHROME_WORKMODE==e&&u.post_sync("request",0,"setsdkoptionint",r,d.sessionid,d.httpurl),s.BRAC_SO_VIDEOSHOW_CLIPMODE==e&&0==parseInt(n)&&(d.remoteVideoClipMode=t),s.BRAC_SO_LOCALVIDEO_CLIPMODE==e&&0==parseInt(n)&&(d.localVideoClipMode=t),parseInt(n)},E.VideoCallControl=function(e,t,n,r,o,s){var a,i={};return i.eventtype=e,i.userid=t,i.errorcode=n,i.flags=r,i.param=o,i.userstr=s,a=u.post_sync("request",0,"videocallcontrol",i,d.sessionid),parseInt(a)},E.SetObjectStringValue=function(e,t,n,r){var o,s={};return s.objecttype=e,s.objectid=t,s.infoname=n,s.value=r,o=u.post_sync("request",0,"setobjectstringvalue",s,d.sessionid),parseInt(o)},E.SetObjectIntValue=function(e,t,n,r){var o,s={};return s.objecttype=e,s.objectid=t,s.infoname=n,s.value=r,o=u.post_sync("request",0,"setobjectintvalue",s,d.sessionid),parseInt(o)},E.GetObjectIntValue=function(e,t,n){var r,o={};return o.objecttype=e,o.objectid=t,o.infoname=n,r=u.post_sync("request",0,"getobjectintvalue",o,d.sessionid),parseInt(r)},E.GetObjectStringValue=function(e,t,n){var r={};return r.objecttype=e,r.objectid=t,r.infoname=n,u.post_sync("request",0,"getobjectstringvalue",r,d.sessionid)},E.GetUserStreamInfoString=function(e,t,n){e==d.userid&&(e=-1);var r={};return r.userid=e,r.streamindex=t,r.infoname=n,u.post_sync("request",0,"getuserstreaminfostring",r,d.sessionid)},E.GetUserStreamInfoInt=function(e,t,n){var r;e==d.userid&&(e=-1);var o={};return o.userid=e,o.streamindex=t,o.infoname=n,r=u.post_sync("request",0,"getuserstreaminfoint",o,d.sessionid),parseInt(r)},E.ObjectControl=function(e,t,n,r,o,s,a,i){var _,R={};return R.objecttype=e,R.objectid=t,R.ctrlcode=n,R.param1=r,R.param2=o,R.param3=s,R.param4=a,R.strparam=i,_=u.post_sync("request",0,"objectcontrol",R,d.sessionid),parseInt(_)},E.ObjectGetIdList=function(e){var t=[],n={};n.objecttype=e;try{t=u.post_sync("request",0,"objectgetidlist",n,d.sessionid),t=JSON.parse(t).array}catch(e){t=u.post_sync("request",0,"objectgetidlist",n,d.sessionid)}return t},E.GetRoomOnlineUsers=function(e){var t=[],n={};n.roomid=e;try{t=u.post_sync("request",0,"getroomonlineusers",n,d.sessionid),t=JSON.parse(t).array}catch(e){t=u.post_sync("request",0,"getroomonlineusers",n,d.sessionid)}return t},E.SendTextMessage=function(e,t,n){var r;if(n.length>8e3)return a.AC_ERROR_TRANSFILE_TOOLARGE;var o={};return o.userid=e,o.secret=t,o.msgbuf=n,r=u.post_sync("request",0,"sendtextmessage",o,d.sessionid),parseInt(r)},E.GetSDKOptionString=function(e){var t={};return t.optname=e,e==s.BRAC_SO_CORESDK_NEWGUID?v():u.post_sync("request",0,"getsdkoptionstring",t,d.sessionid)},E.GetSDKOptionStringEx=function(e,t,n){if(e==s.BRAC_SO_LOCALPATH2URL&&""==t.replace(/^\s*|\s*$/g,""))return C("GetSDKOptionStringEx strValue is empty."),a.AC_ERROR_FUNCOPTERROR;var r={};return r.optname=e,r.strvalue=t,r.flags=n,u.post_sync("request",0,"getsdkoptionstringex",r,d.sessionid)},E.GetSDKOptionInt=function(e){var t,n={};return n.optname=e,t=u.post_sync("request",0,"getsdkoptionint",n,d.sessionid),parseInt(t)},E.TransBuffer=function(e,t){var n,r={};return r.userid=e,r.buf=t,C("TransBuffer(userid:"+e+")="+(n=u.post_sync("request",0,"transbuffer",r,d.sessionid))),parseInt(n)},E.TransBufferEx=function(e,t,n,r,o){var s,a={};return a.userid=e,a.buf=t,a.wparam=n,a.lparam=r,a.flags=o,C("TransBufferEx(userid:"+e+", wParam:"+n+", lParam:"+r+", dwFlags:"+o+") = taskid:"+(s=u.post_sync("request",0,"transbufferex",a,d.sessionid))),parseInt(s)},E.GetVersion=function(e){e-=0;var t={};return t.type=e,u.post_sync("request",0,"getversion",t,d.sessionid)},E.GetCurrentDevice=function(e){var t={};return t.devicetype=e,u.post_sync("request",0,"getcurrentdevice",t,d.sessionid)},E.QueryUserStateInt=function(e,t){var n=0;e==d.userid&&(e=-1);var r={};if(r.userid=e,r.infoname=t,t!=s.BRAC_USERSTATE_SPEAKVOLUME&&t!=s.BRAC_USERSTATE_VIDEOBITRATE&&t!=s.BRAC_USERSTATE_AUDIOBITRATE||!d.logOptimizeFlags)n=u.post_sync("request",0,"queryuserstateint",r,d.sessionid);else switch(d.socket_send("request",0,"queryuserstateint",r),t){case s.BRAC_USERSTATE_SPEAKVOLUME:l[e]&&l[e].length>0&&(n=l[e].shift());break;case s.BRAC_USERSTATE_VIDEOBITRATE:I[e]&&I[e].length>0&&(n=I[e].shift());break;case s.BRAC_USERSTATE_AUDIOBITRATE:f[e]&&f[e].length>0&&(n=f[e].shift())}return parseInt(n)},E.GetUserFriends=function(){var e=[],t={};try{e=u.post_sync("request",0,"getuserfriends",t,d.sessionid),e=JSON.parse(e).array}catch(n){e=u.post_sync("request",0,"getuserfriends",t,d.sessionid)}return e},E.GetGroupFriends=function(e){var t=[],n={};n.groupid=e;try{t=u.post_sync("request",0,"getgroupfriends",n,d.sessionid),t=JSON.parse(t).array}catch(e){t=u.post_sync("request",0,"getgroupfriends",n,d.sessionid)}return t},E.GetUserInfo=function(e,t){var n={};return n.userid=e,n.infoid=t,u.post_sync("request",0,"getuserinfo",n,d.sessionid)},E.GetFriendStatus=function(e){var t,n={};return n.frienduserid=e,t=u.post_sync("request",0,"getfriendstatus",n,d.sessionid),parseInt(t)},E.QueryInfoFromServer=function(e,t){var n={};return n.infoname=parseInt(e),n.param=0,n.strparam=t+"",u.post_sync("request",0,"queryinfofromserver",n,d.sessionid)},E.QueryUserStateString=function(e,t){e==d.userid&&(e=-1);var n={};return n.userid=e,n.infoname=t,u.post_sync("request",0,"queryuserstatestring",n,d.sessionid)},E.StreamRecordCtrl=function(e,t,n,r){var o,s={};return s.userid=e,s.startrecord=t,s.flags=n,s.param=r,C("StreamRecordCtrl(userId:"+e+",startRecord:"+t+",flags:"+n+",param:"+r+")="+(o=u.post_sync("request",0,"streamrecordctrl",s,d.sessionid))),parseInt(o)},E.StreamRecordCtrlEx=function(e,t,n,r,o){var s,a={};return a.userid=e,a.startrecord=t,a.flags=n,a.param=r,a.userstr=o,C("StreamRecordCtrlEx(userId:"+e+",startRecord:"+t+",flags:"+n+",param:"+r+")="+(s=u.post_sync("request",0,"streamrecordctrlex",a,d.sessionid))),parseInt(s)},E.SelectDevice=function(e,t){var n,r={};return r.devicetype=e,r.capturename=t,C("SelectDevice(deviceType:"+e+",captureName:"+t+")="+(n=u.post_sync("request",0,"selectdevice",r,d.sessionid))),parseInt(n)},E.SnapShot=function(e,t,n){var r,o={};return o.userid=e,o.flags=t,o.param=n,C("SnapShot(userId:"+e+",flags:"+t+",param:"+n+")="+(r=u.post_sync("request",0,"snapshot",o,d.sessionid))),parseInt(r)},E.SDKControl=function(e,t){var n={};return n.ctrlcode=e,n.inparam=t,u.post_sync("request",0,"sdkcontrol",n,d.sessionid)},E.TransFile=function(e,t,n,r,o){var s,a=-1,i={};return i.userid=e,i.localpathname=t,i.wparam=n,i.lparam=r,i.flags=o,-1!=(s=u.post_sync("request",0,"transfile",i,d.sessionid))&&(a=0),C("TransFile(userId:"+e+",localPathName:"+t+",wParam:"+n+",lParam:"+r+",flags:"+o+")="+a),s},E.TransFileEx=function(e,t,n,r,o){var s,a={};return a.taskguid=e,a.userid=t,a.localpathname=n,a.flags=r,a.strparam=o,s=u.post_sync("request",0,"transfileex",a,d.sessionid),o.length>50?C("TransFileEx(taskGuid:"+e+",userId:"+t+",localPathName:"+n+",flags:"+r+",strParam len:"+o.length+")="+s):C("TransFileEx(taskGuid:"+e+",userId:"+t+",localPathName:"+n+",flags:"+r+",strParam:"+o+")="+s),parseInt(s)},E.QueryTransTaskInfo=function(e,t,n){var r,o=-1,s={};return s.userid=e,s.taskid=t,s.infoname=n,-1!=(r=u.post_sync("request",0,"querytranstaskinfo",s,d.sessionid))&&(o=0),C("QueryTransTaskInfo(userId:"+e+",taskId:"+t+",infoname:"+n+")="+o),r},E.QueryTransTaskInfoEx=function(e,t){var n,r=-1,o={};return o.taskguid=e,o.infoname=t,-1!=(n=u.post_sync("request",0,"querytranstaskinfoex",o,d.sessionid))&&(r=0),C("QueryTransTaskInfoEx(taskGuid:"+e+",infoname:"+t+")="+r),n},E.CancelTransTask=function(e,t){var n,r={};return r.userid=e,r.taskid=t,C("CancelTransTask(userId:"+e+",taskId:"+t+")="+(n=u.post_sync("request",0,"canceltranstask",r,d.sessionid))),parseInt(n)},E.CancelTransTaskEx=function(e,t,n){var r,o={};return o.taskid=e,o.flags=t,o.errorcode=n,C("CancelTransTaskEx(taskGuid:"+e+",flags:"+t+",errorCode:"+n+")="+(r=u.post_sync("request",0,"canceltranstaskex",o,d.sessionid))),parseInt(r)},E.StreamPlayInit=function(e,t,n,r){var o,s=-1,a="";try{s=null!=(a=JSON.parse(r)).streamindex?a.streamindex:0}catch(e){s=0}""!=a?a.streamindex=s:(a={}).streamindex=s;var i={};if(i.taskguid=e,i.streampath=t,i.flags=n,i.strparam=JSON.stringify(a),0==(o=u.post_sync("request",0,"streamplayinit",i,d.sessionid)))if(N.SetMediaInfo[-1])N.SetMediaInfo[-1][s]||(N.SetMediaInfo[-1][s]={}),N.SetMediaInfo[-1][s].taskGuid=e;else{N.SetMediaInfo[-1]=new Array;var _={};_.taskGuid=e,N.SetMediaInfo[-1][s]=_}return r.length>50?C("StreamPlayInit(taskguid:"+e+",streampath:"+t+",flags:"+n+",strparam len:"+r.length+")="+o):C("StreamPlayInit(taskguid:"+e+",streampath:"+t+",flags:"+n+",strparam:"+r+")="+o),parseInt(o)},E.StreamPlayControl=function(e,t,n,r,o){var s,a={};return a.taskguid=e,a.ctrlcode=t,a.param=n,a.flags=r,a.strparam=o,s=u.post_sync("request",0,"streamplaycontrol",a,d.sessionid),o.length>50?C("StreamPlayControl(taskguid:"+e+",ctrlcode:"+t+",param:"+n+",flags:"+r+",strparam len:"+o.length+")="+s):C("StreamPlayControl(taskguid:"+e+",ctrlcode:"+t+",param:"+n+",flags:"+r+",strparam:"+o+")="+s),parseInt(s)},E.StreamPlaySetVideoPos=function(e,t,n){var r=-1;try{for(var o=-1,s=N.SetMediaInfo[-1],a=0;a<s.length;a++)s[a]&&s[a].taskGuid==e&&(o=a);if(-1==o)return C("StreamPlaySetVideoPos: taskGuid is not existence!"),-1;if(document.getElementById(n)&&"canvas"!=document.getElementById(n).tagName.toLocaleLowerCase())return C("StreamPlaySetVideoPos: The same already exists id form this page"),-1;if(N.SetMediaInfo[-1]&&N.SetMediaInfo[-1][o]&&N.SetMediaInfo[-1][o].id)if(N.SetMediaInfo[-1][o].parentobj==t&&N.SetMediaInfo[-1][o].id==n){if(document.getElementById(n))return C("StreamPlaySetVideoPos: Repeat invoked!"),-1;C("StreamPlaySetVideoPos: change video between the same parent node!"),delete d.docid[N.SetMediaInfo[-1][o].id]}else document.getElementById(N.SetMediaInfo[-1][o].id)?(C("StreamPlaySetVideoPos: remove the already exists video"),d.removeLocalVideo(N.SetMediaInfo[-1][o].id,!1)):(C("StreamPlaySetVideoPos: change video between the different parent node!"),delete d.docid[N.SetMediaInfo[-1][o].id]);if(N.SetMediaInfo[-1]&&N.SetMediaInfo[-1][o])N.SetMediaInfo[-1][o].parentobj=t,N.SetMediaInfo[-1][o].id=n;else try{var i={};i.taskGuid=e,i.parentobj=t,i.id=n,N.SetMediaInfo[-1]||(N.SetMediaInfo[-1]=new Array),N.SetMediaInfo[-1][o]=i}catch(e){C("StreamPlaySetVideoPos err:"+e)}C("StreamPlaySetVideoPos(taskguid:"+e+",id:"+n+")=0"),r=0}catch(e){return C("StreamPlaySetVideoPos: error!"),-1}return r},E.StreamPlayGetInfo=function(e,t){var n={};return n.taskguid=e,n.infoname=t,u.post_sync("request",0,"streamplaygetinfo",n,d.sessionid)},E.StreamPlayDestroy=function(e,t){var n,r={};return r.taskguid=e,r.flags=t,C("StreamPlayDestroy(taskguid:"+e+",flags:"+t+")="+(n=u.post_sync("request",0,"streamplaydestroy",r,d.sessionid))),parseInt(n)},E.AudioGetVolume=function(e){var t,n={};return n.device=e,t=u.post_sync("request",0,"audiogetvolume",n,d.sessionid),parseInt(t)},E.AudioSetVolume=function(e,t){var n,r={};return r.device=e,r.volume=t,C("AudioSetVolume(device:"+e+", volume:"+t+")="+(n=u.post_sync("request",0,"audiosetvolume",r,d.sessionid))),parseInt(n)},E.ShowLVProperty=function(e){var t,n={};return n.caption=e,t=u.post_sync("request",0,"showlvproperty",n,d.sessionid),parseInt(t)},E.ChangeChatMode=function(e){var t,n={};return n.chatMode=e,t=u.post_sync("request",0,"changechatmode",n,d.sessionid),parseInt(t)},E.GetUserChatMode=function(e){var t,n={};return n.userid=e,t=u.post_sync("request",0,"getuserchatmode",n,d.sessionid),parseInt(t)},E.PrivateChatRequest=function(e){var t,n={};return n.userid=e,t=u.post_sync("request",0,"privatechatrequest",n,d.sessionid),parseInt(t)},E.PrivateChatEcho=function(e,t,n){var r,o={};return o.userid=e,o.requestid=t,o.accept=n,r=u.post_sync("request",0,"privatechatecho",o,d.sessionid),parseInt(r)},E.PrivateChatEchoEx=function(e,t,n){var r,o={};return o.userid=e,o.requestid=t,o.errorcode=n,r=u.post_sync("request",0,"privatechatechoex",o,d.sessionid),parseInt(r)},E.PrivateChatExit=function(e){var t,n={};return n.userid=e,t=u.post_sync("request",0,"privatechatexit",n,d.sessionid),parseInt(t)},E.SendSDKFilterData=function(e){var t,n={};return n.buf=e,t=u.post_sync("request",0,"sendsdkfilterdata",n,d.sessionid),parseInt(t)},E.UserInfoControl=function(e,t,n,r,o){var s,a={};return a.userid=e,a.ctrlcode=t,a.wparam=n,a.lparam=r,a.strvalue=o,s=u.post_sync("request",0,"userinfocontrol",a,d.sessionid),parseInt(s)},E.Release=function(){var e={};u.post_sync("request",0,"release",e,d.sessionid),""!==d.workhttpurl&&u.post_sync("request",0,"release",e,d.sessionid,d.httpurl),d.sessionid=null,d.dblclickfullscreen=!0,d.workhttpurl="",d.urlprotocol_addr="127.0.0.1:10010",d.wsport=10010,u.resetparam("httpurl","http://127.0.0.1:30055/sdk"),d.httpheartbeat&&(clearInterval(d.httpheartbeat),d.httpheartbeat=null)},E.PDInit=function(){var e;return e=u.post_sync("request",0,"pdinit",{},d.sessionid),parseInt(e)},E.PDEdgeCut=function(e){var t={};return t.framebase=e,u.post_sync("request",0,"pdedgecut",t,d.sessionid)},E.PDZoneCut=function(e,t){var n={};return n.framebase=e,n.zonerect=t,u.post_sync("request",0,"pdzonecut",n,d.sessionid)},E.PDAdjust=function(e){var t={};return t.framebase=e,u.post_sync("request",0,"pdadjust",t,d.sessionid)},E.PDAugment=function(e,t){var n=50;parseInt(t)&&parseInt(t)>=1&&(n=parseInt(t));var r={};return r.framebase=e,r.bright=n,u.post_sync("request",0,"pdaugment",r,d.sessionid)},E.PDParallel=function(e,t){var n={};return n.framebase=e,n.flags=t,u.post_sync("request",0,"pdparallel",n,d.sessionid)},E.PDSignature=function(e){var t={};return t.framebase=e,u.post_sync("request",0,"pdsignature",t,d.sessionid)},E.PDWriteCheck=function(e,t){var n={};return n.framebase=e,n.zonerect=t,u.post_sync("request",0,"pdwritecheck",n,d.sessionid)},E.PDRecognition=function(e){var t={};return t.framebase=e,u.post_sync("request",0,"pdrecognition",t,d.sessionid)},E.PDGetBright=function(e){var t={};return t.framebase=e,u.post_sync("request",0,"pdgetbright",t,d.sessionid)},E.FileBase64Control=function(e,t,n){for(var o=r(),s=0,a=t.length;s<=a/d.transBufMaxLen;s++){var i=t.slice(d.transBufMaxLen*s,d.transBufMaxLen*(s+1)),_={};_.index=s+1,_.flag=0,_.content=i,_.uuid=o,(s+1)*d.transBufMaxLen>=a&&(_.flag=1);var R={};R.ctrlcode=e,R.fileorigin=_,R.inparam=n,d.socket_send("request",0,"filebase64control",R)}return 0},E.VideoFullScreen=function(e,t){var n=0,r=document.getElementById(e).getAttribute("name");if(r){var o=r.split("_")[0],s=r.split("_")[1];-1==o&&(o=d.userid),t=t?1:0;var a={};a.userid=o,a.streamindex=s,a.flags=t,n=u.post_sync("request",0,"dblclick",a,d.sessionid)}else n=-1;return parseInt(n)},E.pipControl=function(e,t,n,r){-1==e&&(e=d.userid),n=n?1:0;var o={};o.userid=e,o.streamindex=t,o.flags=n,o.wsport=d.wsport,o.strparam=r,u.post_sync("request",0,"pipcontrol",o,d.sessionid)},E.messageBox=function(e,t){var n={};n.textcontent=e,n.strparam=t,u.post_sync("request",0,"messagebox",n,d.sessionid)},E.createWindow=function(e,t,n,r){-1==e&&(e=d.userid),n=n?1:0;var o={};o.userid=e,o.streamindex=t,o.flags=n,o.strparam=r,u.post_sync("request",0,"createwindow",o,d.sessionid)},E.AnyChatBase64=c,E.CloseUserFullScreenWindow=function(e){var t,n={};return n.userid=e,n.flags=0,t=u.post_sync("request",0,"dblclick",n,d.sessionid),parseInt(t)},E}},445:function(module,exports,__webpack_require__){"use strict";var __WEBPACK_AMD_DEFINE_ARRAY__,__WEBPACK_AMD_DEFINE_RESULT__,__WEBPACK_AMD_DEFINE_FACTORY__,__WEBPACK_AMD_DEFINE_RESULT__,_interopRequireDefault=__webpack_require__(318),_typeof2=_interopRequireDefault(__webpack_require__(8)),global,factory;global="undefined"!=typeof self?self:"undefined"!=typeof window?window:void 0!==__webpack_require__.g?__webpack_require__.g:void 0,factory=function(global){var _Base64=global.AnyChatBase64,version="2.4.9",buffer;if(module.exports)try{buffer=eval("require('buffer').Buffer")}catch(e){buffer=void 0}var b64chars="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",b64tab=function(e){for(var t={},n=0,r=e.length;n<r;n++)t[e.charAt(n)]=n;return t}(b64chars),fromCharCode=String.fromCharCode,cb_utob=function(e){if(e.length<2)return(t=e.charCodeAt(0))<128?e:t<2048?fromCharCode(192|t>>>6)+fromCharCode(128|63&t):fromCharCode(224|t>>>12&15)+fromCharCode(128|t>>>6&63)+fromCharCode(128|63&t);var t=65536+1024*(e.charCodeAt(0)-55296)+(e.charCodeAt(1)-56320);return fromCharCode(240|t>>>18&7)+fromCharCode(128|t>>>12&63)+fromCharCode(128|t>>>6&63)+fromCharCode(128|63&t)},re_utob=/[\uD800-\uDBFF][\uDC00-\uDFFFF]|[^\x00-\x7F]/g,utob1=function(e){return e.replace(re_utob,cb_utob)},cb_encode=function(e){var t=[0,2,1][e.length%3],n=e.charCodeAt(0)<<16|(e.length>1?e.charCodeAt(1):0)<<8|(e.length>2?e.charCodeAt(2):0);return[b64chars.charAt(n>>>18),b64chars.charAt(n>>>12&63),t>=2?"=":b64chars.charAt(n>>>6&63),t>=1?"=":b64chars.charAt(63&n)].join("")},btoa1=global.btoa?function(e){return global.btoa(e)}:function(e){return e.replace(/[\s\S]{1,3}/g,cb_encode)},_encode=buffer?buffer.from&&Uint8Array&&buffer.from!==Uint8Array.from?function(e){return(e.constructor===buffer.constructor?e:buffer.from(e)).toString("base64")}:function(e){return(e.constructor===buffer.constructor?e:new buffer(e)).toString("base64")}:function(e){return btoa1(utob1(e))},encode1=function(e,t){return t?_encode(String(e)).replace(/[+\/]/g,(function(e){return"+"==e?"-":"_"})).replace(/=/g,""):_encode(String(e))},encodeURI1=function(e){return encode1(e,!0)},re_btou=new RegExp(["[À-ß][-¿]","[à-ï][-¿]{2}","[ð-÷][-¿]{3}"].join("|"),"g"),cb_btou=function(e){switch(e.length){case 4:var t=((7&e.charCodeAt(0))<<18|(63&e.charCodeAt(1))<<12|(63&e.charCodeAt(2))<<6|63&e.charCodeAt(3))-65536;return fromCharCode(55296+(t>>>10))+fromCharCode(56320+(1023&t));case 3:return fromCharCode((15&e.charCodeAt(0))<<12|(63&e.charCodeAt(1))<<6|63&e.charCodeAt(2));default:return fromCharCode((31&e.charCodeAt(0))<<6|63&e.charCodeAt(1))}},btou1=function(e){return e.replace(re_btou,cb_btou)},cb_decode=function(e){var t=e.length,n=t%4,r=(t>0?b64tab[e.charAt(0)]<<18:0)|(t>1?b64tab[e.charAt(1)]<<12:0)|(t>2?b64tab[e.charAt(2)]<<6:0)|(t>3?b64tab[e.charAt(3)]:0),o=[fromCharCode(r>>>16),fromCharCode(r>>>8&255),fromCharCode(255&r)];return o.length-=[0,0,2,1][n],o.join("")},atob1=global.atob?function(e){return global.atob(e)}:function(e){return e.replace(/[\s\S]{1,4}/g,cb_decode)},_decode=buffer?buffer.from&&Uint8Array&&buffer.from!==Uint8Array.from?function(e){return(e.constructor===buffer.constructor?e:buffer.from(e,"base64")).toString()}:function(e){return(e.constructor===buffer.constructor?e:new buffer(e,"base64")).toString()}:function(e){return btou1(atob1(e))},decode1=function(e){return _decode(String(e).replace(/[-_]/g,(function(e){return"-"==e?"+":"/"})).replace(/[^A-Za-z0-9\+\/]/g,""))},noConflict1=function(){var e=global.AnyChatBase64;return global.AnyChatBase64=_Base64,e};if(global.AnyChatBase64={VERSION:version,atob:atob1,btoa:btoa1,fromBase64:decode1,toBase64:encode1,utob:utob1,encode:encode1,encodeURI:encodeURI1,btou:btou1,decode:decode1,noConflict:noConflict1,__buffer__:buffer},"function"==typeof Object.defineProperty){var noEnum=function(e){return{value:e,enumerable:!1,writable:!0,configurable:!0}};global.AnyChatBase64.extendString=function(){Object.defineProperty(String.prototype,"fromBase64",noEnum((function(){return decode1(this)}))),Object.defineProperty(String.prototype,"toBase64",noEnum((function(e){return encode1(this,e)}))),Object.defineProperty(String.prototype,"toBase64URI",noEnum((function(){return encode1(this,!0)})))}}return global.Meteor&&(AnyChatBase64=global.AnyChatBase64),module.exports?module.exports.AnyChatBase64=global.AnyChatBase64:(__WEBPACK_AMD_DEFINE_ARRAY__=[],__WEBPACK_AMD_DEFINE_RESULT__=function(){return global.AnyChatBase64}.apply(exports,__WEBPACK_AMD_DEFINE_ARRAY__),void 0===__WEBPACK_AMD_DEFINE_RESULT__||(module.exports=__WEBPACK_AMD_DEFINE_RESULT__)),{AnyChatBase64:global.AnyChatBase64}},"object"===(0,_typeof2.default)(exports)?module.exports=factory(global):void 0===(__WEBPACK_AMD_DEFINE_RESULT__="function"==typeof(__WEBPACK_AMD_DEFINE_FACTORY__=factory)?__WEBPACK_AMD_DEFINE_FACTORY__.call(exports,__webpack_require__,exports,module):__WEBPACK_AMD_DEFINE_FACTORY__)||(module.exports=__WEBPACK_AMD_DEFINE_RESULT__)},631:function(e){"use strict";e.exports={BROWSER_TYPE_UNKNOWD:0,BROWSER_TYPE_TBS:1,BROWSER_TYPE_UNDER43GOOGLE:2,BROWSER_TYPE_UNDER50GOOGLE:3,BROWSER_TYPE_GOOGLE:4,ACCESS_TYPE_H5_LOW:1,ACCESS_TYPE_H5_HIGH:2,SDK_FUNCTIONS_H5BASE:1,SDK_FUNCTIONS_H5SEPARATESTREAMS:2,SDK_FUNCTIONS_H5NEWSESSIONKEEP:4,SDK_FUNCTIONS_H5NEWTRANSBUF:8,SDK_FUNCTIONS_H5NEWSCENE:16,SDK_FUNCTIONS_LOGOPTIMIZE:32,ANYCHAT_SVRFLAGS_ROOTSERVER:1,ANYCHAT_SVRFLAGS_DNSSERVER:2,ANYCHAT_SVRFLAGS_LOGINSERVER:4,ANYCHAT_SVRFLAGS_MEDIASERVER:8,ANYCHAT_SVRFLAGS_CORESERVER:16,ANYCHAT_SVRFLAGS_RECORDSERVER:32,ANYCHAT_SVRFLAGS_FILESERVER:64,ANYCHAT_SVRFLAGS_DBPROXYSERVER:128,ANYCHAT_SVRFLAGS_APPSERVER:256,ANYCHAT_SVRFLAGS_WORMHOLESERVER:512,ANYCHAT_SVRFLAGS_BUSINESERVER:1024,ANYCHAT_SVRFLAGS_QUEUESERVER:2048,ANYCHAT_SVRFLAGS_LBSERVER:4096,ANYCHAT_SVRFLAGS_MTSERVER:8192,ANYCHAT_SVRFLAGS_PMSERVER:16384,ANYCHAT_SVRFLAGS_LUSERVER:32768,ANYCHAT_SVRFLAGS_HTML5SERVER:65536,ANYCHAT_SVRFLAGS_AGENTSERVER:131072,ANYCHAT_SVRFLAGS_RECORDPROXY:262144,ANYCHAT_SVRFLAGS_RECORDDISPATCH:524288,ANYCHAT_SVRFLAGS_DBSERVER:1048576,ANYCHAT_SVRFLAGS_WEBRTCGWSERVER:2097152,ANYCHAT_SVRFLAGS_GENERALSERVER:4194304,ANYCHAT_SVRFLAGS_STORAGEMONITOR:8388608,ANYCHAT_SVRFLAGS_STORAGEMANAGE:16777216,ANYCHAT_SVRFLAGS_STORAGESERVER:33554432,ANYCHAT_SVRFLAGS_ACCESSGATEWAY:134217728,ANYCHAT_SVRFLAGS_ROOMSERVER:268435456,ANYCHAT_SVRFLAGS_AISERVER:536870912,BRAC_SO_CORESDK_NEWGUID:29,BRAC_SO_VIDEOSHOW_CLIPMODE:86,BRAC_SO_CORESDK_AIROBOT:102,BRAC_SO_LOCALVIDEO_CLIPMODE:107,BRAC_SO_CORESDK_WRITELOG:135,BRAC_SO_CORESDK_CHROMEPARAMS:279,BRAC_SO_CLOUD_APPGUID:300,PROGRAM_STAGE_CLOSED:0,PROGRAM_STAGE_CONNECT:1,PROGRAM_STAGE_LOGIN:2,PROGRAM_STAGE_ENTERROOM:3,STREAM_INFO_TYPE_UPLOAD:1,STREAM_INFO_TYPE_DOWNLOAD:2,WEBRTC_VERSION_ADAPTER:1,WEBRTC_VERSION_ORIGIN:2,STREAM_TYPE_SEND:1,STREAM_TYPE_RECV:2,ANYCHAT_AI_CTRL_ROBOT_INIT:1,ANYCHAT_AI_CTRL_ROBOT_RELEASE:2,ANYCHAT_AI_CTRL_ROBOT_ACTIVETEST:3,ANYCHAT_AI_CTRL_ABILITY_INVOKE:4,ANYCHAT_AI_CTRL_ABILITY_REQUEST:5,ANYCHAT_AI_CTRL_ABILITY_RELEASE:6,ANYCHAT_AI_CTRL_ABILITY_ACTIVETEST:7,ANYCHAT_AI_CTRL_GETOPTION:8,ANYCHAT_AI_CTRL_SETOPTION:9,ANYCHAT_AI_TYPE_ASR:1,ANYCHAT_AI_TYPE_TTS:2,ANYCHAT_AI_TYPE_VPR:3,ANYCHAT_AI_TYPE_AFR:4,ANYCHAT_AI_TYPE_OCR:5,ANYCHAT_AI_TYPE_HWR:6,ANYCHAT_AI_TYPE_NLU:7,ANYCHAT_AI_TYPE_FPR:8,ANYCHAT_AI_TYPE_AIR:9,ANYCHAT_AI_TYPE_AIC:100,ANYCHAT_AI_TYPE_APM:101,ANYCHAT_AI_EVENT_ROBOT_INITRESULT:1,ANYCHAT_AI_EVENT_ROBOT_STATUS:2,ANYCHAT_AI_EVENT_ROBOT_ACTIVERET:3,ANYCHAT_AI_EVENT_ABILITY_RESULT:4,ANYCHAT_AI_EVENT_ABILITY_STATUS:5,ANYCHAT_AI_EVENT_ABILITY_ACTIVERET:6,ANYCHAT_AIROBOT_STATUS_UNKNOW:0,ANYCHAT_AIROBOT_STATUS_OFFLINE:1,ANYCHAT_AIROBOT_STATUS_PREPARE:2,ANYCHAT_AIROBOT_STATUS_ONLINE:3,ANYCHAT_AIROBOT_STATUS_PAUSE:4,ANYCHAT_AIABILITY_STATUS_UNKNOW:0,ANYCHAT_AIABILITY_STATUS_PREPARE:1,ANYCHAT_AIABILITY_STATUS_PROCESS:2,ANYCHAT_AIABILITY_STATUS_FINISH:3,ANYCHAT_APMMODE_AUDIOPLAY:1,ANYCHAT_APMMODE_VIDEOPLAY:2,ANYCHAT_APMMODE_AUDIOVIDEOPLAY:3,ANYCHAT_TTSMODE_FILE:1,ANYCHAT_TTSMODE_LIVEPLAY:2,TRANS_FILE_DATA_TYPE_BASE4:1,TRANS_FILE_DATA_TYPE_BINARY:2,BRAC_TRANSTASK_ALL:0,BRAC_TRANSTASK_PROGRESS:1,BRAC_TRANSTASK_BITRATE:2,BRAC_TRANSTASK_STATUS:3,BRAC_TRANSTASK_SAVEASPATH:4,BRAC_TRANSTASK_SOURCEFILE:5,BRAC_TRANSTASK_JSONSTATUS:6,BRAC_TRANSTASK_STATUS_UNKNOW:0,BRAC_TRANSTASK_STATUS_PREPARE:1,BRAC_TRANSTASK_STATUS_TRANSMITTING:2,BRAC_TRANSTASK_STATUS_COMPLETE:3,BRAC_TRANSTASK_STATUS_CANCELBYSENDER:4,BRAC_TRANSTASK_STATUS_CANCELBYRECEIVER:5,BRAC_RECORD_FLAGS_SNAPSHOT:1024,BRAC_SO_ENABLEWEBSERVICE:11002,BRAC_SO_LOCALPATH2URL:11003,BRAC_SO_CHROME_WORKMODE:4294901761,BRAC_USERSTATE_SPEAKVOLUME:3,BRAC_USERSTATE_VIDEOBITRATE:9,BRAC_USERSTATE_AUDIOBITRATE:10,BRAC_SO_RECORD_INSERTIMAGE:146,WM_GV_CONNECT:1225,WM_GV_LOGINSYSTEM:1226,WM_GV_ENTERROOM:1227,WM_GV_USERATROOM:1229,WM_GV_LINKCLOSE:1230}},37:function(e,t){"use strict";var n,r={},o={},s=0,a=0,i=0,_=null;function R(){var e=i,t={eventname:"request",ssrc:0,seq:0,data:{cmdcode:"restrans",begin_reg:++e}};n.is_socket_active&&n.socket_instance&&n.socket_instance.send(JSON.stringify(t))}t.AddDataPack=function(e){return"heartbeat"!==e.data.cmdcode?(e.seq=s,r[s]=e,s++):"heartbeat"===e.data.cmdcode&&(o=e),e},t.DelDataPack=function(e){for(var t=e;t>=a;t--)delete r[t];a=e},t.DelHeartbeatDate=function(){o={}},t.setWay=function(e){n=e,_&&(clearInterval(_),_=null),_=setInterval((function(){var e;e={eventname:"ack",ssrc:0,seq:i,data:{}},n.is_socket_active&&n.socket_instance&&n.socket_instance.send(JSON.stringify(e))}),3e3)},t.SetLatestNotifySeq=function(e){var t=-1;return e-i!=1?("{}"!==JSON.stringify(o)&&n.socket_send("request",0,"heartbeat",{}),R()):(i=e,t=0),n.new_keep_session_flag||(i=e),t},t.ReSendStrans=R,t.ReSendDataPackToServer=function(e){for(var t in r)t>e&&n.is_socket_active&&n.socket_instance&&r[t]&&(n.socket_instance.send(JSON.stringify(r[t])),n.new_keep_session_flag||delete r[t])},t.clear_all=function(){s=0,i=0,a=0,r={}}},682:function(e){"use strict";e.exports={AC_ERROR_SUCCESS:0,AC_ERROR_DB_ERROR:1,AC_ERROR_NOTINIT:2,AC_ERROR_NOTINROOM:3,AC_ERROR_MEMORYFAIL:4,AC_ERROR_EXCEPTION:5,AC_ERROR_CANCEL:6,AC_ERROR_PROTOCOLFAIL:7,AC_ERROR_SESSIONNOTEXIST:8,AC_ERROR_DATANOTEXIST:9,AC_ERROR_DATAEXIST:10,AC_ERROR_INVALIDGUID:11,AC_ERROR_RESOURCERECOVER:12,AC_ERROR_RESOURCEUSED:13,AC_ERROR_JSONFAIL:14,AC_ERROR_OBJECTDELETE:15,AC_ERROR_SESSIONEXIST:16,AC_ERROR_SESSIONNOTINIT:17,AC_ERROR_DATANOTPREPARE:18,AC_ERROR_FUNCNOTALLOW:20,AC_ERROR_FUNCOPTERROR:21,AC_ERROR_DEVICEOPENFAIL:22,AC_ERROR_NOENOUGHRESOURCE:23,AC_ERROR_PIXFMTNOTSUPPORT:24,AC_ERROR_NOTMULTICASTADDR:25,AC_ERROR_MULTIRUNERROR:26,AC_ERROR_FILETRUSTFAILED:27,AC_ERROR_CERTVERIFYFAILED:28,AC_ERROR_CERTUSERFAILED:29,AC_ERROR_MASTERISSLAVE:30,AC_ERROR_MASTERNOTCREDIT:31,AC_ERROR_VERSIONNOTMATCH:32,AC_ERROR_CERTFAILSECOND:33,AC_ERROR_SERVERVERIFYFAIL:34,AC_ERROR_CLIENTCERTFAILED:35,AC_ERROR_CERTSUMFAILED:36,AC_ERROR_REMOTECTRL:37,AC_ERROR_DUPLICATESERVICEID:38,AC_ERROR_DIRENTERROR:39,AC_ERROR_EXTRACTFILEERROR:40,AC_ERROR_STARTPROCESSFAILED:41,AC_ERROR_SERVICEISRUNNING:42,AC_ERROR_DISKSPACELIMITED:43,AC_ERROR_REQUESTFAILED:44,AC_ERROR_INVALIDMACHINE:45,AC_ERROR_GETCERTINFOFAILED:46,AC_ERROR_CLUSTERNOTMATCH:47,AC_ERROR_NONECLUSTERID:48,AC_ERROR_CREATESERVICE_MORE:49,AC_ERROR_COPYFILEFAILED:50,AC_ERROR_CLOUDNATIVEDBFAIL:51,AC_ERROR_CLOUDOSSUPLOADFAIL:52,AC_ERROR_SERVICEBINDCHANGE:53,AC_ERROR_SERVICENOTBIND:54,AC_ERROR_SERVICEBINDFAIL:55,AC_ERROR_PIPELINEUSERFAIL:56,AC_ERROR_PIPELINESESSFAIL:57,AC_ERROR_SERVICECLOSED:58,AC_ERROR_FILEENCRYPTED:59,AC_ERROR_FILEHEADINVAILD:60,AC_ERROR_FILEDECODE_PASSERR:61,AC_ERROR_OVERFLOW:62,AC_ERROR_OLDSERVER:63,AC_ERROR_CONNECT_TIMEOUT:100,AC_ERROR_CONNECT_ABORT:101,AC_ERROR_CONNECT_AUTHFAIL:102,AC_ERROR_CONNECT_DNSERROR:103,AC_ERROR_CONNECT_OVERFLOW:104,AC_ERROR_CONNECT_FUNCLIMIT:105,AC_ERROR_CONNECT_INTRANET:106,AC_ERROR_CONNECT_OLDVERSION:107,AC_ERROR_CONNECT_SOCKETERR:108,AC_ERROR_CONNECT_DEVICELIMIT:109,AC_ERROR_CONNECT_PAUSED:110,AC_ERROR_CONNECT_HOTSERVER:111,AC_ERROR_CONNECT_ERRCERUSER:112,AC_ERROR_CONNECT_IPFORBID:113,AC_ERROR_CONNECT_TYPEWRONG:114,AC_ERROR_CONNECT_ERRORIP:115,AC_ERROR_CONNECT_SELFCLOSE:116,AC_ERROR_CONNECT_NOSVRLIST:117,AC_ERROR_CONNECT_LBTIMEOUT:118,AC_ERROR_CONNECT_NOTWORK:119,AC_ERROR_CONNECT_OFFLINE:120,AC_ERROR_CONNECT_NETLIMITED:121,AC_ERROR_CONNECT_LOWTRAFFIC:122,AC_ERROR_CONNECT_IPV6FAIL:123,AC_ERROR_CONNECT_NOMASTER:124,AC_ERROR_CONNECT_NOSTATUS:125,AC_ERROR_CERTIFY_FAIL:200,AC_ERROR_ALREADY_LOGIN:201,AC_ERROR_ACCOUNT_LOCK:202,AC_ERROR_IPADDR_LOCK:203,AC_ERROR_VISITOR_DENY:204,AC_ERROR_INVALID_USERID:205,AC_ERROR_SERVERSDK_FAIL:206,AC_ERROR_SERVERSDK_TIMEOUT:207,AC_ERROR_NOTLOGIN:208,AC_ERROR_LOGIN_NEWLOGIN:209,AC_ERROR_LOGIN_EMPTYNAME:210,AC_ERROR_KICKOUT:211,AC_ERROR_SERVER_RESTART:212,AC_ERROR_FORBIDDEN:213,AC_ERROR_SIGSTREMPTY:214,AC_ERROR_SIGVERIFYFAIL:215,AC_ERROR_SIGPUBLICKEYEMPTY:216,AC_ERROR_SIGPRIVATEKEYEMPTY:217,AC_ERROR_SIGPARAMEMPTY:218,AC_ERROR_SIGPARAMFAIL:219,AC_ERROR_SIGTIMEFAILURE:220,AC_ERROR_APPNOTACTIVE:221,AC_ERROR_APPPAUSED:222,AC_ERROR_APPLOCKED:223,AC_ERROR_APPEXPIRED:224,AC_ERROR_APPUNKNOWSTATUS:225,AC_ERROR_SIGALREADYUSED:226,AC_ERROR_USERROLE_FAIL:227,AC_ERROR_INVALID_AGENT:228,AC_ERROR_SERVERSIGFAIL:229,AC_ERROR_ROOM_LOCK:300,AC_ERROR_ROOM_PASSERR:301,AC_ERROR_ROOM_FULLUSER:302,AC_ERROR_ROOM_INVALID:303,AC_ERROR_ROOM_EXPIRE:304,AC_ERROR_ROOM_REJECT:305,AC_ERROR_ROOM_OWNERBEOUT:306,AC_ERROR_ROOM_ENTERFAIL:307,AC_ERROR_ROOM_ALREADIN:308,AC_ERROR_ROOM_NOTIN:309,AC_ERROR_ROOM_MAXNUM:310,AC_ERROR_ROOM_NOPORT:311,AC_ERROR_STREAM_OLDPACK:350,AC_ERROR_STREAM_SAMEPAK:351,AC_ERROR_STREAM_PACKLOSS:352,AC_ERROR_STREAM_MISTAKE:353,AC_ERROR_STREAM_LACKBUFFER:354,AC_ERROR_STREAM_INVALIDID:355,AC_ERROR_ROOM_PRINULL:401,AC_ERROR_ROOM_REJECTPRI:402,AC_ERROR_ROOM_PRIDENY:403,AC_ERROR_ROOM_PRIREQIDERR:420,AC_ERROR_ROOM_PRIALRCHAT:421,AC_ERROR_ROOM_PRITIMEOUT:431,AC_ERROR_ROOM_PRICHATBUSY:432,AC_ERROR_ROOM_PRIUSERCLOSE:433,AC_ERROR_ROOM_PRISELFCLOSE:434,AC_ERROR_ROOM_PRIREQCANCEL:435,AC_ERROR_VIDEOCALL_INCHAT:440,AC_ERROR_MICLOSE_TIMEOUT:500,AC_ERROR_MICLOSE_HIGHUSER:501,AC_ERROR_COMMBUS_SELFMASTER:610,AC_ERROR_COMMBUS_OTHERMASTER:611,AC_ERROR_COMMBUS_LOWPRIORITY:612,AC_ERROR_TRANSBUF_CREATEFAIL:700,AC_ERROR_TRANSBUF_NOTASK:701,AC_ERROR_TRANSFILE_OPENFAIL:710,AC_ERROR_TRANSFILE_ZEROLEN:711,AC_ERROR_TRANSFILE_TOOLARGE:712,AC_ERROR_TRANSFILE_READFAIL:713,AC_ERROR_TRANSFILE_DOWNLOADING:714,AC_ERROR_TRANSFILE_FAILED:715,AC_ERROR_TRANSFILE_NOTASK:716,AC_ERROR_RECORD_NOTASK:720,AC_ERROR_RECORD_CREATEFAIL:721,AC_ERROR_RECORD_WAITINFO:722,AC_ERROR_RECORD_VIDEOPARAMFAIL:723,AC_ERROR_RECORD_AUDIOPARAMFAIL:724,AC_ERROR_RECORD_CREATEFILEFAIL:725,AC_ERROR_RECORD_SERVICEOFFLINE:726,AC_ERROR_QUEUE_INVALID:750,AC_ERROR_QUEUE_PREPARESERVICE:751,AC_ERROR_QUEUE_TIMEOUT:752,AC_ERROR_WARNING_UDPFAIL:780,AC_ERROR_WARNING_MISCUTILFAIL:781,AC_ERROR_WARNING_MEDIAUTILFAIL:782,AC_ERROR_WARNING_MEDIACOREFAIL:783,AC_ERROR_WARNING_MEDIASHOWFAIL:784,AC_ERROR_WARNING_CAMERAFAIL:785,AC_ERROR_WARNING_MICFAIL:786,AC_ERROR_CERTFILE_GETINFOFAILED:800,AC_ERROR_CERTFILE_EXPIRED:801,AC_ERROR_CERTFILE_DECODEFAIL:802,AC_ERROR_CERTFILE_HDSIGFAIL:810,AC_ERROR_CERTFILE_CPUNUMFAIL:811,AC_ERROR_CERTFILE_CPUFREQFAIL:812,AC_ERROR_CERTFILE_RAMSIZEFAIL:813,AC_ERROR_CERTFILE_MACADDRFAIL:814,AC_ERROR_CERTFILE_CPUMODELFAIL:815,AC_ERROR_CERTFILE_DRIVERIDFAIL:816,AC_ERROR_CERTFILE_UKEYSIGFAIL:830,AC_ERROR_CERTFILE_UKEYNOTFOUND:831,AC_ERROR_CERTFILE_UKEYREADERR:832,AC_ERROR_CERTFILE_UKEYNOTMATCH:833,AC_ERROR_CERTFILE_UKEYDLLFAIL:834,AC_ERROR_CERTFILE_URLVERIFYFAIL:840,AC_ERROR_CERTFILE_DNSFAIL:842,AC_ERROR_CERTFILE_BINDIPFAIL:850,AC_ERROR_CERTFILE_DOMAINFAIL:860,AC_ERROR_CERTFILE_UKEYFAIL:861,AC_ERROR_CERTFILE_IPADDRFAIL:862,AC_ERROR_VIDEO_OPENFAIL:10001,AC_ERROR_VIDEO_UNKNOWFMT:10002,AC_ERROR_VIDEO_VIDIOC_G_FMT:10003,AC_ERROR_VIDEO_VIDIOC_S_FMT:10004,AC_ERROR_VIDEO_VIDIOC_G_PARM:10005,AC_ERROR_VIDEO_VIDIOC_S_PARM:10006,AC_ERROR_VIDEO_VIDIOC_QUERYCAP:10007,AC_ERROR_VIDEO_V4L2_CAP_VIDEO:10008,AC_ERROR_VIDEO_PREP_CAPBUFFER_FALL:10009,AC_ERROR_VIDEO_VIDIOC_REQBUFS:10010,AC_ERROR_VIDEO_VIDIOC_QUERYBUF:10011,AC_ERROR_VIDEO_MAP_FAILED:10012,AC_ERROR_VIDEO_VIDIOC_QBUF:10013,AC_ERROR_VIDEO_PREPBUF:10013,AC_ERROR_VIDEO_GETVIDEO_FAIL:10014,AC_ERROR_VIDEO_VIDIOC_DQBUF:10015,AC_ERROR_VIDEO_VIDIOC_STREAMON:10016,AC_ERROR_VIDEO_VIDIOC_STREAMOFF:10017,AC_ERROR_VIDEO_CAMERA_EBUSY:10018,AC_ERROR_VIDEO_UNSUPPORTMODE:10019,AC_ERROR_VIDEO_CAMERA_EINVAL:10020,AC_ERROR_AUDIO_OPENFAIL:10500,AC_ERROR_AUDIO_ALLOCHWPARAMS:10501,AC_ERROR_AUDIO_INTERLEAVED:10502,AC_ERROR_AUDIO_FORMAT:10503,AC_ERROR_AUDIO_SAMPLESPERSEC:10504,AC_ERROR_AUDIO_CHANNELS:10505,AC_ERROR_AUDIO_PERIODS:10506,AC_ERROR_AUDIO_BUFFERSIZE:10507,AC_ERROR_AUDIO_PARAMS:10508,AC_ERROR_AUDIO_BUFFERTIME:10509,AC_ERROR_AUDIO_BUFFERFRAME:10510,AC_ERROR_AUDIO_PERIODTIME:10511,AC_ERROR_AUDIO_PERIODFRAME:10512,AC_ERROR_AUDIO_ALLOCSWPARAMS:10513,AC_ERROR_AUDIO_START_THRESHOID:10514,AC_ERROR_AUDIO_START_AVAIL_MIN:10515,AC_ERROR_AUDIO_PCMPREPARE:10516,AC_ERROR_AUDIO_READFAIL:10517,AC_ERROR_AUDIO_CAPMODE:10518,AC_ERROR_PLAY_INVALIDSTREAM:2e4,AC_ERROR_STREAM_SESSIONFAILED:3e4,AC_ERROR_VIDEOCALL_CANCEL:100101,AC_ERROR_VIDEOCALL_OFFLINE:100102,AC_ERROR_VIDEOCALL_BUSY:100103,AC_ERROR_VIDEOCALL_REJECT:100104,AC_ERROR_VIDEOCALL_TIMEOUT:100105,AC_ERROR_VIDEOCALL_DISCONNECT:100106,AC_ERROR_VIDEOCALL_NOTINCALL:100107,AC_ERROR_OBJECT_EXISTAREA:100201,AC_ERROR_OBJECT_EXISTQUEUE:100202,AC_ERROR_APPID_DEFAULTNOTSUPPORT:100300,AC_ERROR_APPID_SIGNEED:100301,AC_ERROR_APPID_SIGFAILED:100302,AC_ERROR_APPID_NOTEXIST:100303,AC_ERROR_APPID_SYSLOCK:100304,AC_ERROR_APPID_NOTMATCH:100305,AC_ERROR_APPID_NOTCLOUDSERVER:100306,AC_ERROR_APPID_CHARGEMACHINELACK:100307,AC_ERROR_APPID_CHARGEMODECHANGE:100308,AC_ERROR_APPID_CARRIERCHANGE:100309,AC_ERROR_APPID_IDLE:100310,AC_ERROR_USERCFG_PASSWDLEN_SMALL:100400,AC_ERROR_USERCFG_USERNAME_SAME:100401,AC_ERROR_USERCFG_ACCESSLIMIT:100402,AC_ERROR_USERCFG_USERNAME_LIMIT:100403,AC_ERROR_LIVEUPDATE_BEGIN:100500,AC_ERROR_LIVEUPDATE_STOPING:100501,AC_ERROR_LIVEUPDATE_BACKUPING:100502,AC_ERROR_LIVEUPDATE_DELETEING:100503,AC_ERROR_LIVEUPDATE_COPYING:100504,AC_ERROR_LIVEUPDATE_STARTING:100505,AC_ERROR_LIVEUPDATE_RECOVERING:100506,AC_ERROR_LIVEUPDATE_ISTAGVER:100507,AC_ERROR_LIVEUPDATE_SERVICENEEDSTOP:100508,AC_ERROR_LIVEUPDATE_BACKUPFAIL:100509,AC_ERROR_LIVEUPDATE_DELETEFAIL:100510,AC_ERROR_LIVEUPDATE_COPYFAIL:100511,AC_ERROR_LIVEUPDATE_RECOVERYFAIL:100512,AC_ERROR_LIVEUPDATE_BRIDGENOTREGISTER:100513,AC_ERROR_LIVEUPDATE_WRITECONFIGFILEFAILED:100514,AC_ERROR_LIVEUPDATE_CANTGETBACKUPDIR:100515,AC_ERROR_LIVEUPDATE_FINISH:100516,AC_ERROR_LIVEUPDATE_UNABLETOGETMAINTAINIFO:100517,AC_ERROR_LIVEUPDATE_NOTRENAMEDIR:100518,AC_ERROR_STOPPROCESS_TIMEOUT:100600,AC_ERROR_STOPPROCESS_FAIL:100601,AC_ERROR_STOPPROCESS_FORCEFAIL:100602,AC_ERROR_STARTPROCESS_TIMEOUT:100603,AC_ERROR_SERVICE_CONTROLLED:100604,AC_ERROR_SERVICE_EXISTELSEVER:100605,AC_ERROR_SERVICE_NOTSUPPORT:100606,AC_ERROR_NONEXISTENCE_THE_VERSION:100607,AC_ERROR_NONEXISTENCE_THE_SERVICE:100608,AC_ERROR_ILLEGAL_EXTRA_CONFIG:100609,AC_ERROR_MOVETEMPFILE_FAIL:100610,AC_ERROR_INCOMPATIBLE_CURRENT_PLATFORM:100611,AC_ERROR_GETROOT_CONNECT_FAIL:100612,AC_ERROR_BUSINESS_PARAM_INVALID:100701,AC_ERROR_BUSINESS_APPID_NOTEXIST:100702,AC_ERROR_BUSINESS_BODY_INVALID:100703,AC_ERROR_BUSINESS_SIGVERIFYFAIL:100704,AC_ERROR_BUSINESS_SIGTIMEINVALID:100705,AC_ERROR_BUSINESS_MEMORYFAIL:100706,AC_ERROR_BUSINESS_EXCEPTION:100707,AC_ERROR_BUSINESS_PROTOCOLFAIL:100708,AC_ERROR_BUSINESS_TIMEOUT:100709,AC_ERROR_BUSINESS_FILENOEXIST:100710,AC_ERROR_DB_EXECUTE_ERROR:100801,AC_ERROR_DB_SELECT_NODATA:100802,AC_ERROR_DB_FETCH_ERROR:100803,AC_ERROR_DB_EXCEPTION:100804,AC_ERROR_DB_CONNECT_ERROR:100805,AC_ERROR_PPTHELPER_INVALIAD_URL:100901,AC_ERROR_PPTHELPER_RETURNED_ERROR:100902,AC_ERROR_PPTHELPER_COULDNT_CONNECT:100903,AC_ERROR_WEBRTC_BROSWERNOTSUPPORT:15e4,AC_ERROR_WEBRTC_NOTINSTALLTBS:150001,AC_ERROR_WEBRTC_PCCREATEFAIL:150002,AC_ERROR_H5_SUPPORT_BROWSERNOWEBSOCKET:1020001,AC_ERROR_H5_SUPPORT_BROWSERNOCANVAS:1020002}},760:function(e,t){"use strict";var n="http://127.0.0.1:30055/sdk";t.post_async=function(e,t,r,o,s,a){var i={};i.eventname=e,i.sessionid=s,i.ssrc=0,o.userid&&(o.userid=parseInt(o.userid)),i.data={},i.data=o,i.data.cmdcode=r;var _=new XMLHttpRequest;null!=a?_.open("post",a,!0):_.open("post",n,!0),_.setRequestHeader("Content-type","application/x-www-form-urlencoded"),_.onreadystatechange=function(){_.readyState===XMLHttpRequest.DONE&&_.status},_.send(JSON.stringify(i))},t.post_sync=function(e,t,r,o,s,a){var i,_={};_.eventname=e,_.sessionid=s,_.ssrc=0,o.userid&&(o.userid=parseInt(o.userid)),_.data={},_.data=o,_.data.cmdcode=r;var R=new XMLHttpRequest;null!=a?R.open("post",a,!1):R.open("post",n,!1),R.setRequestHeader("Content-type","application/x-www-form-urlencoded");try{R.send(JSON.stringify(_)),4==R.readyState&&200==R.status&&(i=R.responseText),void 0===i&&(i=-1)}catch(e){}return i},t.resetparam=function(e,t){"httpurl"==e&&(n=t)}},974:function(e,t){"use strict";var n;t.start=function(e){var t={};clearInterval(n),n=setInterval((function(){e.socket_send("request",0,"heartbeat",t)}),5e3)},t.notify=function(e){},t.stop=function(){n&&clearInterval(n)}},762:function(e){"use strict";var t=null;e.exports=function(e){return{clear_timer:function(){t||(clearInterval(t),t=null)},getStringLength:function(e){for(var t,n=e,r=0;r<n.length;r++){var o=n.charAt(r);/^[\u0000-\u00ff]$/.test(o)?t+=1:t+=3}return t}}}},470:function(e,t,n){"use strict";var r=n(318)(n(8)),o=n(171),s=n(631),a=(n(682),n(37));function i(){this.event_k_v={}}function _(){this.roomid=0,this.userid=-1,this.appGuid="",this.socket_instance=null,this.sessionid=null,this.is_socket_active=!1,this.peerConnections={},this.candidateInfos={},this.dataChannels={},this.userList=[],this.userControlInfo={},this.cmdSpliceInfo={},this.aiRobotInfoMapping={},this.timeOutInt=30,this.new_transbuf_flag=!0,this.srrc_index=0,this.sendData={},this.system="",this.mobile_type=0,this.queueId="",this.is_open_microphone=!1,this.camera_index=0,this.microphone_index=0,this.deviceInfo=null,this.bReOpenFlag=!1,this.bSwitchCamera=!1,this.websocket_id="",this.reconnect_flag=!0,this.reconnect_num=0,this.releaseFlag=!1,this.websocket_info=[],this.connect_limit_num=5,this.connect_cur_num=0,this.inLinkingFlag=!1,this.dataChnlSendDataLen=0,this.rateUnit=1024,this.videoCodecConfig=null,this.videoCodecApply=null,this.authority_flag=!1,this.userReocrdLimitNum=10,this.browerVisibility=!0,this.mediaAuthority=!1,this.enumMediaDevice=!1,this.transBufMaxLen=6144,this.transFileMaxLen=6144,this.socketSendMaxLen=20480,this.sdkFunction=s.SDK_FUNCTIONS_H5BASE+s.SDK_FUNCTIONS_H5SEPARATESTREAMS+s.SDK_FUNCTIONS_H5NEWSESSIONKEEP+s.SDK_FUNCTIONS_H5NEWTRANSBUF+s.SDK_FUNCTIONS_H5NEWSCENE+s.SDK_FUNCTIONS_LOGOPTIMIZE,this.urlprotocolmode=!0,this.urlprotocol_addr="127.0.0.1:10010",this.wsport=10010,this.otherresolution=[],this.docid=[],this.exeStatusFlag=-1,this.httpurl="http://127.0.0.1:30055/sdk",this.workhttpurl="",this.logout_flag=!1,this.localVideoClipMode=0,this.remoteVideoClipMode=0,this.filebase64json={},this.placeholderInfo=[],this.httpheartbeat=null,this.logOptimizeFlags=!1,this.dblclickfullscreen=!0}i.prototype.on=function(e,t){this.event_k_v[e]=this.event_k_v[e]||[],this.event_k_v[e].push(t)},i.prototype.emit=function(e,t){var n=this.event_k_v[e]||[],r=Array.prototype.slice.call(arguments,1);n.map((function(e,t){e.apply(null,r)}))},_.prototype=new i,_.prototype.getConnectInfoCount=function(){var e=0,t=this.websocket_info;for(var n in t)e++;return e},_.prototype.handleConnectInfo=function(e){var t=this,n=t.getConnectState();return n?e==t.getConnectUuidByState()?n=!1:(t.websocket_info[e].state=!1,t.websocket_info[e].socket&&t.websocket_info[e].socket.close(),t.websocket_info[e].socket=null):(t.websocket_info[e].state=!0,t.is_socket_active=!0),n},_.prototype.checkConnectUuid=function(e){var t=!1,n=this.websocket_info;for(var r in n)if(n[r].uuid==e){t=!0;break}return t},_.prototype.getConnectUuidByAddr=function(e){var t="",n=this.websocket_info;for(var r in n)if(n[r].origin==e){t=n[r].uuid;break}return t},_.prototype.getConnectUuidByState=function(){var e=this.websocket_info,t=0;for(var n in e)if(e[n].state){t=e[n].uuid;break}return t},_.prototype.closeConnectLink=function(e){var t=this;for(var n in t.websocket_info)if(t.websocket_info[n].state){e||(t.websocket_info[n].state=!1),t.websocket_info[n].socket&&t.websocket_info[n].socket.close(),t.websocket_info[n].socket=null,t.is_socket_active=!1,t.socket_instance=null;break}},_.prototype.closeConnectStateByUuid=function(e){var t=this;for(var n in t.websocket_info)if(t.websocket_info[n].uuid==e){t.websocket_info[n].state=!1,t.websocket_info[n].socket&&t.websocket_info[n].socket.close(),t.websocket_info[n].socket=null,t.is_socket_active=!1,t.socket_instance=null;break}},_.prototype.openConnectStateByUuid=function(e){var t=this;for(var n in t.websocket_info)if(t.websocket_info[n].uuid==e){t.websocket_info[n].state=!0;break}},_.prototype.getConnectState=function(){var e=this.websocket_info,t=!1;for(var n in e)if(e[n].state){t=!0;break}return t},_.prototype.cleanConnectInfo=function(){var e=this,t=e.websocket_info;for(var n in e.websocket_info)t[n].state&&(e.websocket_info[t[n].uuid].state=!1,e.websocket_info[t[n].uuid].socket&&e.websocket_info[t[n].uuid].socket.close(),e.websocket_info[t[n].uuid].socket=null,e.is_socket_active=!1,e.socket_instance=null);e.websocket_info=[]},_.prototype.setLastConnectFlag=function(e){var t=this;if(t.websocket_info[e]){for(var n in t.websocket_info)t.websocket_info[n].lastconnect=!1;t.websocket_info[e].lastconnect=!0}},_.prototype.getNextLastConnectUuid=function(){var e=0,t=this.websocket_info,n=!1;for(var r in t){if(0==e&&(e=t[r].uuid),n){e=t[r].uuid,this.setLastConnectFlag(e);break}t[r].lastconnect&&(n=!0)}return e},_.prototype.handleConnectAddrInfo=function(e,t){for(var n=this,r=e.split(/[,;]/),s=0;s<r.length;s++){var a="",i=0,_=r[s].split(":");if(_.length>1?(a=_[0],i=_[1]):(a=r[s],i=t),n.socket_instance)return;var R=a+":"+i,c="",E={},d=n.getConnectUuidByAddr(R);""==d?(c=o(),(E={}).uuid=c,E.origin=R,E.state=!1,E.socket=null,E.lastconnect=!1,n.websocket_info[c]=E):c=d,n.connect(R,c)}},_.prototype.connect=function(e,t){var n,o=this;if(t==o.getConnectUuidByState()||o.checkConnectUuid(t)&&!o.is_socket_active){var s="ws://"+e;if("WebSocket"in window)n=new WebSocket(s,"anychat-protocol");else{if(!("MozWebSocket"in window))return void o.emit("log","do not support the webrtc");n=new MozWebSocket(s,"anychat-protocol")}o.connect_cur_num++,o.setLastConnectFlag(t),n.onopen=function(){var e;o.websocket_info[t]&&(o.websocket_info[t].socket=n,!o.handleConnectInfo(t))&&(o.is_socket_active=!0,o.websocket_id=t,o.socket_instance=n,e={eventname:"request",sessionid:o.sessionid,ssrc:0,data:{sdkfunction:o.sdkFunction,cmdcode:"connect",ip:o.lpHost,port:o.dwPort,version:AnyChatSDKVersion,runmode:1}},n.send(JSON.stringify(e)))},n.onmessage=function(e){var t=JSON.parse(e.data),s=t.data;"object"!==(0,r.default)(s)&&(t.data=JSON.parse(s)),t.eventname?o.emit(t.eventname,t):o.emit("socket_receive_message",n,t)},n.onerror=function(n){o.emit("socket_error",t,e,n)},n.onclose=function(e){o.logout_flag||(o.emit("log","Event websocket onclose: conneciton closed!"),o.connect_cur_num--,o.websocket_info[t]&&(o.websocket_info[t].state=!1,o.websocket_info[t].socket=null),o.is_socket_active=!1,o.socket_instance=null,o.inLinkingFlag=!1,o.websocket_info[t]&&o.websocket_info[t].origin&&o.emit("close_websocket",e,t,o.websocket_info[t].origin))}}},_.prototype.socket_send=function(e,t,n,r){var o={};o.eventname=e,o.ssrc=0,r.userid&&(r.userid=parseInt(r.userid)),o.data={},o.data=r,o.data.cmdcode=n,JSON.stringify(o).length>this.socketSendMaxLen?this.emit("log","socket send data too long! eventnamem:"+e+", cmdcode:"+n+", maxLen:"+this.socketSendMaxLen):(o=a.AddDataPack(o),this.getConnectState()&&this.socket_instance&&1==this.socket_instance.readyState&&this.socket_instance.send(JSON.stringify(o)))},_.prototype.support=function(){navigator.userAgent;var e=0;return"WebSocket"in window||"MozWebSocket"in window||(e+=1),document.createElement("canvas").getContext||(e+=2),e},_.prototype.addUserControlInfo=function(e){var t=this;if(t.userid==e&&(e=-1),!t.userControlInfo[e]){var n={};n.userid=e,n.stream=null,n.mediarecorder=null,n.data=[],n.recordinfo=[],n.transcontentindex=0,n.transcontent="",t.userControlInfo[e]=n}},_.prototype.deleteUserControlInfo=function(e){var t=this;t.userid==e&&(e=-1),t.userControlInfo[e]&&(t.userControlInfo[e].mediarecorder&&"recording"===t.userControlInfo[e].mediarecorder.state&&t.userControlInfo[e].mediarecorder.stop(),t.userControlInfo[e].mediarecorder=null,t.userControlInfo[e].data=[],t.userControlInfo[e].recordinfo=[],delete t.userControlInfo[e])},_.prototype.cleanUserControlInfo=function(){var e=this;for(var t in e.userControlInfo)e.userControlInfo[t].mediarecorder&&"recording"===e.userControlInfo[t].mediarecorder.state&&e.userControlInfo[t].mediarecorder.stop(),e.userControlInfo[t].mediarecorder=null,e.userControlInfo[t].data=[],e.userControlInfo[t].recordinfo=[],e.userControlInfo[t].recordinfonum=0;e.userControlInfo={},e.addUserControlInfo(-1)},_.prototype.canvasVideo=function(e,t,n,r,o){var s=this,a={x:r.clientWidth,y:r.clientHeight};if(0==a.y&&(a.y=50),s.docid[n]){if(document.getElementById(n)){var i=document.getElementById(n);if(i){var _=i.getContext("2d");(c=new Image).src="data:image/jpg;base64,"+t,c.onload=function(){var t=A(c.width,c.height,a,0,e);0==e&&(t=A(c.width,c.height,a,0,e,!0)),i.setAttribute("width",a.x),i.setAttribute("height",a.y);var n=e+"_"+o;i.setAttribute("name",n),_.drawImage(c,t.sx,t.sy,t.sw,t.sh,t.dx,t.dy,t.dw,t.dh)}}}}else{s.docid[n]=!0;var R=document.createElement("div");R.style.width="100%",R.style.height=0==r.clientHeight?"50px":"100%",R.style.overflow="hidden",R.style.backgroundColor="#000000";var c,E=document.createElement("canvas");if(E.setAttribute("id",n),-1==e?(E.setAttribute("class","anychat-local-video"),R.setAttribute("class","anychat-local-video-box")):(E.setAttribute("class","anychat-remote-video"),R.setAttribute("class","anychat-remote-video-box")),0==e){var d=e+"_"+o;E.setAttribute("name",d)}E.innerHTML="浏览器不支持canvas标签",_=E.getContext("2d"),(c=new Image).src="data:image/jpg;base64,"+t,c.onload=function(){var t=A(c.width,c.height,a,0,e);0==e&&(t=A(c.width,c.height,a,0,e,!0)),E.setAttribute("width",a.x),E.setAttribute("height",a.y),_.drawImage(c,t.sx,t.sy,t.sw,t.sh,t.dx,t.dy,t.dw,t.dh),s.emit("log",n+" video parent node width:"+r.clientWidth+", height:"+r.clientHeight),r.appendChild(R),R.appendChild(E),document.getElementById(n)&&document.getElementById(n).addEventListener("dblclick",(function(){-1==e&&(e=s.userid),s.dblclickfullscreen&&s.emit("dblclick",n,e,o)})),s.emit("log","attachStream: width:"+c.width+", height:"+c.height),s.emit("log","create video (video id = "+n+") successfully")}}function A(e,t,n,r,o,a){if(!e||!t||!n.x||!n.y||a)return{sx:0,sy:0,sw:e,sh:t,dx:0,dy:0,dw:n.x,dh:n.y};var i={sx:0,sy:0,sw:e,sh:t,dx:0,dy:0,dw:n.x,dh:n.y};switch(n.x,n.y,-1==o?s.localVideoClipMode:s.remoteVideoClipMode){case 0:i=t/e>=n.y/n.x?{sx:0,sy:(t-e/n.x*n.y)/2,sw:e,sh:e/n.x*n.y,dx:0,dy:0,dw:n.x,dh:n.y}:{sx:(e-t/n.y*n.x)/2,sy:0,sw:t/n.y*n.x,sh:t,dx:0,dy:0,dw:n.x,dh:n.y};break;case 1:i=e>=n.x?t>=n.y?{sx:(e-n.x)/2,sy:(t-n.y)/2,sw:n.x,sh:n.y,dx:0,dy:0,dw:n.x,dh:n.y}:{sx:(e-n.x)/2,sy:0,sw:n.x,sh:t,dx:0,dy:(n.y-t)/2,dw:n.x,dh:t}:t>=n.y?{sx:0,sy:(t-n.y)/2,sw:e,sh:n.y,dx:(n.x-e)/2,dy:0,dw:e,dh:n.y}:{sx:0,sy:0,sw:e,sh:t,dx:(n.x-e)/2,dy:(n.y-t)/2,dw:e,dh:t};break;case 2:i=t/e>=n.y/n.x?{sx:0,sy:0,sw:e,sh:t,dx:(n.x-n.y/t*e)/2,dy:0,dw:n.y/t*e,dh:n.y}:{sx:0,sy:0,sw:e,sh:t,dx:0,dy:(n.y-n.x/e*t)/2,dw:n.x,dh:n.x/e*t};break;case 3:case 4:i={sx:0,sy:0,sw:e,sh:t,dx:0,dy:0,dw:n.x,dh:n.y}}return i}},_.prototype.removeAllVideo=function(){var e=document.querySelectorAll("div.anychat-local-video-box,div.anychat-remote-video-box");if(e)for(var t=0;t<e.length;t++){var n=e[t];n.parentNode.removeChild(n)}this.emit("log","remove all video table successfully!")},_.prototype.removeLocalVideo=function(e,t){var n=document.querySelectorAll("div.anychat-local-video-box");if(n){for(var r=0;r<n.length;r++)for(var o=n[r],s=o.children,a=0;a<s.length;a++)if(s[a].getAttribute("id")==e){delete this.docid[e],o.parentNode.removeChild(o);break}0!=t&&this.emit("log","remove local video table successfully! id:"+e)}},_.prototype.removeRemoteVideo=function(e,t){var n=document.querySelectorAll("div.anychat-remote-video-box");if(n){for(var r=0;r<n.length;r++)for(var o=n[r],s=o.children,a=0;a<s.length;a++)if(s[a].getAttribute("id")==e){delete this.docid[e],o.parentNode.removeChild(o);break}0!=t&&this.emit("log","remove remote video table successfully! id:"+e)}},_.prototype.handleFunctionFlag=function(e){var t=this;e&s.SDK_FUNCTIONS_H5NEWSESSIONKEEP||(t.new_keep_session_flag=!1),e&s.SDK_FUNCTIONS_H5NEWTRANSBUF||(t.new_transbuf_flag=!1),e&s.SDK_FUNCTIONS_LOGOPTIMIZE&&(t.logOptimizeFlags=!0)},t.instance=_},327:function(e){for(var t=[],n=0;n<256;++n)t[n]=(n+256).toString(16).substr(1);e.exports=function(e,n){var r=n||0,o=t;return o[e[r++]]+o[e[r++]]+o[e[r++]]+o[e[r++]]+"-"+o[e[r++]]+o[e[r++]]+"-"+o[e[r++]]+o[e[r++]]+"-"+o[e[r++]]+o[e[r++]]+"-"+o[e[r++]]+o[e[r++]]+o[e[r++]]+o[e[r++]]+o[e[r++]]+o[e[r++]]}},217:function(e){var t="undefined"!=typeof crypto&&crypto.getRandomValues.bind(crypto)||"undefined"!=typeof msCrypto&&msCrypto.getRandomValues.bind(msCrypto);if(t){var n=new Uint8Array(16);e.exports=function(){return t(n),n}}else{var r=new Array(16);e.exports=function(){for(var e,t=0;t<16;t++)0==(3&t)&&(e=4294967296*Math.random()),r[t]=e>>>((3&t)<<3)&255;return r}}},171:function(e,t,n){var r=n(217),o=n(327);e.exports=function(e,t,n){var s=t&&n||0;"string"==typeof e&&(t="binary"===e?new Array(16):null,e=null);var a=(e=e||{}).random||(e.rng||r)();if(a[6]=15&a[6]|64,a[8]=63&a[8]|128,t)for(var i=0;i<16;++i)t[s+i]=a[i];return t||o(a)}}},__webpack_module_cache__={};function __webpack_require__(e){var t=__webpack_module_cache__[e];if(void 0!==t)return t.exports;var n=__webpack_module_cache__[e]={exports:{}};return __webpack_modules__[e](n,n.exports,__webpack_require__),n.exports}__webpack_require__.g=function(){if("object"==typeof globalThis)return globalThis;try{return this||new Function("return this")()}catch(e){if("object"==typeof window)return window}}();var __webpack_exports__={};!function(){"use strict";window.AnyChatSDKVersion="V9.5.0.r112 buildtime: 2025/4/11 16:46:02 commit:6733fdf62dd15314f269f5a035af1a725438c2a4";var e=__webpack_require__(872);window.AnyChatSDK=e.instance}()})();