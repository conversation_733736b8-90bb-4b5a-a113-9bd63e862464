/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 2017/5/22.
 */
var AnyChatMediaUtils  =  (function(){
    var ANYCHAT_SDKCTRL_PPTHELPERINIT   =   90; ///< PPT播报环境初始化
    var ANYCHAT_SDKCTRL_PPTFILECTRL     =   91; ///< PPT文件控制
    var ANYCHAT_SDKCTRL_PPTFILEINFO     =   92; ///< PPT文件信息

    var BRPPT_FILETYPE_PPT = 0x01;      ///< ppt文件
    var BRPPT_FILETYPE_VIDEO = 0x02;        ///< 视频文件
    var BRPPT_FILETYPE_AUDIO = 0x03;        ///< 音频文件
    var BRPPT_FILETYPE_PPTZIP = 0x04;       ///< ppt文件夹

    var option = {};
    var fuc = {};
    var newMediaPlayerList = [];               // 判断fileid 是否已经存在实例

    /**
     * type : 1:ppt ; 2:mp4 ; 3:mp3;
     */

    /**
     * [ {
          fileid:'20170522',
          guid:'201452-56544656-defeafew',
          type:1,
          data:{

                }
           },

        {
         fileid:'20170522',
         guid:'201452-56544656-defeafew',
         type:2,
         data:{

         }
     }]
     * @mediaDataInfo {Array}
     */

    var mediaDataInfo = [];

    var currentTask = []; //下载列表

    /**
     * {
     *  savePath:'d://video';保存路径
     *  downloadcallback:      ;下载完回调法方法
     *  playendcallback:       ;播放完成方法名
     * }
     * @param option_json_var
     */

    if(typeof window.AnyChatWebSDK.anychatSDKInstance === 'undefined'){
      throw Error('please add anychatsdk.js before this file.');
    }

    function init(option_json_var){
        option = option_json_var;
        //ppt 压缩包保存地址

    }





    /**
     * 开始下载ppt
     */

    function download(fileid,fileurl,filetype,filemd5){
        if(filetype == 4){
            filetype = 1;
        }
        var task = window.AnyChatWebSDK.anychatSDKInstance.createFileDownloadTask({
            savepath: option.savepath, //下载保存路径
            fileid:fileid,
            fileurl: fileurl, //文件下载地址
            filetype: filetype, //文件类型
            filemd5:filemd5,
    
            intervalTime: 1, //返回文件状态时间间隔，单位为秒
            done: option.downloadcallback
            //onDownloadTaskStatusChanged: onDownloadTaskStatusChanged
        });
        currentTask.push(task);
        var errorcode = startDownload(fileid);
        if(errorcode==0){
            var obj = {};
            obj.fileid = fileid;
            obj.type = filetype;
            mediaDataInfo.push(obj);
        }
        
        var obj ={
            errorcode:errorcode,
            fileid :fileid
        }
        return obj;
    }
    
        //开始下载
    function startDownload(guid) {
        for (var i in currentTask) {
            if (currentTask[i].objParam.fileid == guid) {
                var errorcode = currentTask[i].start();
                return errorcode;
            }
        }
    }

    /**
     * 获取ppt 下载进度
     * 返回 {"errorcode":0, "fileid":"xxxxxx", "filetotalsize":10000, "downloadsize":5000, "progress":20}
     */
    function queryDownLoadStatus(fileid){
        var result = {};
        for (var i in currentTask) {
            if (currentTask[i].objParam.fileid == fileid) {
                result = currentTask[i].getStatus();
            }
        }
        return result;
    }

    /**
     * 取消ppt 下载
     * */
    function cancelDownload(fileid){
        for (var i in currentTask) {
            if (currentTask[i].objParam.fileid == fileid) {
                currentTask[i].cancel();
            }
        }
    }


    //构造函数 首字母大写方便区分函数
    /**
     * 媒体播放类
     * @param fileid :文件id ; viewid : 播放窗口dom元素的id
     * {
     *  fileid:'20170518',
     *  viewid:'pptpage',
     * }
     */
    function Player(param){

        var self = this;
        self.isPlay_once = false;               // 判断是否点击播放后的第一秒
        var mediaPlayerObj;
        
        /**
         * 根据参数值获取相应的fileid
         */
        if(param.fileid){
            var isNew = get_is_newMedia(param.fileid);
            self.fileid = param.fileid;
        }else if(param.filepath){
            var currentObj = init_filepath(param.filepath);
            var isNew = get_is_newMedia(currentObj.fileid);
            self.fileid = currentObj.fileid;
        }

        

        //self.mediaInfo =get_media_info( self.fileid);
        if(param.hasOwnProperty("mediaInfo")){
            self.mediaInfo = param.mediaInfo;
        }else{
            self.mediaInfo = get_media_info( self.fileid);
        }  

        if(param.hasOwnProperty("fileType")){
            self.mediaType = param.fileType;
        }else{
            self.mediaType = self.mediaInfo.type;
        }   
        
        //self.mediaType  = self.mediaInfo.type;                           //当前播放媒体类型

        self.viewid = param.viewid;
        self.$mediaView = document.getElementById(self.viewid);                              //当前播放窗口
        self.$pptpage = "";                                               //当前播放ppt图片路径
        self.currentPage = 1;                                             //当前页
        self.oldpage = 0;
        self.timer = null;                                                        //计时器
        self.option = param;
        self.streamindex = 0;                                              //流在第几个位置
        var pptallpage = 0;                                           // ppt总页码
        self.mediaDuration = 0;                                     // 当前媒体时长
        if(param.streamindex){
            self.streamindex = param.streamindex;
        }
        
        if(isNew && isNew.guid){
            self.guid = isNew.guid;
        }else{
            self.guid = window.AnyChatWebSDK.anychatSDKInstance.createGuid();
        }
        
        // if(!isNew.guid){
            if(self.mediaType == BRPPT_FILETYPE_PPT){
                initMedia(self.mediaInfo.details.audio_address,self.mediaType);
            }else if(self.mediaType == BRPPT_FILETYPE_VIDEO || self.mediaType == BRPPT_FILETYPE_AUDIO){
                initMedia(self.mediaInfo.filepath,self.mediaType);
            }else if(self.mediaType == BRPPT_FILETYPE_PPTZIP){
	    	 initMedia(self.mediaInfo.filepath,self.mediaType);
	    // }
        }
        
        
        /**
         * 根据文件路径创建媒体对象信息
         * @param url
         */
        function init_filepath(url){
            var ext = url.substring(url.lastIndexOf('.') + 1, url.length);
            if(/mp4/i.test(ext) ){
               var filetype = BRPPT_FILETYPE_VIDEO;
            }else if(/mp3/i.test(ext) ){
               var  filetype = BRPPT_FILETYPE_AUDIO;
            }
            var obj = {};
            obj.fileid = window.AnyChatWebSDK.anychatSDKInstance.createGuid();
            obj.type = filetype;
            obj.details = {} ;
            obj.errorcode = 0;
            obj.filepath = url ;
            mediaDataInfo.push(obj);
            return obj;
        }

        /**
         * 获取媒体对象详情
         * @param fileid
         */
        function get_media_info(fileid){
            var obj = {};
            for(var i in mediaDataInfo){
                if(fileid == mediaDataInfo[i].fileid){
                    obj = mediaDataInfo[i];
                    break;
                }
            }
            return obj;
        }


        /**
         *
         */
        function get_is_newMedia(fileid){
            var obj = {};
            for(var i in newMediaPlayerList){
                if(fileid == newMediaPlayerList[i].fileid){
                    obj = newMediaPlayerList[i];
                    break;
                }
            }
            return obj;
        }

        /**
         * 媒体初始化
         * @param url
         * @param type
         */
        function initMedia(url,type){
            var dwFlags;
            var strJson = '';
            // type 
            // 1 音频  2 视频  3 音视频  4 ppt(压缩包|文件夹)
            switch(type){
                case BRPPT_FILETYPE_PPT:
                    dwFlags = 4;
                    pptallpage = self.mediaInfo.details.pptlist.length;  // 获取PPT总页码
                    break;
                case BRPPT_FILETYPE_VIDEO:
                    dwFlags = 3;
                    strJson = '{"streamindex":'+self.streamindex+'}';
                    break;
                case BRPPT_FILETYPE_AUDIO:
                    dwFlags = 1;
                    break;
                case BRPPT_FILETYPE_PPTZIP:
                    dwFlags = 4;
                    pptallpage = self.mediaInfo.details.pptlist.length;  // 获取PPT总页码
                    break;
            }
            
            var obj = {
                guid:self.guid,
                streamType:dwFlags,
                streamPath:url,
                strParam:strJson,
                streamindex:self.streamindex,
                divId:self.viewid
            };
            mediaPlayerObj =  window.AnyChatWebSDK.anychatSDKInstance.createStreamPlay(obj);

            var audiodura = JSON.parse(mediaPlayerObj.getInfo()).audioduration / 1000;
            var videodura = JSON.parse(mediaPlayerObj.getInfo()).videoduration / 1000;
            self.mediaDuration = videodura > audiodura ? videodura : audiodura;

            var obj = {};
            obj.fileid = self.fileid;
            obj.guid = self.guid;
            newMediaPlayerList.push(obj);
        }

        /**
         * 获取流媒体播放进度(第几秒)
         */
		 
		function queryInfoMedia(currentguid) {
			var jsonInfo = JSON.parse(mediaPlayerObj.getInfo());
            return jsonInfo.playtime / 1000;
        } 
		
		/**
         * 获取流媒体播放信息
         */
        function queryInfoMediaJson(currentguid) {
            var jsonInfo = JSON.parse(mediaPlayerObj.getInfo());
            return jsonInfo;
        }

        /**
         * 切换PPT 图片
         * @param page
         * @returns {boolean}
         */
         var SDKOption = {
            enableWebService: 1, //启动本地Web服务
        };

        window.AnyChatWebSDK.anychatSDKInstance.setSDKOption(SDKOption);

        function switch_page(page){
            if(page == self.oldpage && self.isPlay_once == false) return false;
	    if(self.mediaType == BRPPT_FILETYPE_PPT){
		    var ppt_address =  self.mediaInfo.details.pptlist[page-1].ppt_address;
	            video_images(ppt_address);
	            self.$mediaView.innerHTML='<img src=""  style="width: 100%">';
	            self.$pptpage = self.$mediaView.getElementsByTagName('img');
	            self.$pptpage[0].src = window.AnyChatWebSDK.anychatSDKInstance.turnPathToUrl({
	                path: ppt_address
	            });
	    }
            self.oldpage = page;
            if(typeof(self.option.turnpagecallback) == 'function'){
                var json = {};
                json.currentpage = page ;
                json.allpage = pptallpage;
                self.option.turnpagecallback(json);
            }
            self.isPlay_once = false;
        }

        /**
         * 录入图片
         * @param filename
         */
        function video_images(filename){
            window.AnyChatWebSDK.anychatSDKInstance.insertFileDuringRecord({
                streamindex:self.streamindex,
                filename:filename
            });
            var json = {};

            /**
             * 底层传输
             * @type {number}
             */
            json.userid =  -1;
            json.command="control_ppt_page";
            json.data  = {"page_num":self.currentPage};
            BRAC_SDKControl(95,JSON.stringify(json));
        }

        /**
         *自动切换PPT 图片
         */
        function swtich_play_page(){
            for(var k in self.mediaInfo.details.pptlist){
                var audio_end = self.mediaInfo.details.pptlist[k].audio_end;
                var audio_start = self.mediaInfo.details.pptlist[k].audio_start;
                var json = queryInfoMediaJson(self.guid);
                var playtime = json.playtime / 1000;
                if(audio_start <= playtime && audio_end > playtime){
                    self.currentPage = k - 0 + 1;
                }
            }
            if(playtime==0 || (json && json.playstatus==3)){
                if(typeof(self.option.playendcallback) == 'function'){
                    var json = {};
                    json.errorcode = 0;
                    json.fileid = self.fileid;
                    self.option.playendcallback(json);
                }
                self.stop(self.fileid);
            }
        }

        /**
         * 手动切换ppt页面
         */
        function switch_ppt_ex(){
            if(self.currentPage - 1 == 0){
					mediaPlayerObj.stop();
            }else{
                mediaPlayerObj.seek({
                    strParam:'',
                    seekPos:self.mediaInfo.details.pptlist[(self.currentPage-1)].audio_start
                });
            }
        }


        


        function videoTime(){
            if(self.timer) clearInterval(self.timer);
            self.timer = setInterval(function(){
                var json = queryInfoMediaJson(self.guid);
                var playtime = json.playtime / 1000;
                if(playtime == 0 || json.playstatus == 3){
                    if(typeof(self.option.playendcallback) == 'function'){
                        var json = {};
                        json.errorcode = 0;
                        json.fileid = self.fileid;
                        self.option.playendcallback(json);
                    }
                    self.stop(self.fileid);
                }
            },1000);
        }

        /**
         * 播放
         */
        self.play = function(){
            // document.getElementById(self.viewid).innerHTML = '';

            if(self.mediaType ==  BRPPT_FILETYPE_PPTZIP){
              mediaPlayerObj.setPos();
            }
            if(self.mediaType ==  BRPPT_FILETYPE_PPT || self.mediaType ==  BRPPT_FILETYPE_PPTZIP ){
                 /**
                 * ppt 播放
                 */
                self.isPlay_once = true;
                switch_page(self.currentPage);
                if(self.timer){
                    clearInterval(self.timer);
                    self.timer = 1;
                }
                self.timer = setInterval(function(){
                    swtich_play_page();
                    switch_page(self.currentPage);
                },1000);
            }else if(self.mediaType ==  BRPPT_FILETYPE_VIDEO){
                /**
                 * mp4 播放
                 */
                videoTime();

                mediaPlayerObj.setPos();
            }else if(self.mediaType == BRPPT_FILETYPE_AUDIO){
                /**
                 * mp3 播放
                 */
                videoTime();
            }

            return mediaPlayerObj.play();
        };

        /**
         * 获取PPT的页码
         */
        self.pptdetail = function(){
            var json = {};
            if(self.mediaType == BRPPT_FILETYPE_PPT || self.mediaType == BRPPT_FILETYPE_PPTZIP){
                json.currentpage = self.currentPage ;
                json.allpage = pptallpage;
            }else{
                json.currentpage = 0 ;
                json.allpage = 0;
            }
            return json
        }

        /**
         * 暂停
         */
        self.pause = function(){
            if(self.timer){
                self.timer = clearInterval(self.timer);
            }
            return mediaPlayerObj.pause();
        };

        /**
         * 停止
         */
        self.stop = function(){

            self.timer = clearInterval(self.timer);
            self.timer=null;

            return mediaPlayerObj.stop();
        };

        /**
         * 拖动进度条
         */
        self.dragProcess = function(dragTime){
            if(self.timer){
                self.timer = clearInterval(self.timer);
            }
             mediaPlayerObj.seek({
                strParam:'',
                seekPos:dragTime
                });
        };


        /**
         * 控制ppt翻页
         * @param is   true 上一页 false 下一页
         */
        self.PPTControl = function(is){
            if(is){
                if(self.currentPage>1) {
                    self.currentPage--;
                    switch_page(self.currentPage);
                    switch_ppt_ex();
                }else{
                    return false;
                }
            }else{
                var allpage =  self.mediaInfo.details.pptlist.length;
                if(self.currentPage<allpage){
                    self.currentPage++;
                    switch_page(self.currentPage);
		    if(self.mediaType == BRPPT_FILETYPE_PPT){
		       self.pause();
		    }
                    switch_ppt_ex();
                }else{
                    return false;
                }

            }
            return true;
        };

        /**
         * ppt 翻下一页
         */
        self.nextFrame = function(){
            self.PPTControl(false);
        };


        /**
         * ppt 翻上一页
         */
        self.previousFrame = function(){
            self.PPTControl(true);
        };




        /**
         * 获取播放状态
         * @returns {{}}
         */
        self.getPlayStatus = function(){
            if(!self.guid) return {};
            var jsonInfo = JSON.parse(mediaPlayerObj.getInfo());
            if (jsonInfo.audioduration) {
                self.mediaDuration = jsonInfo.audioduration / 1000;
            } else {
                self.mediaDuration = jsonInfo.videoduration / 1000;
            }
            return jsonInfo;
        };


        /**
         * 销毁播放guid
         */
        self.destroy = function(){
            for(var i in newMediaPlayerList){
                if(self.guid == newMediaPlayerList[i].guid){
                    newMediaPlayerList[i] = {};
                    break;
                }
            }
            return mediaPlayerObj.destory();
        }

        /**
         * 触发录入第一张
         */
        self.once_img = function(){
            if(self.mediaType == 1){
                var filename = self.mediaInfo.details.pptlist[self.currentPage-1].ppt_address;
                video_images(filename);
            }
        }
	
	 /**
         * 具体翻页
         */
        self.page_turning = function(pageNum){
            if(self.pptallpage<pageNum){
                return 1328001;
            }
            if(pageNum<1){
                return 1328002;
            }
            self.currentPage = pageNum;
            switch_page(self.currentPage);
            self.pause();
            switch_ppt_ex();
            return 0;
        }

    }

    //工厂模式
    fuc.downloadinit = init; //初始化
    fuc.download = download;//开始下载ppt
    fuc.queryDownLoadStatus = queryDownLoadStatus;//获取ppt 下载进度
    //fuc.downLoadCallBack = downLoadCallBack;//下载完成回调函数
    fuc.cancelDownload = cancelDownload; //取消下载
    fuc.Player = Player;  //播放器
    return fuc;
})();
