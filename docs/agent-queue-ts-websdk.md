# agent-queue-ts-websdk

座席端websdk（ts版本）

## 注意事项

引导到vite工程，需要在vite.config.js中增加如下配置
```
    optimizeDeps: {
      esbuildOptions: {
        define: {
          global: 'globalThis',
        },
      },
    },
```

## 示例代码

```typescript
import {ApexQueueCowork, Constants} from 'agent-queue-ts-websdk';

// ---------- 坐席签入（坐席登录）实例代码begin ---------- //
const clientConfig = {
    // 旧排队服务的ws地址，公司环境地址为ws://*************:7070/ws/
    wsURL: url.value,
    // 坐席账号
    userId: 'admin',
    // 坐席密码
    password: '123456',
};

// 初始化
ApexQueueCowork.init(clientConfig);

// 登录、建立websocket连接
ApexQueueCowork.login({
    // 建立连接超时时间（单位 s），默认值30s
    timeout: 30,
    // websocket连接成功后，向排队服务器发送心跳包的频率（默认每30s发送一次）
    interval: 30,
    // 取出视频见证过程的相关数据
    spjzHandler: (data) => {
        // data: {'nState':'', 'nSubCode':'', 'sParam1':'', 'sParam2':''}

        if (data && data.nState) {
            let str = '';
            let nState = data.nState;
            let nSubCode = data.nSubCode;

            switch (nState) {
                case Constants.STATE_LOGINFAIL:
                    str = '登录失败：' + nSubCode + ':' + data.sParam1;
                    alert(str);
                    break;
                case Constants.STATE_LOGINOK:
                    str = '登录成功';
                    break;
                case Constants.STATE_NOTIFY_QUEUE:
                    str = '收到服务队列信息';
                    agentStatus.value = 1;
                    // 当前队列人数
                    requestingCustomerCount.value = (nSubCode as Record<string, any>).count;
                    break;
                case Constants.STATE_RECIEVE_CUSTOMER_REQUESTING:
                    str = '收到客户排队请求';
                    // 当前客户排队请求详情
                    currentRequestingCustomer.value = nSubCode;
                    // _this.requestingCustomer.set(nSubCode.id, nSubCode);
                    // _this.requestingCustomer = new Map(_this.requestingCustomer);
                    break;
                case Constants.STATE_RECIEVE_CUSTOMER_CANCELQUEUE:
                    str = '收到客户取消排队请求';
                    currentRequestingCustomer.value = {};
                    // _this.requestingCustomer.delete(nSubCode);
                    // _this.requestingCustomer = new Map(_this.requestingCustomer);
                    break;
                case Constants.STATE_NO_NOTIFY_QUEUE:
                    // 该坐席没有可服务的队列
                    str += '该坐席没有可服务的队列';
                    alert(str);
                    agentStatus.value = 0;
                    break;
                case Constants.STATE_OFFLINE:
                    str += '连接关闭';
                    agentStatus.value = 0;
                    // 此时坐席需要登出视频服务
                    // _this.hideVideo();
                    break;
                case Constants.STATE_LOGIN_ON_OTHER:
                    str += '该坐席已在其他地方登录';
                    // 此时坐席需要登出视频服务
                    alert(str);
                    break;
                default:
                    // 此时坐席需要登出视频服务
                    str += '未知错误，' + nState + '，' + nSubCode;
                // 此时断开视频
                // _this.hideVideo();
            }

            // log(str, data);
            addLog(str, data);
        }
    },
    error: function (error) {
        if (console) {
            console.info('login error: ' + error);
        }
    }
})
// ---------- 坐席签入（坐席登录）实例代码end ---------- //

// 坐席登录成功后，切换坐席状态的方法----切换到“示闲”状态
ApexQueueCowork.agentStatus();
// 坐席登录成功后，切换坐席状态的方法----切换到“繁忙”状态
ApexQueueCowork.agentStatusDnd();
// 坐席登录成功后，切换坐席状态的方法----切换到“学习”状态
ApexQueueCowork.agentStatusAway();

// 坐席拒绝客户的排队请求
ApexQueueCowork.offerReject();
// 坐席接受客户的排队请求，此时坐席需要登录视频服务（anychat、腾讯云等）
ApexQueueCowork.offerAccept();
// 坐席接受请求后，见证结束时，调用的见证结束接口，此时坐席需要登出视频服务（anychat、腾讯云等）
// param: true, false
// reason: 见证的详细结果
ApexQueueCowork.videoResult(param, reason);
// 坐席登出
ApexQueueCowork.disconnect();
```
