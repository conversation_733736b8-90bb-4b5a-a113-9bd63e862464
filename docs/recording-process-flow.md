# 录制流程文档

本文档描述了见证视频录制的完整流程，从开始录制（handleStartRecord）到人脸检测和数据处理的全过程。

## 录制流程图

```mermaid
flowchart TD
    %% 主要流程节点
    Start([开始]) --> handleStartRecord["handleStartRecord()\n开始录制"]
    
    %% 录制初始化流程
    handleStartRecord --> CheckEnv{环境检查}
    CheckEnv -->|生产环境| CallStartRecord["UserAPI.startRecord()\n开始录制API"]
    CheckEnv -->|开发环境| SkipAPI["跳过API调用"]
    
    CallStartRecord --> SaveTaskId["保存taskId"]
    SkipAPI --> InitRecord["初始化录制状态"]
    SaveTaskId --> InitRecord
    
    InitRecord --> startTimer["开始计时器"]
    InitRecord --> ClearHistory["清空检测记录\nstore.$patch({detectionHistory: []})"]
    
    %% WebSocket通知流程
    InitRecord --> socketRecordStatusSync["socketRecordStatusSync()\n通知开始录制"]
    socketRecordStatusSync --> WSPublish["WebSocket发送START_RECORD状态\n通知客户端开始录制"]
    
    %% 人脸检测初始化流程
    InitRecord --> startDetectFace["startDetectFace()\n开始人脸检测"]
    startDetectFace --> CreateVideo["创建video元素"]
    CreateVideo --> GetVideoTrack["获取视频轨道\ntrtc.getVideoTrack()"]
    GetVideoTrack --> SetupMediaStream["设置MediaStream"]
    SetupMediaStream --> PlayVideo["播放视频"]
    
    %% 人脸检测启动
    PlayVideo --> detectFaces["detectFaces()\n初始化人脸检测"]
    PlayVideo --> startCapture["startCapture()\n开始视频捕获"]
    
    %% 捕获与检测处理
    startCapture --> InitWorker["初始化Worker\ninitializeWorker()"]
    InitWorker --> SetInterval["设置捕获间隔\ncaptureInterval"]
    SetInterval --> CaptureFrame["捕获视频帧\nrequestAnimationFrame()"]
    CaptureFrame --> SendToWorker["发送帧到Worker\npostMessage()"]
    
    %% WebSocket处理人脸检测
    SendToWorker --> WSPublishFrame["WebSocket发送帧\nWebSocketSingleton.publish()"]
    WSPublishFrame --> WSProcessResult["WebSocket接收结果\n/facedetection_result"]
    
    %% 处理检测结果
    WSProcessResult --> ProcessDetectionResponse["处理检测响应\nprocessDetectionResponse()"]
    ProcessDetectionResponse --> UpdateResult["更新检测结果\ndetectionResult.value"]
    UpdateResult --> GenDetail["生成详细信息\ngenDetail()"]
    GenDetail --> UpdateHistory["更新历史记录\nupdateHistory()"]
    
    %% 通知坐席端
    UpdateHistory --> NotifyClient["通知坐席端\nWebSocket发送检测结果"]
    
    %% 结束与清理
    StopRecord["handleStopRecord()\n结束录制"] --> EmitStopRecord["触发stopRecord事件"]
    EmitStopRecord --> SocketStopRecord["WebSocket通知结束录制\nWS:STOP_RECORD"]
    SocketStopRecord --> CallStopRecord["UserAPI.stopRecord(taskId)\n停止录制API"]
    CallStopRecord --> ClearTimer["清除计时器"]
    ClearTimer --> UpdateStatus["更新状态\nStatus.RecordEnd"]
    UpdateStatus --> ClearResources["清理资源\nclearupFaceDetection()"]
    ClearResources --> SaveData["保存数据\nClientApi.modifyWitness()"]
    SaveData --> ShowAlert["显示完成提示\nElMessageBox.alert()"]
    
    %% 返回清理函数
    startDetectFace --> ReturnCleanup["返回清理函数\n()=>{\n  停止视频\n  清理资源\n}"]
    
    %% 子流程：人脸检测结果处理
    subgraph DetectionProcess["人脸检测结果处理"]
        hasFaceQualityIssues["检查人脸质量问题\nhasFaceQualityIssues()"]
        hasLowCompareScore["检查人脸对比分数\nhasLowCompareScore()"]
        hasResultChanged["检查结果变化\nhasResultChanged()"]
        
        hasFaceQualityIssues --> shouldUpdate{是否需要更新?}
        hasLowCompareScore --> shouldUpdate
        hasResultChanged --> shouldUpdate
        
        shouldUpdate -->|是| PushHistory["推送到历史记录"]
        shouldUpdate -->|否| Skip["跳过更新"]
    end
    
    UpdateResult --> DetectionProcess
```

## 关键函数说明

### 1. handleStartRecord
位于 `user-camera.vue`，负责初始化录制过程。根据环境调用录制API，并同步录制状态到客户端，同时启动人脸检测。

### 2. startDetectFace
位于 `user-camera.vue`，负责设置视频流和初始化人脸检测。获取视频轨道，创建视频元素，并启动人脸检测和视频捕获。

### 3. startCapture
位于 `useFaceDetection.ts`，负责视频帧捕获。初始化工作线程(Worker)，设置捕获间隔，并通过WebSocket发送视频帧到服务器进行人脸检测。

### 4. processDetectionResponse
位于 `useFaceDetection.ts`，负责处理从服务器返回的人脸检测结果。更新检测结果，生成详细信息，并更新检测历史记录。

### 5. updateHistory
位于 `useFaceDetection.ts`，负责更新人脸检测历史记录，并通过WebSocket发送检测结果到坐席端。

### 6. handleStopRecord
位于 `user-camera.vue`，负责结束录制过程。通知客户端结束录制，调用停止录制API，清理资源，并保存数据。

## 数据流

1. 视频数据从TRTC SDK获取
2. 通过WebSocket发送到服务器进行人脸检测
3. 检测结果通过WebSocket返回
4. 结果保存到历史记录并通知坐席端

## 关键状态

- `record.value`: 录制状态标志
- `status.value`: 录制状态（Status.Recording, Status.RecordEnd等）
- `detectionResult.value`: 当前检测结果
- `detectionHistory.value`: 检测历史记录

这个流程图展示了从开始录制到结束录制的完整流程，包括人脸检测、状态同步和数据处理。
