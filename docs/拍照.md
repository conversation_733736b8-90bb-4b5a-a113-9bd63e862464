# 5.4. 录像开始获取比对源

## 描述

此接口发生在录像开始前。获取图像对比源的业务参数，可用于应用层进行人脸在框检测、人脸比对等业务相关操作。

## 示例代码

```javascript
var agentAppFrame = document.querySelector('#agentApp').contentWindow;
agentAppFrame.postMessage({
    cmd: 'CMDAnyChatSendUserPhoto',
    msg: {
        headShot: ''
    }
});
```

## 接口字段参数简介

| 名称   | 类型   | 说明           | 是否必须 | 默认值 |
|--------|--------|----------------|----------|--------|
| cmd    | String | 通讯指令       | 是       |        |
| msg    | String | headShot：图像对比源数据 | 是       |        |

---

# 5.5. 录像结束回调

## 回调描述

此回调接口发生在音视频通话结束后。回调带有客户端传入的业务参数，可用于通知上层应用传递录像回放地址等业务相关操作。

## 示例代码

```javascript
// 监听消息事件
window.addEventListener('message', function (event) {
    var cmd = event.data.cmd;
    var msg = event.data.msg;

    switch (cmd) {
        case 'CMDAnyChatAgentRecordDone': onRecordDone(msg);
    };
});

function onRecordDone(jsonString) {
    // TODO 解析回调的 json 字符串，获取业务数据
}
```

## 回调字段参数简介

| 名称   | 类型   | 说明           | 是否必须 | 默认值 |
|--------|--------|----------------|----------|--------|
| cmd    | String | 通讯指令       | 是       |        |
| msg    | String | 录像文件信息   | 是       |        |

---

# 5.6. 录像回放生成回调

## 接口描述

此回调接口发生在录像回放生成后。回调带有质检得分、标签数据的业务参数，可用于通知上层应用传递质检得分、标签数据等业务相关操作。

## 示例代码

```javascript
// 监听消息事件
window.addEventListener('message', function (event) {
    var cmd = event.data.cmd;
    var msg = event.data.msg;

    switch (cmd) {
        case 'CMDAnyChatQualityData':
            // 处理逻辑
            break;
    };
});
```

## 回调字段参数简介

| 名称   | 类型   | 说明           | 是否必须 | 默认值 |
|--------|--------|----------------|----------|--------|
| cmd    | String | 通讯指令       | 是       |        |
| msg    | String | 质检得分、标签数据 | 是       |        |

---

# 5.7. 发送录像文件地址接口

## 接口描述

此接口用于上层业务在录像结束回调后，解析并生成服务器录像文件访问地址，提供给坐席组件进行录像回放功能。此接口使用 `postMessage` 方法向坐席组件发送指令操作。

## 示例代码

```javascript
var agentAppFrame = document.querySelector('#agentApp').contentWindow;
agentAppFrame.postMessage({
    cmd: 'CMDAnyChatAgentRecordAddress',
    msg: 'http://xxx.xxxxx.com?tradeNo=xxx'
});
```

## 接口字段参数简介

| 名称   | 类型   | 说明           | 是否必须 | 默认值 |
|--------|--------|----------------|----------|--------|
| cmd    | String | 通讯指令       | 是       |        |
| msg    | String | 服务器端录像文件访问地址 | 是       |        |

# 5.8. 调用本地拍照接口

## 接口描述

此接口用于上层业务调用组件内部拍照功能。此接口使用 `postMessage` 方法向坐席组件发送指令操作。

## 示例代码

```javascript
var agentAppFrame = document.querySelector('#agentApp').contentWindow;
agentAppFrame.postMessage({
    cmd: 'CMDAnyChatBusinessGetUserPhoto'
});
```

## 接口字段参数简介

| 名称   | 类型   | 说明       | 是否必须 | 默认值 |
|--------|--------|------------|----------|--------|
| cmd    | String | 通讯指令   | 是       |        |

---

# 5.9. 本地拍照回调

## 回调描述

此回调接口用于音视频通话中，点击拍照按钮后，照片以 base64 的形式对外输出，可以用于页面上的照片展示。集成方需要监听 `message` 事件接收数据。

## 示例代码

```javascript
// 监听消息事件
window.addEventListener('message', function (event) {
    var cmd = event.data.cmd;
    var msg = event.data.msg;

    switch (cmd) {
        case 'CMDAnyChatReceiveSnapshotFromClient':
            // 处理逻辑
            break;
    }
});
```

## 回调字段参数简介

| 名称     | 类型   | 说明           | 是否必须 | 默认值 |
|----------|--------|----------------|----------|--------|
| cmd      | String | 通讯指令       | 是       |        |
| msg      | Object | 拍照图片对象信息 | 是       |        |

### ImageInfo

| 名称     | 类型   | 说明               | 是否必须 | 默认值 |
|----------|--------|--------------------|----------|--------|
| imgType  | Number | 拍照信息类型       | 是       |        |
|          |        | - 1 大头照         |          |        |
|          |        | - 2 手持证件照     |          |        |
| base64   | String | 照片的 base64       | 是       |        |
|          |        | （服务端拍照时为空）|          |        |
