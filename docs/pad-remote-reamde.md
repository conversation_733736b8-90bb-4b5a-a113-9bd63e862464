# 坐席端和客户端话术播报通信过程

## 概述

本文档梳理了远程见证系统中坐席端（user.vue）和客户端（client.vue）之间话术播报的通信过程。系统通过WebSocket实现实时通信，包括话术播报（TTS）和语音识别（ASR）两个主要功能模块。

## 通信流程

### 1. 话术播报（TTS）流程

坐席端（user.vue）通过点击话术项旁边的"开始播放"按钮，触发TTS播放流程。系统将话术内容转换为语音，并通过WebSocket发送到客户端（client.vue）进行播放，同时在客户端显示字幕。

### 2. 语音识别（ASR）流程

话术播放结束后，系统会等待客户回答。客户端（client.vue）会启动语音识别功能，捕获客户的语音回答，并将识别结果通过WebSocket发送回坐席端（user.vue）进行验证。

## 详细通信流程图

```mermaid
sequenceDiagram
    participant 坐席端 as 坐席端(user.vue)
    participant WebSocket as WebSocket服务
    participant 客户端 as 客户端(client.vue)

    %% 初始连接
    坐席端->>WebSocket: 建立WebSocket连接
    客户端->>WebSocket: 建立WebSocket连接

    %% TTS播放流程
    坐席端->>坐席端: 点击"开始播放"按钮
    坐席端->>坐席端: 调用playTTS(index)函数
    坐席端->>坐席端: 启动MarqueeText组件
    坐席端->>WebSocket: 发送TTS_SUB消息(包含话术文本和字幕)
    WebSocket->>客户端: 转发TTS_SUB消息
    客户端->>客户端: 接收消息并显示字幕(TtsSubtitle组件)
    客户端->>客户端: 播放TTS音频

    %% ASR识别流程
    客户端->>客户端: TTS播放结束
    坐席端->>WebSocket: 发送ASR_START消息(包含期望回答内容)
    WebSocket->>客户端: 转发ASR_START消息
    客户端->>客户端: 显示"请您回答"提示和期望回答内容
    客户端->>客户端: 启动语音识别
    客户端->>WebSocket: 发送语音识别结果
    WebSocket->>坐席端: 转发语音识别结果

    %% 验证结果
    坐席端->>坐席端: 验证客户回答是否符合预期

    alt 回答正确
        坐席端->>WebSocket: 发送ASR_SUCCESS消息
        WebSocket->>客户端: 转发ASR_SUCCESS消息
        坐席端->>坐席端: 继续播放下一条话术
    else 回答错误
        坐席端->>WebSocket: 发送ASR_FAIL消息
        WebSocket->>客户端: 转发ASR_FAIL消息
        客户端->>客户端: 显示"您的回答不准确"提示
        客户端->>客户端: 重新启动语音识别
    end
```

## 关键组件和文件

### 坐席端（user.vue）

1. **MarqueeText组件**：
   - 负责显示话术内容，并支持字幕同步高亮
   - 通过WebSocket将字幕信息发送给客户端

2. **playTTS函数**：
   - 控制话术播放的核心函数
   - 管理播放状态和字幕同步

3. **handleAudioEnded函数**：
   - 处理音频播放结束事件
   - 启动ASR语音识别流程

### 客户端（client.vue）

1. **TtsSubtitle组件**：
   - 负责在客户端显示从坐席端发送过来的字幕
   - 支持字幕滚动和高亮效果

2. **VoiceAssistantIcon组件**：
   - 提供语音识别的视觉反馈
   - 显示倒计时和录音状态

3. **setupWebSocket函数**：
   - 处理各类WebSocket消息
   - 根据消息类型执行相应的操作

## WebSocket消息类型

系统使用以下WebSocket消息类型进行通信：

1. **TTS_SUB**：发送TTS字幕信息
2. **ASR_START**：启动语音识别
3. **ASR_RESTART**：重新启动语音识别
4. **ASR_SUCCESS**：语音识别成功
5. **ASR_FAIL**：语音识别失败
6. **FACE_DETECTION_RESULT**：人脸检测结果

## 技术实现细节

1. **TTS音频处理**：
   - 使用AudioHandler类处理音频流
   - 支持音频分段处理和流式播放

2. **ASR语音识别**：
   - 使用腾讯云ASR服务进行语音识别
   - 支持热词识别和过滤

3. **事件总线**：
   - 使用mitt库实现组件间通信
   - 定义了TTS_SUBTITLES_UPDATE和FACE_DETECTION_UPDATE等事件类型

## 总结

坐席端和客户端之间的话术播报通信采用WebSocket实现实时双向通信，主要包括TTS话术播报和ASR语音识别两个核心功能。系统设计了完善的状态管理和错误处理机制，确保通信过程的稳定性和可靠性。